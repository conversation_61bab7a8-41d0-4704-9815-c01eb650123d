"""
API configurations including API keys and client instances.

⚠️  SECURITY WARNING: This file contains hardcoded API keys.
    - DO NOT commit this file to version control with real keys
    - DO NOT share this file or your codebase with real keys
    - Consider using environment variables instead for better security
"""

import os
from openai import OpenAI

# ============================================================================
# OPTION 1: HARDCODED API KEYS (NOT RECOMMENDED FOR PRODUCTION)
# ============================================================================
# Replace the placeholder values below with your actual API keys
# SECURITY RISK: These keys will be visible in your source code

# Set USE_HARDCODED_KEYS to True to use the hardcoded keys below
USE_HARDCODED_KEYS = True  # Change to True to enable hardcoded keys

# Hardcoded API Keys (replace with your actual keys)
HARDCODED_OPENAI_API_KEY = "***********************************************************************************************"
HARDCODED_DEEPSEEK_API_KEY = "***********************************"
HARDCODED_SERPER_API_KEY = "830c7dd98dc3debbfb1e9dfcabaaabf9ef3c1a01"
HARDCODED_GOOGLE_API_KEY = "AIzaSyDaUxfxIwpCHkrJfNCKytbySA5Ka4h3Rjg"
HARDCODED_GEMINI_API_KEY = "AIzaSyDaUxfxIwpCHkrJfNCKytbySA5Ka4h3Rjg"

# ============================================================================
# OPTION 2: ENVIRONMENT VARIABLES (RECOMMENDED)
# ============================================================================
# Load from environment variables (more secure)
ENV_OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
ENV_DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
ENV_SERPER_API_KEY = os.getenv("SERPER_API_KEY")
ENV_GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
ENV_GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

# ============================================================================
# FINAL API KEY SELECTION
# ============================================================================
# Choose between hardcoded keys or environment variables
if USE_HARDCODED_KEYS:
    print("⚠️  WARNING: Using hardcoded API keys from config/api.py")
    openai_api_key = HARDCODED_OPENAI_API_KEY
    deepseek_api_key = HARDCODED_DEEPSEEK_API_KEY
    serper_api_key = HARDCODED_SERPER_API_KEY
    google_api_key = HARDCODED_GOOGLE_API_KEY
    gemini_api_key = HARDCODED_GEMINI_API_KEY
else:
    print("✅ Using API keys from environment variables")
    openai_api_key = ENV_OPENAI_API_KEY
    deepseek_api_key = ENV_DEEPSEEK_API_KEY
    serper_api_key = ENV_SERPER_API_KEY
    google_api_key = ENV_GOOGLE_API_KEY
    gemini_api_key = ENV_GEMINI_API_KEY



# Client instances - lazy initialization to avoid errors when API keys are not set
def _get_openai_client():
    """Lazy initialization of OpenAI client"""
    if openai_api_key:
        return OpenAI(api_key=openai_api_key)
    else:
        # Return None if no API key is set, or create a dummy client for testing
        return None

# Initialize client if API key is available
openai_client = _get_openai_client()

# Token validation
VALIDATE_TOKEN = False  # Flag to enable/disable token validation (set to False for testing environments)

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def print_api_keys_status():
    """
    Print the status and values of all API keys.
    Shows which source is being used (hardcoded vs environment).
    """
    print("=" * 60)
    print("API KEYS STATUS")
    print("=" * 60)
    print(f"Source: {'HARDCODED' if USE_HARDCODED_KEYS else 'ENVIRONMENT VARIABLES'}")
    print("-" * 60)

    keys_info = [
        ("OpenAI API Key", openai_api_key),
        ("DeepSeek API Key", deepseek_api_key),
        ("Serper API Key", serper_api_key),
        ("Google API Key", google_api_key),
    ]

    for name, key in keys_info:
        status = "✅ SET" if key else "❌ NOT SET"
        value_display = key if key else "NOT SET"
        print(f"{name:20} | {status:10} | {value_display}")

    print("=" * 60)

def print_api_keys_masked():
    """
    Print API keys with masking for security.
    Shows only first 4 and last 4 characters.
    """
    def mask_key(key):
        if not key or len(key) < 8:
            return "NOT SET" if not key else "*" * len(key)
        return f"{key[:4]}{'*' * (len(key) - 8)}{key[-4:]}"

    print("=" * 60)
    print("API KEYS STATUS (MASKED)")
    print("=" * 60)
    print(f"Source: {'HARDCODED' if USE_HARDCODED_KEYS else 'ENVIRONMENT VARIABLES'}")
    print("-" * 60)

    keys_info = [
        ("OpenAI API Key", openai_api_key),
        ("DeepSeek API Key", deepseek_api_key),
        ("Serper API Key", serper_api_key),
        ("Google API Key", google_api_key),
    ]

    for name, key in keys_info:
        status = "✅ SET" if key else "❌ NOT SET"
        masked_value = mask_key(key)
        print(f"{name:20} | {status:10} | {masked_value}")

    print("=" * 60)

def get_api_keys_dict():
    """
    Return a dictionary of all API keys.
    Useful for programmatic access.
    """
    return {
        "openai_api_key": openai_api_key,
        "deepseek_api_key": deepseek_api_key,
        "serper_api_key": serper_api_key,
        "google_api_key": google_api_key,
        "source": "hardcoded" if USE_HARDCODED_KEYS else "environment"
    }
