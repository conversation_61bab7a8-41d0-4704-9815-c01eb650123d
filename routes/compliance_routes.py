import logging
from fastapi import APIRouter, Request, HTTPException, Form, BackgroundTasks
from fastapi.responses import FileResponse
import os
import json as jp
from datetime import datetime, timezone
import config
import json
import uuid
import re
import aiofiles
from typing import List
from services.mongo_manager import MongoManager
from services import utils
from services.utils import decode_jwt_token, get_compliance_report
from services.unified_usage_tracking import create_unified_usage_logger
from services.prompts import get_compliance_prompt
from models import insight_model
from starlette.background import BackgroundTask
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from services.async_fs_utils import async_makedirs, async_remove, async_path_exists
from services.logging_utils import LoggingHelper
from exceptions.api_exceptions import ContextLengthExceededError

# Configure logging - will use project+user specific logging
logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize services
mongo_manager = MongoManager(config.insights_collection, config.images_collection, config.tables_collection, config.compliance_collection)

# Compliance routes will be moved here
@router.post(f"/{config.compliance_generation_url}/")
async def generate_compliance_report(
    request: Request,
    project_id: str = Form(...),
    user_id: str = Form(...),
    file_ids: str = Form(...),
    framework_type: str = Form(...),
    generate_pdf: bool = Form(default=False)
):
    # user_id and project_id are available from form data
    LoggingHelper.info("Compliance report generation started", user_id=user_id, project_id=project_id,
                      extra_data={"framework_type": framework_type, "generate_pdf": generate_pdf})

    if config.VALIDATE_TOKEN:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            LoggingHelper.error("Authorization header is missing or invalid", user_id=user_id, project_id=project_id)
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

        token = auth_header.split(' ')[1]
        payload = decode_jwt_token(token)
        if payload:
            user_email = payload['sub']
            is_valid_user = await mongo_manager.check_user_exists(user_email)
            if not is_valid_user:
                LoggingHelper.error("User not found", user_id=user_id, project_id=project_id,
                                   extra_data={"user_email": user_email})
                raise HTTPException(status_code=401, detail="Invalid user")
            else:
                LoggingHelper.info("Valid User authenticated", user_id=user_id, project_id=project_id,
                                  extra_data={"user_email": user_email})
    else:
        LoggingHelper.info("Token validation is turned OFF. Skipping authentication checks.",
                          user_id=user_id, project_id=project_id)





    # Initialize unified usage logger for compliance report generation
    usage_logger = create_unified_usage_logger(project_id=project_id, user_id=user_id, mongo_manager=mongo_manager)
    LoggingHelper.info("Unified usage tracking initialized for compliance report generation",
                      user_id=user_id, project_id=project_id,
                      extra_data={"usage_logger_query_id": usage_logger.query_id})
    usage_logger.log_step_summary("INITIALIZATION", "Unified usage logger created for compliance report generation")



    # Convert _id to project_id
    actual_project_id = await utils.get_project_id_from_object_id(project_id, mongo_manager)
    if not actual_project_id:
        LoggingHelper.error(f"Project with _id {project_id} not found", user_id=user_id, project_id=project_id,
                           extra_data={"object_id": project_id})
        raise HTTPException(status_code=404, detail="Project not found")

    LoggingHelper.info("File IDs received for processing", user_id=user_id, project_id=project_id,
                      extra_data={"file_ids_raw": file_ids, "generate_pdf": generate_pdf,
                                "file_ids_type": type(file_ids).__name__})

    file_ids = file_ids.replace("'", '"')

    # Parse file IDs from JSON string
    file_ids = jp.loads(file_ids)

    LoggingHelper.info("File IDs parsed successfully", user_id=user_id, project_id=project_id,
                      extra_data={"file_ids": file_ids, "file_ids_count": len(file_ids),
                                "parsed_type": type(file_ids).__name__})

    # Get project data from MongoDB
    project_data, exists, _ = await mongo_manager.get_or_initialize_project(actual_project_id)
    if not exists:
        LoggingHelper.error(f"Project {actual_project_id} not found", user_id=user_id, project_id=project_id,
                           extra_data={"actual_project_id": actual_project_id})
        raise HTTPException(status_code=404, detail="Project not found")

    # Verify user has access to this project
    if user_id != project_data.get("user_id", ""):
        LoggingHelper.error(f"User {user_id} not authorized for project {actual_project_id}",
                           user_id=user_id, project_id=project_id,
                           extra_data={"actual_project_id": actual_project_id,
                                     "project_user_id": project_data.get("user_id", "")})
        raise HTTPException(status_code=403, detail="User not authorized for this project")

    # Create mapping of file_id to file_name
    file_id_to_name = {
        file_detail["file_id"]: file_detail["file_name"]
        for file_detail in project_data.get("file_details", [])
        if "file_id" in file_detail
    }

    # Get file names for the provided file IDs
    file_names = []
    for file_id in file_ids:
        if file_id not in file_id_to_name:
            LoggingHelper.error(f"File ID {file_id} not found in project {actual_project_id}",
                               user_id=user_id, project_id=project_id,
                               extra_data={"file_id": file_id, "actual_project_id": actual_project_id,
                                         "available_file_ids": list(file_id_to_name.keys())})
            raise HTTPException(status_code=404, detail=f"File ID {file_id} not found in project")
        file_names.append(file_id_to_name[file_id])

    LoggingHelper.info("File names resolved from file IDs", user_id=user_id, project_id=project_id,
                      extra_data={"file_names": file_names, "file_count": len(file_names)})

    # Create compliance reports directory if it doesn't exist
    compliance_dir = os.path.join(config.file_path, actual_project_id, "compliance_reports")
    os.makedirs(compliance_dir, exist_ok=True)

    framework_type = framework_type.upper()

    if generate_pdf:
        LoggingHelper.info("Generating combined PDF compliance report", user_id=user_id, project_id=project_id,
                          extra_data={"framework_type": framework_type, "file_count": len(file_names)})
        pdf_path = None
        # Generate PDF file name
        current_time = datetime.now(timezone.utc)
        temp_filename = f"temp_{uuid.uuid4().hex}_{framework_type}_compliance.pdf"
        pdf_path = os.path.join(compliance_dir, temp_filename)

        LoggingHelper.info("PDF file path generated", user_id=user_id, project_id=project_id,
                          extra_data={"pdf_path": pdf_path, "temp_filename": temp_filename})

        # Process each file
        valid_reports = []  # Track valid reports
        missing_files = []  # Track missing files

        # First validate all files have reports
        for file_name in file_names:
            report_filename = f"{file_name.split('.')[0]}_{framework_type}_compliance.json"
            report_path = os.path.join(compliance_dir, report_filename)

            if not os.path.exists(report_path):
                missing_files.append(file_name)
                LoggingHelper.warning(f"Report file not found: {report_path}", user_id=user_id, project_id=project_id,
                                     extra_data={"file_name": file_name, "report_path": report_path, "framework_type": framework_type})
                continue

            try:
                # Verify report file contains required data
                async with aiofiles.open(report_path, 'r') as f:
                    report_content = await f.read()
                    report_data = json.loads(report_content)

                # Check for required fields
                required_fields = ['file_name', 'framework', 'average_score', 'summary', 'created_at']
                missing_fields = [field for field in required_fields if field not in report_data]

                if missing_fields:
                    LoggingHelper.error(f"Report for {file_name} is missing required fields: {missing_fields}",
                                       user_id=user_id, project_id=project_id,
                                       extra_data={"file_name": file_name, "missing_fields": missing_fields,
                                                 "framework_type": framework_type})
                    missing_files.append(file_name)
                    continue

                valid_reports.append((file_name, report_path, report_data))

            except json.JSONDecodeError:
                LoggingHelper.error(f"Invalid JSON in report file for {file_name}", user_id=user_id, project_id=project_id,
                                   extra_data={"file_name": file_name, "report_path": report_path, "framework_type": framework_type})
                missing_files.append(file_name)
                continue
            except Exception as e:
                LoggingHelper.error(f"Error reading report file for {file_name}: {str(e)}",
                                   user_id=user_id, project_id=project_id,
                                   extra_data={"file_name": file_name, "error": str(e), "report_path": report_path})
                missing_files.append(file_name)
                continue

        # If no valid reports found, raise 404
        if not valid_reports:
            error_msg = f"No valid compliance reports found for the requested files. Missing reports for: {', '.join(missing_files)}"
            LoggingHelper.error(error_msg, user_id=user_id, project_id=project_id,
                               extra_data={"missing_files": missing_files, "framework_type": framework_type})
            # Clean up temporary PDF file if it exists
            if pdf_path and os.path.exists(pdf_path):
                os.unlink(pdf_path)
            raise HTTPException(status_code=404, detail=error_msg)

        # Create the PDF document
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=72
        )

        # Define styles
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=15,
            alignment=TA_CENTER
        )
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=6
        )
        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=3
        )
        summary_style = ParagraphStyle(
            'CustomSummary',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=3,
            leading=20,
            alignment=TA_LEFT,
            spaceBefore=3,
            firstLineIndent=20
        )
        bold_style = ParagraphStyle(
            'CustomBold',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=6,
            leading=20,
            alignment=TA_LEFT,
            firstLineIndent=20,
            fontName='Helvetica-Bold'
        )

        # Define color-coded score styles
        score_style_green = ParagraphStyle(
            'ScoreGreen',
            parent=styles['Normal'],
            fontSize=14,
            spaceAfter=6,
            textColor=colors.green,
            fontName='Helvetica-Bold'
        )
        score_style_yellow = ParagraphStyle(
            'ScoreYellow',
            parent=styles['Normal'],
            fontSize=14,
            spaceAfter=6,
            textColor=colors.yellow,  # Changed from orangered to yellow as per requirements
            fontName='Helvetica-Bold'
        )
        score_style_red = ParagraphStyle(
            'ScoreRed',
            parent=styles['Normal'],
            fontSize=14,
            spaceAfter=6,
            textColor=colors.red,
            fontName='Helvetica-Bold'
        )

        def process_summary_text(text: str) -> List[Paragraph]:
            """
            Process summary text to handle bold sections marked with asterisks.
            Returns a list of Paragraph objects with appropriate styling.
            """
            paragraphs = []

            # Split text into lines
            lines = text.split('\n')

            for line in lines:
                if not line.strip():
                    continue

                # Find all bold sections (text between ** **)
                bold_pattern = r'\*\*(.*?)\*\*'

                # Split the line at bold sections
                parts = re.split(bold_pattern, line)

                if len(parts) == 1:
                    # No bold text in this line
                    paragraphs.append(Paragraph(line.strip(), summary_style))
                else:
                    # Combine parts with appropriate styling
                    formatted_parts = []
                    for i, part in enumerate(parts):
                        if i % 2 == 0:  # Regular text
                            if part.strip():
                                formatted_parts.append(part)
                        else:  # Bold text
                            formatted_parts.append(f'<b>{part}</b>')

                    # Join parts and create a paragraph with mixed styling
                    combined_text = ''.join(formatted_parts)
                    paragraphs.append(Paragraph(combined_text, summary_style))

            return paragraphs

        # Build the PDF content
        elements = []

        # Add title
        elements.append(Paragraph(f"{framework_type} Compliance Report", title_style))
        elements.append(Paragraph(
            f"Generated on: {current_time.strftime('%B %d, %Y at %I:%M %p')}",
            normal_style
        ))
        elements.append(Spacer(1, 15))

        # Add a legend for the color-coding
        elements.append(Paragraph("Score Legend:", heading_style))
        elements.append(Spacer(1, 6))

        # Create a table for the legend
        legend_data = [
            ["Score Range", "Rating", "Color"],
            ["8.0 - 10.0", "Good", ""],
            ["6.0 - 7.9", "Needs Improvement", ""],
            ["0.0 - 5.9", "Critical", ""]
        ]
        legend_table = Table(legend_data, colWidths=[1.5*inch, 2*inch, 1*inch])

        # Style the legend table
        legend_style = [
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            # Color the rating cells
            ('BACKGROUND', (2, 1), (2, 1), colors.green),
            ('BACKGROUND', (2, 2), (2, 2), colors.yellow),
            ('BACKGROUND', (2, 3), (2, 3), colors.red),
            # Make the text bold in the first column
            ('FONTNAME', (0, 1), (0, 3), 'Helvetica-Bold')
        ]

        legend_table.setStyle(TableStyle(legend_style))
        elements.append(legend_table)
        elements.append(Spacer(1, 30))

        # Process each validated file
        for i, (file_name, report_path, report_data) in enumerate(valid_reports):
            if i > 0:
                elements.append(Spacer(1, 15))

            # Add file information
            elements.append(Paragraph(f"File: {report_data.get('file_name', '')}", heading_style))
            elements.append(Paragraph(f"Framework: {report_data.get('framework', '')}", normal_style))

            # Get the average score and apply color-coding
            average_score = report_data.get('average_score', 0)

            # Determine which style to use based on score range
            if average_score >= 8.0:
                score_style = score_style_green
                score_text = f"Overall Average Score: {average_score:.2f} (Good)"
                bar_color = colors.green
            elif average_score >= 6.0:
                score_style = score_style_yellow
                score_text = f"Overall Average Score: {average_score:.2f} (Needs Improvement)"
                bar_color = colors.yellow
            else:
                score_style = score_style_red
                score_text = f"Overall Average Score: {average_score:.2f} (Critical)"
                bar_color = colors.red

            elements.append(Paragraph(score_text, score_style))

            # Add a visual progress bar for the score
            bar_width = 4 * inch  # Same width as the table
            progress = min(max(average_score / 10.0, 0), 1)  # Normalize score to 0-1 range

            # Create a progress bar using a table with colored cells
            progress_bar_data = [['', '']]
            progress_bar = Table(progress_bar_data, colWidths=[bar_width * progress, bar_width * (1 - progress)])
            progress_bar.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, 0), bar_color),
                ('BACKGROUND', (1, 0), (1, 0), colors.lightgrey),
                ('LINEBELOW', (0, 0), (1, 0), 1, colors.black),
                ('LINEABOVE', (0, 0), (1, 0), 1, colors.black),
                ('LINEBEFORE', (0, 0), (0, 0), 1, colors.black),
                ('LINEAFTER', (1, 0), (1, 0), 1, colors.black),
            ]))

            elements.append(Spacer(1, 6))
            elements.append(progress_bar)
            elements.append(Spacer(1, 12))

            # Add individual factors as a table
            factors = report_data.get('individual_factors_score', {})
            if factors:
                elements.append(Spacer(1, 12))
                elements.append(Paragraph("Individual Factor Scores:", heading_style))

                # Create table data
                table_data = [["Factor", "Score"]]
                for factor, score in factors.items():
                    table_data.append([factor, f"{float(score):.2f}"])

                # Create and style the table
                table = Table(table_data, colWidths=[4*inch, 1*inch])

                # Basic table styling
                table_style = [
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 14),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 1), (-1, -1), 12),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]

                # Add color-coding for each score in the table
                row_num = 1  # Start from row 1 (after header)
                for factor, score in factors.items():
                    score_value = float(score)
                    if score_value >= 8.0:
                        table_style.append(('TEXTCOLOR', (1, row_num), (1, row_num), colors.green))
                        table_style.append(('FONTNAME', (1, row_num), (1, row_num), 'Helvetica-Bold'))
                    elif score_value >= 6.0:
                        table_style.append(('TEXTCOLOR', (1, row_num), (1, row_num), colors.yellow))
                        table_style.append(('FONTNAME', (1, row_num), (1, row_num), 'Helvetica-Bold'))
                    else:
                        table_style.append(('TEXTCOLOR', (1, row_num), (1, row_num), colors.red))
                        table_style.append(('FONTNAME', (1, row_num), (1, row_num), 'Helvetica-Bold'))
                    row_num += 1

                table.setStyle(TableStyle(table_style))
                elements.append(table)

            # Add summary
            elements.append(Spacer(1, 12))
            elements.append(Paragraph("Summary:", heading_style))
            summary = report_data.get('summary', '')

            # Process summary text and add resulting paragraphs
            summary_paragraphs = process_summary_text(summary)
            for paragraph in summary_paragraphs:
                elements.append(paragraph)
                elements.append(Spacer(1, 3))

            # Add creation date with reduced spacing
            elements.append(Spacer(1, 3))
            created_at = datetime.fromisoformat(report_data.get('created_at', ''))
            elements.append(Paragraph(
                f"Report created: {created_at.strftime('%B %d, %Y at %I:%M %p')}",
                normal_style
            ))

        # Build the PDF
        doc.build(elements)
        LoggingHelper.info("Generated temporary PDF report", user_id=user_id, project_id=project_id,
                          extra_data={"pdf_path": pdf_path, "valid_reports_count": len(valid_reports),
                                    "framework_type": framework_type})

        # Return the PDF file and ensure cleanup
        background_tasks = BackgroundTask(
            lambda: os.unlink(pdf_path) if os.path.exists(pdf_path) else None
        )

        LoggingHelper.info("Returning PDF compliance report", user_id=user_id, project_id=project_id,
                          extra_data={"filename": f"combined_{framework_type}_compliance_{current_time.strftime('%Y%m%d_%H%M%S')}.pdf",
                                    "framework_type": framework_type})

        return FileResponse(
            pdf_path,
            filename=f"combined_{framework_type}_compliance_{current_time.strftime('%Y%m%d_%H%M%S')}.pdf",
            media_type="application/pdf",
            background=background_tasks
        )

    else:
        # Original functionality for generating individual reports
        LoggingHelper.info("Generating individual compliance reports", user_id=user_id, project_id=project_id,
                          extra_data={"file_count": len(file_names), "framework_type": framework_type})
        all_reports = []

        # Generate report for each file individually
        for file_name in file_names:
            LoggingHelper.info(f"Processing file: {file_name}", user_id=user_id, project_id=project_id,
                              extra_data={"file_name": file_name, "framework_type": framework_type})
            ext = file_name.split(".")[-1].lower()

            # Get content for this specific file
            try:
                # Always use pickle files regardless of file type
                pkl_file_path = os.path.join(config.file_path, actual_project_id,
                                           config.non_data_analysis_folder_name if ext not in ["csv", "xlsx"]
                                           else config.data_analysis_folder_name,
                                           f"{file_name.split('.')[0]}.pkl")

                LoggingHelper.info(f"Reading pickle file: {pkl_file_path}", user_id=user_id, project_id=project_id,
                                  extra_data={"file_name": file_name, "pkl_file_path": pkl_file_path, "file_ext": ext})
                content = await utils.read_pickle_file_for_compliance(pkl_file_path)

                # For Excel/CSV files, fall back to direct file reading if pickle doesn't exist
                if not content and ext in ["csv", "xlsx"]:
                    LoggingHelper.info(f"Pickle file not found for {file_name}, attempting direct file read",
                                      user_id=user_id, project_id=project_id,
                                      extra_data={"file_name": file_name, "pkl_file_path": pkl_file_path})
                    file_path = os.path.join(config.file_path, actual_project_id,
                                           config.data_analysis_folder_name, file_name)
                    file_exists = await async_path_exists(file_path)
                    if file_exists:
                        content = await utils.read_data_file(file_path)
                        LoggingHelper.info(f"Successfully read file directly: {file_path}",
                                          user_id=user_id, project_id=project_id,
                                          extra_data={"file_name": file_name, "direct_file_path": file_path})

                # If still no content, raise error
                if not content:
                    error_msg = f"Failed to read content from file: {file_name}"
                    LoggingHelper.error(error_msg, user_id=user_id, project_id=project_id,
                                       extra_data={"file_name": file_name, "pkl_file_path": pkl_file_path})
                    raise ValueError(error_msg)

                LoggingHelper.info(f"File content read successfully", user_id=user_id, project_id=project_id,
                                  extra_data={"file_name": file_name, "content_size_bytes": len(content)})

                # Check for token limit and truncate if needed before generating compliance report
                from services.token_utils import get_tokens
                from services.llm_service import get_model_context_window, get_provider_economy_model
                from services.text_extraction_utils import truncate_content_intelligently
                
                # Get project model information
                project_model = await mongo_manager.get_model_for_project(actual_project_id)
                model = get_provider_economy_model(project_model)
                
                # Create prompt and estimate tokens
                prompt_template = get_compliance_prompt(content, framework_type)
                prompt_tokens = get_tokens(prompt_template, model)
                
                # Get context window and calculate available tokens
                context_window = get_model_context_window(model)
                safety_margin = 5000
                max_safe_tokens = context_window - safety_margin
                
                if prompt_tokens > max_safe_tokens:
                    LoggingHelper.warning(
                        f"Content exceeds available tokens ({prompt_tokens} > {max_safe_tokens}), applying intelligent truncation",
                        user_id=user_id, project_id=project_id,
                        extra_data={
                            "original_tokens": prompt_tokens,
                            "available_tokens": max_safe_tokens,
                            "context_window": context_window,
                            "safety_margin": safety_margin,
                            "file_name": file_name
                        }
                    )
                    
                    # Get just the raw content portion
                    # Estimate tokens needed for prompt template without content
                    empty_prompt = get_compliance_prompt("", framework_type)
                    template_tokens = get_tokens(empty_prompt, model)
                    
                    # Calculate how many tokens we can use for content
                    available_for_content = max_safe_tokens - template_tokens
                    
                    # Truncate content intelligently
                    truncated_content = await truncate_content_intelligently(
                        content, available_for_content, model
                    )
                    
                    # Create new prompt with truncated content
                    prompt = get_compliance_prompt(truncated_content, framework_type)
                    
                    LoggingHelper.info(
                        f"Content truncated from {prompt_tokens} to {get_tokens(prompt, model)} tokens",
                        user_id=user_id, project_id=project_id,
                        extra_data={"file_name": file_name}
                    )
                else:
                    # Use original content if within limits
                    prompt = prompt_template

                # Generate compliance report for this file
                LoggingHelper.info(f"Generating compliance report for {file_name}", user_id=user_id, project_id=project_id,
                                  extra_data={"file_name": file_name, "framework_type": framework_type})
                compliance_report = await get_compliance_report(prompt, actual_project_id, mongo_manager, usage_logger, usage_logger)
                # Convert ComplianceReport Pydantic model to dictionary
                report_data = compliance_report.model_dump() if hasattr(compliance_report, 'model_dump') else compliance_report.dict()
                LoggingHelper.info(f"Successfully generated compliance report for {file_name}",
                                  user_id=user_id, project_id=project_id,
                                  extra_data={"file_name": file_name, "framework_type": framework_type,
                                            "report_size": len(str(compliance_report))})

                # Log usage consumption for this compliance report
                usage_logger.log_step_summary("COMPLIANCE_REPORT_GENERATION", f"Generated compliance report for file: {file_name}")

                # Add file name to the report
                report_data["file_name"] = file_name

                # Create or override report file
                report_filename = f"{file_name.split('.')[0]}_{framework_type}_compliance.json"
                report_path = os.path.join(compliance_dir, report_filename)

                # Delete existing report file if it exists
                file_exists = await async_path_exists(report_path)
                if file_exists:
                    LoggingHelper.info(f"Removing existing report file: {report_path}",
                                      user_id=user_id, project_id=project_id,
                                      extra_data={"file_name": file_name, "report_path": report_path})
                    await async_remove(report_path)

                # Get current timestamp
                current_time = datetime.now(timezone.utc)

                # Add timestamp to report data before saving to file
                report_data["created_at"] = current_time.isoformat()

                # Save new report
                async with aiofiles.open(report_path, 'w') as f:
                    await f.write(jp.dumps(report_data, indent=4))
                LoggingHelper.info(f"Saved compliance report to file: {report_path}",
                                  user_id=user_id, project_id=project_id,
                                  extra_data={"file_name": file_name, "report_path": report_path,
                                            "framework_type": framework_type})

                # Check if the file is valid for compliance reporting
                is_valid = report_data.get('is_valid', True)  # Default to True for backward compatibility

                if not is_valid:
                    # If file is not valid, delete the report file we just created
                    file_exists = await async_path_exists(report_path)
                    if file_exists:
                        await async_remove(report_path)
                        LoggingHelper.info(f"Removed invalid compliance report file: {report_path}",
                                          user_id=user_id, project_id=project_id,
                                          extra_data={"file_name": file_name, "report_path": report_path})

                    # Get the reason for invalidity
                    reason = report_data.get('reason', 'No reason provided')
                    error_msg = f"The content of {file_name} is not valid for {framework_type} framework check: {reason}"
                    LoggingHelper.error(error_msg, user_id=user_id, project_id=project_id,
                                       extra_data={"file_name": file_name, "framework_type": framework_type,
                                                 "reason": reason})

                    # Raise an HTTP exception to inform the user
                    # The status code will not be included in the response message
                    # because FastAPI uses the 'detail' property which doesn't include the status code
                    raise HTTPException(status_code=400, detail=f"The content of {file_name} is not valid for {framework_type.upper()} framework type.")

                # If valid, proceed with saving the report
                average_score = report_data.get('average_score', 0)
                LoggingHelper.info(f"Average compliance score for {file_name}: {average_score}",
                                  user_id=user_id, project_id=project_id,
                                  extra_data={"file_name": file_name, "average_score": average_score,
                                            "framework_type": framework_type})

                # Delete any existing compliance report for this file and framework type
                delete_result = await mongo_manager.compliance_collection.delete_many({
                    "project_id": actual_project_id,
                    "file_name": file_name,
                    "framework_type": framework_type
                })
                LoggingHelper.info(f"Deleted {delete_result.deleted_count} existing compliance records for {file_name}",
                                  user_id=user_id, project_id=project_id,
                                  extra_data={"file_name": file_name, "framework_type": framework_type,
                                            "deleted_count": delete_result.deleted_count})

                # Save to compliance collection
                compliance_data = {
                    "file_name": file_name,
                    "framework_type": framework_type,
                    "average_score": average_score,
                    "report_path": report_path,
                    "created_at": current_time
                }

                await mongo_manager.save_compliance_report(actual_project_id, compliance_data)
                LoggingHelper.info(f"Successfully saved compliance data to MongoDB for {file_name}",
                                  user_id=user_id, project_id=project_id,
                                  extra_data={"file_name": file_name, "framework_type": framework_type,
                                            "average_score": average_score})

                # Include timestamp in the response
                all_reports.append({
                    "file_name": file_name,
                    "average_score": compliance_data["average_score"],
                    "framework_type": framework_type,
                    "created_at": current_time.isoformat()
                })

            except HTTPException as http_e:
                # Re-raise HTTP exceptions without modification
                # Log the error using the detail property to avoid including the status code in logs
                LoggingHelper.error(f"Error processing file {file_name}: {http_e.detail}",
                                   user_id=user_id, project_id=project_id,
                                   extra_data={"file_name": file_name, "http_status": http_e.status_code,
                                             "detail": http_e.detail, "framework_type": framework_type})
                raise
            except Exception as e:
                # Check if this is a context length error, even after our truncation attempt
                if isinstance(e, ValueError) and "exceed safe limit" in str(e):
                    LoggingHelper.error(f"File {file_name} is too large for compliance checking even after truncation",
                                       user_id=user_id, project_id=project_id,
                                       extra_data={"file_name": file_name, "framework_type": framework_type,
                                                 "error": str(e)})
                    # Instead of failing, create an error report
                    error_report = {
                        "file_name": file_name,
                        "framework": framework_type,
                        "is_valid": False,
                        "reason": "This file is too large for compliance checking even after intelligent truncation.",
                        "created_at": datetime.now(timezone.utc).isoformat()
                    }
                    all_reports.append(error_report)
                    continue
                
                error_msg = f"Error processing file {file_name}: {str(e)}"
                LoggingHelper.error(error_msg, user_id=user_id, project_id=project_id,
                                   extra_data={"file_name": file_name, "error": str(e), "framework_type": framework_type})
                raise HTTPException(status_code=500, detail=error_msg)

        # Generate a response_id for compliance report and link usage events
        from bson import ObjectId
        compliance_response_id = str(ObjectId())
        usage_logger.set_response_id(compliance_response_id)
        LoggingHelper.info(f"Generated response_id for compliance report: {compliance_response_id}",
                          user_id=user_id, project_id=project_id,
                          extra_data={"response_id": compliance_response_id, "reports_count": len(all_reports),
                                    "framework_type": framework_type})

        # Save usage events to MongoDB before returning
        usage_logger.log_step_summary("PROCESSING_COMPLETE", f"Compliance report generation completed successfully for {len(all_reports)} files")
        try:
            await usage_logger.save_to_mongodb()
            LoggingHelper.info("Usage events saved to MongoDB for compliance report generation",
                              user_id=user_id, project_id=project_id,
                              extra_data={"reports_count": len(all_reports), "framework_type": framework_type})
        except Exception as e:
            LoggingHelper.error(f"Failed to save usage events to MongoDB for compliance report generation: {str(e)}",
                               user_id=user_id, project_id=project_id,
                               extra_data={"error": str(e), "reports_count": len(all_reports)})



        LoggingHelper.info(f"Returning compliance reports with timestamps for {len(all_reports)} files",
                          user_id=user_id, project_id=project_id,
                          extra_data={"reports_count": len(all_reports), "framework_type": framework_type})
        return all_reports

@router.get(f"/{{user_id}}/{{project_id}}/{config.get_compliance_reports_url}")
async def get_compliance_reports(request: Request, user_id: str, project_id: str):
    # user_id and project_id are available from path parameters
    LoggingHelper.info("Get compliance reports called", user_id=user_id, project_id=project_id)

    if config.VALIDATE_TOKEN:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            LoggingHelper.error("Authorization header is missing or invalid", user_id=user_id, project_id=project_id)
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

        token = auth_header.split(' ')[1]
        payload = decode_jwt_token(token)
        if payload:
            user_email = payload['sub']
            is_valid_user = await mongo_manager.check_user_exists(user_email)
            if not is_valid_user:
                LoggingHelper.error("User not found", user_id=user_id, project_id=project_id,
                                   extra_data={"user_email": user_email})
                raise HTTPException(status_code=401, detail="Invalid user")
            else:
                LoggingHelper.info("Valid User authenticated", user_id=user_id, project_id=project_id,
                                  extra_data={"user_email": user_email})
    else:
        LoggingHelper.info("Token validation is turned OFF. Skipping authentication checks.",
                          user_id=user_id, project_id=project_id)







    try:
        # Convert _id to project_id
        actual_project_id = await utils.get_project_id_from_object_id(project_id, mongo_manager)
        if not actual_project_id:
            LoggingHelper.error(f"Project with _id {project_id} not found", user_id=user_id, project_id=project_id,
                               extra_data={"object_id": project_id})
            raise HTTPException(status_code=404, detail="Project not found")

        LoggingHelper.info("Converting object_id to project_id", user_id=user_id, project_id=project_id,
                          extra_data={"object_id": project_id, "actual_project_id": actual_project_id})

        # Check if project exists and user has access
        project_data, exists, _ = await mongo_manager.get_or_initialize_project(actual_project_id)

        if not exists:
            LoggingHelper.error(f"Project {actual_project_id} not found", user_id=user_id, project_id=project_id,
                               extra_data={"actual_project_id": actual_project_id})
            raise HTTPException(status_code=404, detail="Project not found")

        # Check if user has access to this project
        if user_id != project_data.get("user_id", ""):
            LoggingHelper.error(f"User {user_id} not authorized for project {actual_project_id}",
                               user_id=user_id, project_id=project_id,
                               extra_data={"actual_project_id": actual_project_id,
                                         "project_user_id": project_data.get("user_id", "")})
            raise HTTPException(status_code=403, detail="User not authorized to access this project")

        # Get all compliance reports for the project
        reports = await mongo_manager.get_compliance_reports(actual_project_id)

        # Check if there are any reports
        if not reports:
            LoggingHelper.info(f"No compliance reports found for project {actual_project_id}",
                              user_id=user_id, project_id=project_id,
                              extra_data={"actual_project_id": actual_project_id})
            return []

        # Format the response
        formatted_reports = []
        for report in reports:
            formatted_report = {
                "file_name": report.get("file_name"),
                "framework_type": report.get("framework_type"),
                "average_score": report.get("average_score"),
                "created_at": report.get("created_at").isoformat() if report.get("created_at") else None
            }
            formatted_reports.append(formatted_report)

        LoggingHelper.info(f"Found {len(formatted_reports)} compliance reports for project {actual_project_id}",
                          user_id=user_id, project_id=project_id,
                          extra_data={"actual_project_id": actual_project_id, "reports_count": len(formatted_reports)})
        return formatted_reports

    except HTTPException:
        raise
    except Exception as e:
        LoggingHelper.error(f"Error retrieving compliance reports: {str(e)}", user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e), "object_id": project_id})
        raise HTTPException(status_code=500, detail=str(e))

@router.get(f"/{{user_id}}/{{project_id}}/{config.get_all_compliance_reports_markdown_url}", response_model=insight_model.MarkdownResponse)
async def get_all_compliance_reports_markdown(request: Request, user_id: str, project_id: str):
    # user_id and project_id are available from path parameters
    LoggingHelper.info("Get all compliance reports markdown called", user_id=user_id, project_id=project_id)

    if config.VALIDATE_TOKEN:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            LoggingHelper.error("Authorization header is missing or invalid", user_id=user_id, project_id=project_id)
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

        token = auth_header.split(' ')[1]
        payload = decode_jwt_token(token)
        if payload:
            user_email = payload['sub']
            is_valid_user = await mongo_manager.check_user_exists(user_email)
            if not is_valid_user:
                LoggingHelper.error("User not found", user_id=user_id, project_id=project_id,
                                   extra_data={"user_email": user_email})
                raise HTTPException(status_code=401, detail="Invalid user")
            else:
                LoggingHelper.info("Valid User authenticated", user_id=user_id, project_id=project_id,
                                  extra_data={"user_email": user_email})
    else:
        LoggingHelper.info("Token validation is turned OFF. Skipping authentication checks.",
                          user_id=user_id, project_id=project_id)

    try:
        # Convert _id to project_id
        actual_project_id = await utils.get_project_id_from_object_id(project_id)
        if not actual_project_id:
            LoggingHelper.error(f"Project with _id {project_id} not found", user_id=user_id, project_id=project_id,
                               extra_data={"object_id": project_id})
            raise HTTPException(status_code=404, detail="Project not found")

        # Verify project and user access
        project_data, exists, _ = await mongo_manager.get_or_initialize_project(actual_project_id)

        if not exists:
            LoggingHelper.error(f"Project {actual_project_id} not found", user_id=user_id, project_id=project_id,
                               extra_data={"actual_project_id": actual_project_id})
            raise HTTPException(status_code=404, detail="Project not found")

        if user_id != project_data.get("user_id", ""):
            LoggingHelper.error(f"User {user_id} not authorized for project {actual_project_id}",
                               user_id=user_id, project_id=project_id,
                               extra_data={"actual_project_id": actual_project_id,
                                         "project_user_id": project_data.get("user_id", "")})
            raise HTTPException(status_code=403, detail="User not authorized to access this project")

        # Get all compliance reports
        reports = await mongo_manager.get_compliance_reports(actual_project_id)

        if not reports:
            LoggingHelper.error(f"No compliance reports found for project {actual_project_id}",
                               user_id=user_id, project_id=project_id,
                               extra_data={"actual_project_id": actual_project_id})
            raise HTTPException(status_code=404, detail="No compliance reports found for this project")

        # Create compliance reports directory if not exists
        compliance_dir = os.path.join(config.file_path, actual_project_id, "compliance_reports")
        await async_makedirs(compliance_dir, exist_ok=True)

        # Generate markdown content
        current_time = datetime.now(timezone.utc)
        markdown_content = f"# Comprehensive Compliance Report\n\n"
        markdown_content += f"Generated on: {current_time.strftime('%B %d, %Y at %I:%M %p')}\n\n"

        # Add a legend for the color-coding
        markdown_content += "## Score Legend\n\n"
        markdown_content += "| Score Range | Rating | Indicator |\n"
        markdown_content += "|------------|--------|-----------|\n"
        markdown_content += "| 8.0 - 10.0 | Good | 🟢 |\n"
        markdown_content += "| 6.0 - 7.9 | Needs Improvement | 🟠 |\n"
        markdown_content += "| 0.0 - 5.9 | Critical | 🔴 |\n\n"

        # Group reports by framework type
        framework_reports = {}
        for report in reports:
            framework = report.get('framework_type', 'UNKNOWN')
            if framework not in framework_reports:
                framework_reports[framework] = []
            framework_reports[framework].append(report)

        # Process each framework type
        for framework, framework_group in framework_reports.items():
            markdown_content += f"## {framework} Compliance Reports\n\n"

            # Process each report in the framework group
            for report in framework_group:
                # Add file information
                markdown_content += f"### File: {report.get('file_name', '')}\n\n"

                # Get the average score and apply color-coding
                average_score = report.get('average_score', 0)

                # Determine which color indicator to use based on score range
                if average_score >= 8.0:
                    score_indicator = "🟢" # Green circle
                    score_label = "Good"
                elif average_score >= 6.0:
                    score_indicator = "🟠" # Orange circle
                    score_label = "Needs Improvement"
                else:
                    score_indicator = "🔴" # Red circle
                    score_label = "Critical"

                markdown_content += f"Overall Average Score: {average_score:.2f} {score_indicator} ({score_label})\n\n"

                # Get the full report content from the report file
                # Use the same case (uppercase) as when the file was created
                report_filename = f"{report['file_name'].split('.')[0]}_{framework}_compliance.json"
                report_path = os.path.join(compliance_dir, report_filename)

                try:
                    async with aiofiles.open(report_path, 'r') as f:
                        content = await f.read()
                        full_report = json.loads(content)

                    # Check if the file is valid for compliance reporting
                    # Note: This is for backward compatibility with reports generated before the change
                    # New reports will always be valid since we now raise an error for invalid files
                    is_valid = full_report.get('is_valid', True)

                    if not is_valid:
                        # If file is not valid for compliance reporting, show the reason
                        markdown_content += "#### Not Valid for Compliance Reporting\n\n"
                        reason = full_report.get('reason', 'No reason provided')
                        markdown_content += f"{reason}\n\n"

                        # Log a warning about this legacy invalid report
                        LoggingHelper.warning(f"Found legacy invalid compliance report for {report.get('file_name', '')}: {reason}",
                                             user_id=user_id, project_id=project_id,
                                             extra_data={"file_name": report.get('file_name', ''), "reason": reason,
                                                       "actual_project_id": actual_project_id})
                    else:
                        # Add individual factors as a table
                        factors = full_report.get('individual_factors_score', {})
                        if factors:
                            markdown_content += "#### Individual Factor Scores:\n\n"

                            # Create markdown table
                            markdown_content += "| Factor | Score | Rating |\n"
                            markdown_content += "|--------|-------|-------|\n"
                            for factor, score in factors.items():
                                score_value = float(score)
                                # Determine which color indicator to use based on score range
                                if score_value >= 8.0:
                                    score_indicator = "🟢" # Green circle
                                    score_label = "Good"
                                elif score_value >= 6.0:
                                    score_indicator = "🟠" # Orange circle
                                    score_label = "Needs Improvement"
                                else:
                                    score_indicator = "🔴" # Red circle
                                    score_label = "Critical"
                                markdown_content += f"| {factor} | **{score_value:.2f}** | {score_indicator} {score_label} |\n"
                            markdown_content += "\n"

                        # Add summary
                        if 'summary' in full_report:
                            markdown_content += "#### Summary:\n\n"
                            summary = full_report.get('summary', '')
                            markdown_content += f"{summary}\n\n"

                except Exception as e:
                    LoggingHelper.error(f"Error reading report file {report_path}: {e}",
                                       user_id=user_id, project_id=project_id,
                                       extra_data={"report_path": report_path, "error": str(e),
                                                 "actual_project_id": actual_project_id})
                    markdown_content += f"Error reading detailed report: {str(e)}\n\n"

                # Add creation date
                created_at = report.get('created_at')
                if created_at:
                    if isinstance(created_at, str):
                        created_at = datetime.fromisoformat(created_at)
                    markdown_content += f"Report created: {created_at.strftime('%B %d, %Y at %I:%M %p')}\n\n"

                markdown_content += "---\n\n"

            markdown_content += "\n\n"

        LoggingHelper.info(f"Generated comprehensive compliance markdown report for project {actual_project_id}",
                          user_id=user_id, project_id=project_id,
                          extra_data={"actual_project_id": actual_project_id, "reports_count": len(reports),
                                    "markdown_length": len(markdown_content)})

        # Return the markdown content as JSON response
        return {"markdown_content": markdown_content}

    except HTTPException:
        raise
    except Exception as e:
        LoggingHelper.error(f"Error generating comprehensive compliance markdown: {str(e)}",
                           user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e), "object_id": project_id})
        raise HTTPException(status_code=500, detail=str(e))

@router.get(f"/{{user_id}}/{{project_id}}/{config.get_all_compliance_reports_pdf_url}")
async def get_all_compliance_reports_pdf(request: Request, user_id: str, project_id: str):
    # user_id and project_id are available from path parameters
    LoggingHelper.info("Generating comprehensive compliance PDF", user_id=user_id, project_id=project_id,
                      extra_data={"object_id": project_id})

    if config.VALIDATE_TOKEN:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            LoggingHelper.error("Authorization header is missing or invalid", user_id=user_id, project_id=project_id)
            raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

        token = auth_header.split(' ')[1]
        payload = decode_jwt_token(token)
        if payload:
            user_email = payload['sub']
            is_valid_user = await mongo_manager.check_user_exists(user_email)
            if not is_valid_user:
                LoggingHelper.error("User not found", user_id=user_id, project_id=project_id,
                                   extra_data={"user_email": user_email})
                raise HTTPException(status_code=401, detail="Invalid user")
            else:
                LoggingHelper.info("Valid User authenticated", user_id=user_id, project_id=project_id,
                                  extra_data={"user_email": user_email})
    else:
        LoggingHelper.info("Token validation is turned OFF. Skipping authentication checks.",
                          user_id=user_id, project_id=project_id)

    try:
        LoggingHelper.info(f"Generating comprehensive compliance PDF for project {project_id}",
                          user_id=user_id, project_id=project_id,
                          extra_data={"object_id": project_id})

        # Convert _id to project_id
        actual_project_id = await utils.get_project_id_from_object_id(project_id, mongo_manager)
        if not actual_project_id:
            LoggingHelper.error(f"Project with _id {project_id} not found", user_id=user_id, project_id=project_id,
                               extra_data={"object_id": project_id})
            raise HTTPException(status_code=404, detail="Project not found")

        # Verify project and user access
        project_data, exists, _ = await mongo_manager.get_or_initialize_project(actual_project_id)

        if not exists:
            LoggingHelper.error(f"Project {actual_project_id} not found", user_id=user_id, project_id=project_id,
                               extra_data={"actual_project_id": actual_project_id})
            raise HTTPException(status_code=404, detail="Project not found")

        if user_id != project_data.get("user_id", ""):
            LoggingHelper.error(f"User {user_id} not authorized for project {actual_project_id}",
                               user_id=user_id, project_id=project_id,
                               extra_data={"actual_project_id": actual_project_id,
                                         "project_user_id": project_data.get("user_id", "")})
            raise HTTPException(status_code=403, detail="User not authorized to access this project")

        # Get all compliance reports
        reports = await mongo_manager.get_compliance_reports(actual_project_id)

        if not reports:
            LoggingHelper.error(f"No compliance reports found for project {actual_project_id}",
                               user_id=user_id, project_id=project_id,
                               extra_data={"actual_project_id": actual_project_id})
            raise HTTPException(status_code=404, detail="No compliance reports found for this project")

        # Create compliance reports directory
        compliance_dir = os.path.join(config.file_path, actual_project_id, "compliance_reports")
        await async_makedirs(compliance_dir, exist_ok=True)

        # Generate PDF filename
        current_time = datetime.now(timezone.utc)
        temp_filename = f"temp_{uuid.uuid4().hex}_all_compliance_reports.pdf"
        pdf_path = os.path.join(compliance_dir, temp_filename)

        # Create PDF document
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=72
        )

        # Define styles
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER
        )
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=12
        )
        subheading_style = ParagraphStyle(
            'CustomSubHeading',
            parent=styles['Heading3'],
            fontSize=14,
            spaceAfter=8
        )
        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=6
        )
        summary_style = ParagraphStyle(
            'CustomSummary',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=6,
            leading=20,
            alignment=TA_LEFT,
            firstLineIndent=20
        )
        bold_style = ParagraphStyle(
            'CustomBold',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=6,
            leading=20,
            alignment=TA_LEFT,
            firstLineIndent=20,
            fontName='Helvetica-Bold'
        )

        # Define color-coded score styles
        score_style_green = ParagraphStyle(
            'ScoreGreen',
            parent=styles['Normal'],
            fontSize=14,
            spaceAfter=6,
            textColor=colors.green,
            fontName='Helvetica-Bold'
        )
        score_style_yellow = ParagraphStyle(
            'ScoreYellow',
            parent=styles['Normal'],
            fontSize=14,
            spaceAfter=6,
            textColor=colors.yellow,
            fontName='Helvetica-Bold'
        )
        score_style_red = ParagraphStyle(
            'ScoreRed',
            parent=styles['Normal'],
            fontSize=14,
            spaceAfter=6,
            textColor=colors.red,
            fontName='Helvetica-Bold'
        )

        def process_summary_text(text: str) -> List[Paragraph]:
            """
            Process summary text to handle bold sections marked with asterisks.
            Returns a list of Paragraph objects with appropriate styling.
            """
            paragraphs = []

            # Split text into lines
            lines = text.split('\n')

            for line in lines:
                if not line.strip():
                    continue

                # Find all bold sections (text between ** **)
                bold_pattern = r'\*\*(.*?)\*\*'

                # Split the line at bold sections
                parts = re.split(bold_pattern, line)

                if len(parts) == 1:
                    # No bold text in this line
                    paragraphs.append(Paragraph(line.strip(), summary_style))
                else:
                    # Combine parts with appropriate styling
                    formatted_parts = []
                    for i, part in enumerate(parts):
                        if i % 2 == 0:  # Regular text
                            if part.strip():
                                formatted_parts.append(part)
                        else:  # Bold text
                            formatted_parts.append(f'<b>{part}</b>')

                    # Join parts and create a paragraph with mixed styling
                    combined_text = ''.join(formatted_parts)
                    paragraphs.append(Paragraph(combined_text, summary_style))

            return paragraphs

        # Build PDF content
        elements = []

        # Add title
        elements.append(Paragraph("Comprehensive Compliance Report", title_style))
        elements.append(Paragraph(
            f"Generated on: {current_time.strftime('%B %d, %Y at %I:%M %p')}",
            normal_style
        ))
        elements.append(Spacer(1, 15))

        # Add a legend for the color-coding
        elements.append(Paragraph("Score Legend:", heading_style))
        elements.append(Spacer(1, 6))

        # Create a table for the legend
        legend_data = [
            ["Score Range", "Rating", "Color"],
            ["8.0 - 10.0", "Good", ""],
            ["6.0 - 7.9", "Needs Improvement", ""],
            ["0.0 - 5.9", "Critical", ""]
        ]
        legend_table = Table(legend_data, colWidths=[1.5*inch, 2*inch, 1*inch])

        # Style the legend table
        legend_style = [
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            # Color the rating cells
            ('BACKGROUND', (2, 1), (2, 1), colors.green),
            ('BACKGROUND', (2, 2), (2, 2), colors.yellow),
            ('BACKGROUND', (2, 3), (2, 3), colors.red),
            # Make the text bold in the first column
            ('FONTNAME', (0, 1), (0, 3), 'Helvetica-Bold')
        ]

        legend_table.setStyle(TableStyle(legend_style))
        elements.append(legend_table)
        elements.append(Spacer(1, 30))

        # Group reports by framework type
        framework_reports = {}
        for report in reports:
            framework = report.get('framework_type', 'UNKNOWN')
            if framework not in framework_reports:
                framework_reports[framework] = []
            framework_reports[framework].append(report)

        # Process each framework type
        for framework, framework_group in framework_reports.items():
            elements.append(Paragraph(f"{framework} Compliance Reports", heading_style))
            elements.append(Spacer(1, 12))

            # Process each report in the framework group
            for report in framework_group:
                # Add file information
                elements.append(Paragraph(f"File: {report.get('file_name', '')}", subheading_style))

                # Get the average score and apply color-coding
                average_score = report.get('average_score', 0)

                # Determine which style to use based on score range
                if average_score >= 8.0:
                    score_style = score_style_green
                    score_text = f"Overall Average Score: {average_score:.2f} (Good)"
                    bar_color = colors.green
                elif average_score >= 6.0:
                    score_style = score_style_yellow
                    score_text = f"Overall Average Score: {average_score:.2f} (Needs Improvement)"
                    bar_color = colors.yellow
                else:
                    score_style = score_style_red
                    score_text = f"Overall Average Score: {average_score:.2f} (Critical)"
                    bar_color = colors.red

                elements.append(Paragraph(score_text, score_style))

                # Add a visual progress bar for the score
                bar_width = 4 * inch  # Same width as the table
                progress = min(max(average_score / 10.0, 0), 1)  # Normalize score to 0-1 range

                # Create a progress bar using a table with colored cells
                progress_bar_data = [['', '']]
                progress_bar = Table(progress_bar_data, colWidths=[bar_width * progress, bar_width * (1 - progress)])
                progress_bar.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (0, 0), bar_color),
                    ('BACKGROUND', (1, 0), (1, 0), colors.lightgrey),
                    ('LINEBELOW', (0, 0), (1, 0), 1, colors.black),
                    ('LINEABOVE', (0, 0), (1, 0), 1, colors.black),
                    ('LINEBEFORE', (0, 0), (0, 0), 1, colors.black),
                    ('LINEAFTER', (1, 0), (1, 0), 1, colors.black),
                ]))

                elements.append(Spacer(1, 6))
                elements.append(progress_bar)
                elements.append(Spacer(1, 12))

                # Get the full report content from the report file
                # Use the same case (uppercase) as when the file was created
                report_filename = f"{report['file_name'].split('.')[0]}_{framework}_compliance.json"
                report_path = os.path.join(compliance_dir, report_filename)

                try:
                    async with aiofiles.open(report_path, 'r') as f:
                        content = await f.read()
                        full_report = json.loads(content)

                    # Check if the file is valid for compliance reporting
                    # Note: This is for backward compatibility with reports generated before the change
                    # New reports will always be valid since we now raise an error for invalid files
                    is_valid = full_report.get('is_valid', True)

                    if not is_valid:
                        # If file is not valid for compliance reporting, show the reason
                        elements.append(Spacer(1, 12))
                        elements.append(Paragraph("Not Valid for Compliance Reporting", normal_style))
                        reason = full_report.get('reason', 'No reason provided')
                        elements.append(Paragraph(reason, summary_style))

                        # Log a warning about this legacy invalid report
                        LoggingHelper.warning(f"Found legacy invalid compliance report for {report.get('file_name', '')}: {reason}",
                                             user_id=user_id, project_id=project_id,
                                             extra_data={"file_name": report.get('file_name', ''), "reason": reason,
                                                       "actual_project_id": actual_project_id})
                    else:
                        # Add individual factors as a table
                        factors = full_report.get('individual_factors_score', {})
                        if factors:
                            elements.append(Spacer(1, 12))
                            elements.append(Paragraph("Individual Factor Scores:", normal_style))

                            # Create table data
                            table_data = [["Factor", "Score"]]
                            for factor, score in factors.items():
                                table_data.append([factor, f"{float(score):.2f}"])

                            # Create and style the table
                            table = Table(table_data, colWidths=[4*inch, 1*inch])

                            # Basic table styling
                            table_style = [
                                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                                ('FONTSIZE', (0, 0), (-1, 0), 14),
                                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                                ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                                ('FONTSIZE', (0, 1), (-1, -1), 12),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                                ('GRID', (0, 0), (-1, -1), 1, colors.black)
                            ]

                            # Add color-coding for each score in the table
                            row_num = 1  # Start from row 1 (after header)
                            for factor, score in factors.items():
                                score_value = float(score)
                                if score_value >= 8.0:
                                    table_style.append(('TEXTCOLOR', (1, row_num), (1, row_num), colors.green))
                                    table_style.append(('FONTNAME', (1, row_num), (1, row_num), 'Helvetica-Bold'))
                                elif score_value >= 6.0:
                                    table_style.append(('TEXTCOLOR', (1, row_num), (1, row_num), colors.yellow))
                                    table_style.append(('FONTNAME', (1, row_num), (1, row_num), 'Helvetica-Bold'))
                                else:
                                    table_style.append(('TEXTCOLOR', (1, row_num), (1, row_num), colors.red))
                                    table_style.append(('FONTNAME', (1, row_num), (1, row_num), 'Helvetica-Bold'))
                                row_num += 1

                            table.setStyle(TableStyle(table_style))
                            elements.append(table)

                        # Add summary
                        if 'summary' in full_report:
                            elements.append(Spacer(1, 12))
                            elements.append(Paragraph("Summary:", normal_style))
                            summary = full_report.get('summary', '')

                            # Process summary text and add resulting paragraphs
                            summary_paragraphs = process_summary_text(summary)
                            for paragraph in summary_paragraphs:
                                elements.append(paragraph)
                                elements.append(Spacer(1, 3))

                except Exception as e:
                    LoggingHelper.error(f"Error reading report file {report_path}: {e}",
                                       user_id=user_id, project_id=project_id,
                                       extra_data={"report_path": report_path, "error": str(e),
                                                 "actual_project_id": actual_project_id})
                    elements.append(Paragraph(f"Error reading detailed report: {str(e)}", normal_style))

                # Add creation date
                elements.append(Spacer(1, 6))
                created_at = report.get('created_at')
                if created_at:
                    if isinstance(created_at, str):
                        created_at = datetime.fromisoformat(created_at)
                    elements.append(Paragraph(
                        f"Report created: {created_at.strftime('%B %d, %Y at %I:%M %p')}",
                        normal_style
                    ))

                elements.append(Spacer(1, 20))

            elements.append(Spacer(1, 30))

        # Build the PDF
        doc.build(elements)
        LoggingHelper.info(f"Generated comprehensive compliance PDF report: {pdf_path}",
                          user_id=user_id, project_id=project_id,
                          extra_data={"pdf_path": pdf_path, "reports_count": len(reports),
                                    "actual_project_id": actual_project_id})

        # Return the PDF file and ensure cleanup
        background_tasks = BackgroundTask(
            lambda: os.unlink(pdf_path) if os.path.exists(pdf_path) else None
        )
        return FileResponse(
            pdf_path,
            filename=f"all_compliance_reports_{current_time.strftime('%Y%m%d_%H%M%S')}.pdf",
            media_type="application/pdf",
            background=background_tasks
        )

    except HTTPException:
        raise
    except Exception as e:
        LoggingHelper.error(f"Error generating comprehensive compliance PDF: {str(e)}",
                           user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e), "object_id": project_id})
        raise HTTPException(status_code=500, detail=str(e))
