from fastapi import APIRouter
from routes import file_processing_routes, project_management_routes, file_download_routes, compliance_routes, file_details_routes, templated_projects_routes, logging_routes, usage_data_routes

# Create main router
main_router = APIRouter()

# Include all route modules
main_router.include_router(file_processing_routes.router, tags=["File Processing"])
main_router.include_router(project_management_routes.router, tags=["Project Management"])
main_router.include_router(file_download_routes.router, tags=["File Download"])
main_router.include_router(compliance_routes.router, tags=["Compliance"])
main_router.include_router(file_details_routes.router, tags=["File Details"])
main_router.include_router(templated_projects_routes.router, tags=["Templated Projects"])
main_router.include_router(logging_routes.router, tags=["Logging Management"])
main_router.include_router(usage_data_routes.router, tags=["Usage Data"])
