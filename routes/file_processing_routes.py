import logging
from fastapi import APIRouter, UploadFile, Form, File, Request, HTTPException, Query, BackgroundTasks
from services.pdf_processor import PDFProcessor
from services.data_analysis import process_data_analysis
from services.mongo_manager import MongoManager
from fastapi.responses import FileResponse
from sse_starlette.sse import EventSourceResponse
import os
import config
from typing import List, Optional
from models import insight_model
import json as jp
import numpy as np
import asyncio
from bson import ObjectId
from services import utils
from services.unified_usage_tracking import create_unified_usage_logger
from services.logging_utils import LoggingHelper, get_logger
import pandas as pd
import json
import urllib
import aiofiles
from datetime import datetime, timezone
from services.utils import decode_jwt_token, get_compliance_report, generate_title
from exceptions.api_exceptions import handle_openai_error
from services.async_fs_utils import async_makedirs, async_remove, async_path_exists, async_rmtree
from starlette.concurrency import run_in_threadpool

from services.prompts import get_compliance_prompt
from io import BytesIO
import pandas as pd
from openpyxl import load_workbook

import uuid
from starlette.background import BackgroundTask
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.enums import TA_CENTER, TA_LEFT
import re

# Configure logging - will be replaced with project+user specific logger in routes
logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize services
mongo_manager = MongoManager(config.insights_collection, config.images_collection, config.tables_collection, config.compliance_collection)
pdf_processor = PDFProcessor(config.openai_client, mongo_manager)

# File processing routes will be moved here
@router.post(f"/{config.FileProcessUrl}/")
async def process_files(
    request: Request,
    file: UploadFile = File(None),
    prompt: str = Form(...),
    file_name: Optional[str] = Form(None),
    file_type: Optional[str] = Form(None),
    project_id: str = Form(...),
    user_id: str = Form(...),
    page_number: Optional[str] = Form(None),
    title: Optional[str] = Form(None),
    response_type: str = Form(...),
    visualization: bool = Form(False),
    custom_prompt_name: Optional[str] = Form(None),
):

    try:
        # Log file processing request started
        LoggingHelper.info("File processing request started", user_id=user_id, project_id=project_id,
                          extra_data={
                              "file_name": file.filename if file else None,
                              "response_type": response_type,
                              "custom_prompt_name": custom_prompt_name
                          })

        # User Authentication
        if config.VALIDATE_TOKEN:
            token = request.headers.get('Authorization')
            if not token or not token.startswith('Bearer '):
                LoggingHelper.error("Authorization header is missing or invalid", user_id=user_id, project_id=project_id)
                raise HTTPException(status_code=401, detail="Authorization header is missing or invalid")

            token = token.split(' ')[1]
            payload = utils.decode_jwt_token(token)
            if payload:
                user_email = payload['sub']
                is_valid_user = await mongo_manager.check_user_exists(user_email)
                if not is_valid_user:
                    LoggingHelper.error("User not found", user_id=user_id, project_id=project_id,
                                       extra_data={"user_email": user_email})
                    raise HTTPException(status_code=401, detail="Invalid user")
                else:
                    LoggingHelper.info("Valid User authenticated", user_id=user_id, project_id=project_id,
                                      extra_data={"user_email": user_email})
            else:
                LoggingHelper.error("Invalid token", user_id=user_id, project_id=project_id)
                raise HTTPException(status_code=401, detail="Invalid token")
        else:
            LoggingHelper.info("Token validation is turned OFF. Skipping authentication checks.",
                              user_id=user_id, project_id=project_id)

        # Initialize unified usage logger for comprehensive tracking early in the function
        usage_logger = create_unified_usage_logger(project_id=project_id, user_id=user_id, mongo_manager=mongo_manager)
        LoggingHelper.info("Unified usage tracking initialized", user_id=user_id, project_id=project_id,
                          extra_data={"usage_logger_initialized": True})
        usage_logger.log_step_summary("INITIALIZATION", "Unified usage logger created")



        # Check for restricted keywords only when prompt is not None or empty
        try:
            # Skip restricted content check if prompt is None or empty
            if not prompt or prompt.strip() == "":
                LoggingHelper.info("Skipping restricted content check for empty or None prompt",
                                  user_id=user_id, project_id=project_id)
                is_restricted = False
            # Skip restricted content check if using a custom prompt, but still check if it's in the format with |||
            elif custom_prompt_name and prompt == custom_prompt_name:
                # If custom_prompt_name has the format "category|||subcategory|||prompt_text", we should still check for restricted content
                if "|||" in custom_prompt_name:
                    LoggingHelper.info(f"Custom prompt contains ||| format, so checking for restricted content: {custom_prompt_name}",
                                      user_id=user_id, project_id=project_id,
                                      extra_data={"custom_prompt_name": custom_prompt_name})
                    is_restricted = await utils.check_restricted_keywords(prompt, project_id, mongo_manager, usage_logger, usage_logger)
                else:
                    LoggingHelper.info(f"Skipping restricted content check for standard custom prompt: {custom_prompt_name}",
                                      user_id=user_id, project_id=project_id,
                                      extra_data={"custom_prompt_name": custom_prompt_name})
                    is_restricted = False
            else:
                is_restricted = await utils.check_restricted_keywords(prompt, project_id, mongo_manager, usage_logger, usage_logger)

            if is_restricted:
                LoggingHelper.warning(f"Restricted content detected in query: {prompt}",
                                     user_id=user_id, project_id=project_id,
                                     extra_data={"prompt": prompt[:100]})

                restricted_message = "We apologize, but your query contains restricted or sensitive content that we cannot process. Please rephrase your question to avoid potentially harmful or inappropriate content."

                # Create the restricted response object
                restricted_response = {
                    'summary': restricted_message,
                    'tables': "",
                    'page_number': '',
                    'suggested_questions': [],
                    'files': [],
                    'images': "",
                    'input_tokens_used': '',
                    'output_tokens_used': '',
                    'response_id': str(ObjectId()),
                    'response_time': datetime.now(timezone.utc).isoformat(),
                    'response_type': response_type,
                    'source_type': ''
                }

                # Check if we should return directly or stream the response
                if custom_prompt_name and prompt == custom_prompt_name and file:
                    LoggingHelper.info(f"Returning restricted response directly for custom prompt with file: {custom_prompt_name}",
                                      user_id=user_id, project_id=project_id,
                                      extra_data={"custom_prompt_name": custom_prompt_name})
                    return restricted_response
                else:
                    # Create a streaming response for restricted content
                    async def restricted_content_stream():
                        # Stream message word by word
                        words = restricted_message.split()
                        for i, word in enumerate(words):
                            # Add space after each word except the last one
                            word_chunk = word + (" " if i < len(words) - 1 else "")
                            yield json.dumps({
                                "type": "message",
                                "content": word_chunk
                            })
                            # Add a small delay between words for smoother streaming
                            await asyncio.sleep(0.1)

                        # Then yield complete signal
                        yield json.dumps({"type": "complete"})

                        # Finally yield the full response object
                        yield json.dumps({
                            "type": "final_response",
                            "content": restricted_response
                        })

                    return EventSourceResponse(restricted_content_stream())
        except Exception as e:
            LoggingHelper.error(f"Error checking restricted keywords: {str(e)}",
                               user_id=user_id, project_id=project_id,
                               extra_data={"error": str(e)})
            # In case of error checking restrictions, continue with the normal processing
            # This is a defensive approach to not block user queries if the restriction check fails
            LoggingHelper.warning("Continuing with processing despite error in restricted content check",
                                 user_id=user_id, project_id=project_id)

        usage_logger.log_step_summary("RESTRICTED_KEYWORDS_CHECK", "Completed content moderation check")

        LoggingHelper.info("Processing file...", user_id=user_id, project_id=project_id)
        LoggingHelper.info("Request parameters", user_id=user_id, project_id=project_id,
                          extra_data={"file": file.filename if file else None, "prompt": prompt[:100] if prompt else None,
                                    "page_number": page_number, "title": title, "response_type": response_type,
                                    "custom_prompt_name": custom_prompt_name})

        # Check if this is a new project or an existing one
        project_data, exists, _ = await mongo_manager.get_or_initialize_project(project_id)

        # Handle title appropriately
        if title is None:
            if exists:
                # For existing projects, use the existing title if none is provided
                title = project_data.get("title", config.DEFAULT_PROJECT_TITLE)
                LoggingHelper.info(f"Using existing title: {title} for project {project_id}",
                                  user_id=user_id, project_id=project_id,
                                  extra_data={"title": title, "exists": exists})
            else:
                # For new projects, set default title
                title = config.DEFAULT_PROJECT_TITLE
                LoggingHelper.info(f"Set default title to '{config.DEFAULT_PROJECT_TITLE}' for new project",
                                  user_id=user_id, project_id=project_id,
                                  extra_data={"title": title, "exists": exists})

        if file:

            # Create project directory if it doesn't exist
            project_dir = os.path.join(config.file_path, project_id)
            await async_makedirs(project_dir, exist_ok=True)

            # Create subdirectories and store their paths
            data_analysis_dir = os.path.join(project_dir, config.data_analysis_folder_name)
            non_data_analysis_dir = os.path.join(project_dir, config.non_data_analysis_folder_name)

            # Create the directories
            await async_makedirs(data_analysis_dir, exist_ok=True)
            await async_makedirs(non_data_analysis_dir, exist_ok=True)

            LoggingHelper.info(f"Created dirs: {project_dir}, {data_analysis_dir}, {non_data_analysis_dir}",
                              user_id=user_id, project_id=project_id,
                              extra_data={"project_dir": project_dir, "data_analysis_dir": data_analysis_dir,
                                        "non_data_analysis_dir": non_data_analysis_dir})
            temp_file=file
            supported_filetypes = config.SUPPORTED_FILE_TYPES
            # Get the file name and content type
            file_name = file.filename
            # content_type = file.content_type.lower()

            # Get extension from filename
            file_type = file_name.split('.')[-1].lower() if '.' in file_name else ''

            if file_type not in supported_filetypes:
                LoggingHelper.error(f"Unsupported file type: {file_type}", user_id=user_id, project_id=project_id,
                                   extra_data={"file_type": file_type, "filename": file_name,
                                             "supported_types": supported_filetypes})
                raise HTTPException(status_code=415, detail=f"Unsupported file type: {file_type}")
            file_name_without_ext=file_name.split(".")[0]
            LoggingHelper.info(f"File type determined as: {file_type} from extension: {file_type} and filename is : {file_name_without_ext}",
                              user_id=user_id, project_id=project_id,
                              extra_data={"file_type": file_type, "filename": file_name, "file_name_without_ext": file_name_without_ext})
            # temp_file=file
            secret_key=config.secret_key


            if file_type in config.DOCUMENT_FILE_TYPES:

                # Read the file content before saving
                file_content = await file.read()
                encrypted_content = utils.encrypt_data(file_content, secret_key)

                # Save encrypted content to a file
                encrypted_file_name = f"{file_name_without_ext}.enc"
                file_path = os.path.join(non_data_analysis_dir, encrypted_file_name)
                async with aiofiles.open(file_path, "wb") as buffer:
                    await buffer.write(encrypted_content)
                LoggingHelper.info(f"Saved encrypted file to: {file_path}", user_id=user_id, project_id=project_id,
                                  extra_data={"file_path": file_path, "file_name": file_name})

                # Reset file position for subsequent reads
                await file.seek(0)

                previous_responses,previous_prompt="",""
                llm_call=False
                insight_data=""
                if file_type in ['doc', 'docx', 'txt']:
                    content = await file.read()

                    LoggingHelper.info("intent----------------------------file_type --------------------- %s",
                                      user_id=user_id, project_id=project_id,
                                      extra_data={"file_type": file_type})
                    if file_type=='txt':
                        extracted_text= await utils.read_txt_file(content)
                    elif file_type=='doc' or file_type=='docx':
                        extracted_text = await utils.read_doc_file(content)

                    page_number=[]
                    page_numbers=[]
                    valid_page_numbers=[]
                    extracted_text_per_page=[extracted_text]

                    LoggingHelper.info("intent----------------------------extracted_text_per_page --------------------- %s",
                                      user_id=user_id, project_id=project_id,
                                      extra_data={"extracted_text_per_page_length": len(extracted_text_per_page) if extracted_text_per_page else 0})

                else:
                    LoggingHelper.info(f"Extract text from file content", user_id=user_id, project_id=project_id)

                    # Get model for token-aware extraction
                    model = await mongo_manager.get_model_for_project(project_id)

                    if config.plain_txt_without_ocr:
                    # extracted_text, page_numbers, extracted_text_per_page, valid_page_numbers = await utils.extract_text_from_file(file, page_number)
                        print("in plain txt extraction")
                        # Use token-aware extraction
                        result = await utils.extract_text_from_file(
                            temp_file, page_number, prompt, project_id, mongo_manager, model
                        )
                        if len(result) == 5:  # Token-aware extraction
                            extracted_text, page_numbers, extracted_text_per_page, valid_page_numbers, token_info = result
                            LoggingHelper.info("Token-aware extraction completed for plain text",
                                             user_id=user_id, project_id=project_id, extra_data=token_info)
                        else:  # Fallback
                            extracted_text, page_numbers, extracted_text_per_page, valid_page_numbers = result
                    else:
                        # Create unique session-specific temp directory to prevent collisions
                        import uuid
                        session_id = uuid.uuid4().hex
                        TEMP_DIR = os.path.join(config.temp_path, f"session_{session_id}")
                        await async_makedirs(TEMP_DIR, exist_ok=True)

                        # Get project model for vision processing
                        project_model = await mongo_manager.get_model_for_project(project_id)

                        # Use token-aware PDF processing
                        result = await utils.upload_and_process_pdf(
                            temp_file, page_number, TEMP_DIR,
                            project_model=project_model,
                            token_logger=usage_logger,
                            user_id=user_id,
                            project_id=project_id,
                            user_prompt=prompt,
                            mongo_manager=mongo_manager
                        )

                        # Handle both token-aware and fallback results
                        if len(result) == 5:  # Token-aware extraction
                            extracted_text, page_numbers, extracted_text_per_page, valid_page_numbers, token_info = result
                            LoggingHelper.info("Token-aware extraction completed for PDF with OCR",
                                             user_id=user_id, project_id=project_id, extra_data=token_info)
                        else:  # Fallback
                            extracted_text, page_numbers, extracted_text_per_page, valid_page_numbers = result

                        # Clean up session directory after processing
                        try:
                            await async_rmtree(TEMP_DIR)
                            LoggingHelper.info(f"Successfully cleaned up temp directory: {TEMP_DIR}",
                                              user_id=user_id, project_id=project_id,
                                              extra_data={"temp_dir": TEMP_DIR})
                        except Exception as e:
                            LoggingHelper.warning(f"Failed to clean up temp directory {TEMP_DIR}: {e}",
                                                 user_id=user_id, project_id=project_id,
                                                 extra_data={"temp_dir": TEMP_DIR, "error": str(e)})

                    LoggingHelper.info("-----------------------------------", user_id=user_id, project_id=project_id)
                    LoggingHelper.info(f"Extracted text is{extracted_text}", user_id=user_id, project_id=project_id,
                                      extra_data={"extracted_text_length": len(extracted_text) if extracted_text else 0})
                    LoggingHelper.info("-----------------------------------", user_id=user_id, project_id=project_id)


                try:
                    # Ensure we use exactly the first 7500 characters for intent generation
                    text_for_intent = ''.join(extracted_text_per_page)[:7500] if isinstance(extracted_text_per_page, list) else str(extracted_text_per_page)[:7500]
                    LoggingHelper.info(f"Generating intent from {len(text_for_intent)} characters of extracted text",
                                      user_id=user_id, project_id=project_id,
                                      extra_data={"text_length": len(text_for_intent), "file_name": file_name})
                    intent = await utils.get_intent(mongo_manager, config.openai_client, text_for_intent, project_id, usage_logger, usage_logger)
                    # Only parse if intent is returned as a string
                    if isinstance(intent, str):
                        intent = jp.loads(intent)
                    LoggingHelper.info("Intent generated successfully in file processing route",
                                      user_id=user_id, project_id=project_id,
                                      extra_data={"file_name": file_name})
                    usage_logger.log_step_summary("INTENT_GENERATION", f"Generated intent for document file: {file_name}")
                except Exception as e:
                    LoggingHelper.error(f"Error getting intent from extracted text: {str(e)}",
                                       user_id=user_id, project_id=project_id,
                                       extra_data={"error": str(e), "file_name": file_name})
                    handle_openai_error(e)
                    intent = {"context_intent": {"intent_description": "Unable to determine intent", "keywords": []}}
                    usage_logger.log_step_summary("INTENT_GENERATION", f"Failed to generate intent for document file: {file_name}")

                pdf_file_path = non_data_analysis_dir

                # Construct the path
                pkl_file_path = os.path.join(
                    pdf_file_path, file_name.split(".")[0] + ".pkl"
                )
                print(pkl_file_path)
                await utils.put_pickle_file_context(pkl_file_path,{'texts': extracted_text_per_page, 'total_pages': len(extracted_text_per_page)})

                LoggingHelper.info("intent----------------------------context --------------------- %s",
                                  user_id=user_id, project_id=project_id,
                                  extra_data={"intent": str(intent)[:200] if intent else None})
                previous_responses,previous_prompt="",""
                llm_call=False
                # Use the project_data we already retrieved
                if not exists:
                    complete_chat=[]
                    # complete_chat.append({
                    #                 "filename": file_name or "",
                    #                 "query": prompt,
                    #                 "response": insight_data
                    #             })
                else:
                    complete_chat=project_data.get("complete_chat", [])
                LoggingHelper.info(f"Process pdf called with file_name: {file_name}",
                                  user_id=user_id, project_id=project_id,
                                  extra_data={"file_name": file_name})

                async def event_generator():
                    # Log streaming start for PDF processing (data analysis path)
                    LoggingHelper.info("STREAMING_START - PDF processing streaming initiated (data analysis path)",
                                      user_id=user_id, project_id=project_id,
                                      extra_data={
                                          "stream_type": "pdf_processing_data_analysis",
                                          "file_name": file_name,
                                          "file_type": file_type,
                                          "prompt_length": len(prompt) if prompt else 0,
                                          "response_type": response_type,
                                          "visualization": visualization,
                                          "custom_prompt_name": custom_prompt_name
                                      })

                    pdf_processor = PDFProcessor(config.openai_client, mongo_manager)
                    async for event in pdf_processor.process_pdf(
                        file=file,
                        prompt=prompt,
                        project_id=project_id,
                        user_id=user_id,
                        page_number=page_number,
                        file_name=file_name,
                        file_type=file_type,
                        title=title,
                        extracted_text=extracted_text,
                        page_numbers=page_numbers,
                        extracted_text_per_page=extracted_text_per_page,
                        valid_page_numbers=valid_page_numbers,
                        intent=intent,
                        previous_responses=previous_responses,
                        previous_prompt=previous_prompt,
                        llm_call=False,
                        insight_data="",
                        custom_prompt_name=custom_prompt_name,
                        response_type=response_type,
                        visualization=visualization,
                        complete_chat=complete_chat,
                        token_logger=usage_logger,
                        unified_logger=usage_logger,
                        source_type="file"
                    ):
                        LoggingHelper.info("Event: %s", user_id=user_id, project_id=project_id,
                                          extra_data={"event": str(event)[:100]})
                        jsonifed_event = json.loads(event)

                        if jsonifed_event["type"] == "final_response":
                            LoggingHelper.info(f"Type of event content: {type(jsonifed_event['content'])}",
                                              user_id=user_id, project_id=project_id,
                                              extra_data={"content_type": str(type(jsonifed_event['content']))})
                            yield jsonifed_event['content']  # Yield the content instead of returning
                        else:
                            yield {
                                "type": "message",
                                "content": event  # Wrap the original event in a JSON object
                            }

                # Collect results from the event generator
                response_content = []
                async for result in event_generator():
                    response_content.append(result)

                # Save usage events to MongoDB before returning
                usage_logger.log_step_summary("PROCESSING_COMPLETE", "PDF file processing completed successfully")
                try:
                    await usage_logger.save_to_mongodb()
                    LoggingHelper.info("Usage events saved to MongoDB for PDF processing path",
                                      user_id=user_id, project_id=project_id)
                except Exception as e:
                    LoggingHelper.error(f"Failed to save usage events to MongoDB for PDF processing: {str(e)}",
                                       user_id=user_id, project_id=project_id,
                                       extra_data={"error": str(e)})

                # Return the final response
                return response_content[0]

            elif file_type in config.DATA_ANALYSIS_FILE_TYPES:
                try:
                    # Read the file content as binary
                    file_content = await file.read()

                    # Encrypt the binary content using your encryption method
                    encrypted_content = utils.encrypt_data(file_content, secret_key)

                    # Save the encrypted content to a new file
                    encrypted_file_name = f"{file_name_without_ext}.enc"
                    encrypted_file_path = os.path.join(data_analysis_dir, encrypted_file_name)
                    async with aiofiles.open(encrypted_file_path, "wb") as buffer:
                        await buffer.write(encrypted_content)

                    LoggingHelper.info(f"Saved encrypted file to: {encrypted_file_path}",
                                      user_id=user_id, project_id=project_id,
                                      extra_data={"encrypted_file_path": encrypted_file_path, "file_name": file_name})

                    # If the file is CSV, you may want to process it as follows
                    if file_type == 'csv':
                        try:
                            # Wrap CSV processing in run_in_threadpool to avoid blocking
                            def _process_csv():
                                return pd.read_csv(BytesIO(file_content))

                            csv_data = await run_in_threadpool(_process_csv)
                            # Process csv_data as needed (e.g., analysis or further encryption)
                            LoggingHelper.info("CSV file processed successfully.", user_id=user_id, project_id=project_id,
                                              extra_data={"file_name": file_name})
                        except Exception as e:
                            LoggingHelper.error(f"Error processing CSV: {e}", user_id=user_id, project_id=project_id,
                                               extra_data={"error": str(e), "file_name": file_name})

                    # If the file is Excel (XLSX), use openpyxl to handle it
                    elif file_type == 'xlsx':
                        try:
                            # Wrap Excel processing in run_in_threadpool to avoid blocking
                            def _process_excel():
                                return load_workbook(BytesIO(file_content))

                            workbook = await run_in_threadpool(_process_excel)
                            # Process the workbook as needed
                            LoggingHelper.info("Excel file processed successfully.", user_id=user_id, project_id=project_id,
                                              extra_data={"file_name": file_name})
                        except Exception as e:
                            LoggingHelper.error(f"Error processing Excel file: {e}", user_id=user_id, project_id=project_id,
                                               extra_data={"error": str(e), "file_name": file_name})

                    # Reset file position for any subsequent reads
                    await file.seek(0)

                except Exception as e:
                    LoggingHelper.error(f"Error reading file: {e}", user_id=user_id, project_id=project_id,
                                       extra_data={"error": str(e), "file_name": file_name})
                # Save file in project-specific directory
                file_path = os.path.join(data_analysis_dir, file_name)
                file_content = await file.read()
                async with aiofiles.open(file_path, "wb") as buffer:
                    await buffer.write(file_content)
                LoggingHelper.info(f"Saved file to local path: {file_path}", user_id=user_id, project_id=project_id,
                                  extra_data={"file_path": file_path, "file_name": file_name})

                try:
                    if file_type.lower() in config.DATA_ANALYSIS_FILE_TYPES:
                        # Wrap pandas operations in run_in_threadpool to avoid blocking
                        def _process_data_file():
                            if file_type.lower() == 'csv':
                                df = pd.read_csv(file_path)
                            else:  # xlsx
                                df = pd.read_excel(file_path)
                            return df.head().to_string()

                        file_text = await run_in_threadpool(_process_data_file)

                except Exception as e:
                    LoggingHelper.error(f"Error reading file: {e}", user_id=user_id, project_id=project_id,
                                       extra_data={"error": str(e), "file_name": file_name})
                    raise HTTPException(status_code=500, detail="Error reading file")

                try:
                    # Ensure we use exactly the first 7500 characters for intent generation
                    text_for_intent = str(file_text)[:7500]
                    LoggingHelper.info(f"Generating intent from {len(text_for_intent)} characters of data analysis file text",
                                      user_id=user_id, project_id=project_id,
                                      extra_data={"text_length": len(text_for_intent), "file_name": file_name})
                    intent = await utils.get_intent(mongo_manager, config.openai_client, text_for_intent, project_id, usage_logger, usage_logger)
                    # Only parse if intent is returned as a string
                    if isinstance(intent, str):
                        intent = json.loads(intent)
                    LoggingHelper.info(f"Intent generated successfully for data analysis mode: {intent}",
                                      user_id=user_id, project_id=project_id,
                                      extra_data={"intent": str(intent)[:200] if intent else None, "file_name": file_name})
                    usage_logger.log_step_summary("INTENT_GENERATION", f"Generated intent for data analysis file: {file_name}")
                except Exception as e:
                    LoggingHelper.error(f"Error getting intent for data analysis: {str(e)}",
                                       user_id=user_id, project_id=project_id,
                                       extra_data={"error": str(e), "file_name": file_name})
                    handle_openai_error(e)
                    intent = {"context_intent": {"intent_description": "Unable to determine intent", "keywords": []}}
                    usage_logger.log_step_summary("INTENT_GENERATION", f"Failed to generate intent for data analysis file: {file_name}")

                # Replace direct call with streaming response
                response = await process_data_analysis(
                            file_path, file_type, file_name, prompt,
                            project_id, user_id, title, response_type,
                            intent, "", visualization, custom_prompt_name,
                            usage_logger, usage_logger
                        )

                # Save usage events to MongoDB before returning
                usage_logger.log_step_summary("PROCESSING_COMPLETE", "Data analysis file processing completed successfully")
                try:
                    await usage_logger.save_to_mongodb()
                    LoggingHelper.info("Usage events saved to MongoDB for data analysis file upload path",
                                      user_id=user_id, project_id=project_id)
                except Exception as e:
                    LoggingHelper.error(f"Failed to save usage events to MongoDB for data analysis file upload: {str(e)}",
                                       user_id=user_id, project_id=project_id,
                                       extra_data={"error": str(e)})

                return response


        else:
            LoggingHelper.info("Entered else block in process file route", user_id=user_id, project_id=project_id)
            fileInDb = True
            extracted_text=""

            # We already have project_data from earlier

            # Get model for token-aware extraction
            model = await mongo_manager.get_model_for_project(project_id)

            # Try token-aware extraction first
            try:
                result = await utils.extract_text_from_existing_project(
                    project_data, page_number, project_id, file_name,
                    prompt, mongo_manager, model
                )
                if len(result) == 5:  # Token-aware extraction
                    page_numbers, previous_responses, previous_prompt, intent, token_info = result
                    LoggingHelper.info("Token-aware extraction completed for existing project",
                                     user_id=user_id, project_id=project_id, extra_data=token_info)
                else:  # Fallback
                    page_numbers, previous_responses, previous_prompt, intent = result
            except Exception as e:
                LoggingHelper.warning(f"Token-aware extraction failed, using fallback: {str(e)}",
                                    user_id=user_id, project_id=project_id)
                # Fallback to original method
                page_numbers, previous_responses, previous_prompt, intent = await utils.extract_text_from_existing_project(
                    project_data, page_number, project_id, file_name
                )

            valid_page_numbers = page_numbers
            LoggingHelper.info(f"Length of previous_responses before removing images: {len(previous_responses)}",
                              user_id=user_id, project_id=project_id,
                              extra_data={"previous_responses_length": len(previous_responses)})
            # Remove images from each response in previous_responses
            for response in previous_responses:
                if 'images' in response:
                    del response['images']
            LoggingHelper.info("Removed images from previous_responses", user_id=user_id, project_id=project_id)
            LoggingHelper.info(f"Length of previous_responses after removing images: {len(previous_responses)}",
                              user_id=user_id, project_id=project_id,
                              extra_data={"previous_responses_length_after": len(previous_responses)})
            filesearch_descriptions,dataset_descriptions=utils.get_files_and_description(intent)

            chat_history = [{p: r} for p, r in zip(previous_prompt, previous_responses)]
            # complete_chat=[{p: r} for p, r in zip(previous_prompt, previous_responses)]

            source_to_select=await utils.get_source(chat_history,prompt,filesearch_descriptions,dataset_descriptions,project_id,mongo_manager,usage_logger,usage_logger)
            LoggingHelper.info(f"source_to_select: {source_to_select}", user_id=user_id, project_id=project_id,
                              extra_data={"source_to_select": str(source_to_select)[:200] if source_to_select else None})
            # print("source_to_select",source_to_select)
            LoggingHelper.info(f"type of source_to_select: {type(source_to_select)}", user_id=user_id, project_id=project_id,
                              extra_data={"source_type": str(type(source_to_select))})
            # Only parse if it's returned as a string
            if isinstance(source_to_select, str):
                source_to_select = jp.loads(source_to_select)
            LoggingHelper.info(f"source_to_select after processing: {source_to_select}", user_id=user_id, project_id=project_id,
                              extra_data={"source_to_select_processed": str(source_to_select)[:200] if source_to_select else None})
            usage_logger.log_step_summary("SOURCE_SELECTION", f"Selected source: {source_to_select.get('intent', 'unknown')}")

            llm_call=False
            try:
                insight_data=""
                LoggingHelper.info(f"source_to_select['source'][0]['FileTool']: {source_to_select['source'][0]['FileTool']}",
                                  user_id=user_id, project_id=project_id,
                                  extra_data={"file_tool": source_to_select['source'][0]['FileTool']})
                LoggingHelper.info(f"source_to_select['source'][0]['WebSeach']: {source_to_select['source'][0]['WebSeach']}",
                                  user_id=user_id, project_id=project_id,
                                  extra_data={"web_search": source_to_select['source'][0]['WebSeach']})
                LoggingHelper.info(f"source_to_select['source'][0]['SelfAnswer']: {source_to_select['source'][0]['SelfAnswer']}",
                                  user_id=user_id, project_id=project_id,
                                  extra_data={"self_answer": source_to_select['source'][0]['SelfAnswer']})
                if source_to_select['source'][0]['FileTool'] == True:
                    extracted_text = []
                    files_for_data_analysis = []

                    # Check file type of first item to determine priority
                    first_item = source_to_select['source'][0]['Filename'][0]
                    first_ext = first_item.split(".")[1]
                    is_data_analysis_priority = first_ext in config.DATA_ANALYSIS_FILE_TYPES
                    LoggingHelper.info(f"is_data_analysis_priority: {is_data_analysis_priority}",
                                      user_id=user_id, project_id=project_id,
                                      extra_data={"is_data_analysis_priority": is_data_analysis_priority, "first_ext": first_ext})

                    for item in source_to_select['source'][0]['Filename']:
                        LoggingHelper.info(f"file to get context of {item}", user_id=user_id, project_id=project_id,
                                          extra_data={"file_item": item})
                        ext = item.split(".")[1]

                        if is_data_analysis_priority:
                            if ext in config.DATA_ANALYSIS_FILE_TYPES:
                                files_for_data_analysis.append(item)
                        else:
                            if ext in config.DOCUMENT_FILE_TYPES:
                                # Construct the pickle path correctly
                                pkl_file_path = os.path.join(
                                    config.file_path,
                                    project_id,
                                    config.non_data_analysis_folder_name,
                                    item.split(".")[0] + ".pkl"
                                )
                                LoggingHelper.info(f"Attempting to read pickle file from: {pkl_file_path}",
                                                  user_id=user_id, project_id=project_id,
                                                  extra_data={"pkl_file_path": pkl_file_path, "item": item})

                                try:
                                    file_context = await utils.get_pickle_file_context(pkl_file_path, valid_page_numbers)
                                    if file_context:
                                        extracted_text.append(file_context)
                                        LoggingHelper.info(f"Successfully read context from pickle file",
                                                          user_id=user_id, project_id=project_id,
                                                          extra_data={"pkl_file_path": pkl_file_path})
                                    else:
                                        LoggingHelper.warning(f"No context found in pickle file: {pkl_file_path}",
                                                             user_id=user_id, project_id=project_id,
                                                             extra_data={"pkl_file_path": pkl_file_path})
                                except Exception as e:
                                    LoggingHelper.error(f"Error reading pickle file {pkl_file_path}: {e}",
                                                       user_id=user_id, project_id=project_id,
                                                       extra_data={"error": str(e), "pkl_file_path": pkl_file_path})
                                    continue

                    # Return appropriate response based on priority
                    if is_data_analysis_priority and files_for_data_analysis:
                        LoggingHelper.info(f"process data analysis called with files_for_data_analysis: {files_for_data_analysis}",
                                          user_id=user_id, project_id=project_id,
                                          extra_data={"files_for_data_analysis": files_for_data_analysis})

                        # Note: Usage events will be saved inside process_data_analysis function
                        usage_logger.log_step_summary("EXISTING_DATA_ANALYSIS_FILES", "Processing existing data analysis files")

                        return await process_data_analysis(None, None, None, prompt, project_id, user_id, title, response_type, "", files_for_data_analysis, visualization, None, usage_logger, usage_logger)


                elif source_to_select['source'][0]['WebSeach'] == True:
                    LoggingHelper.info("Processing web search", user_id=user_id, project_id=project_id)
                    usage_logger.log_step_summary("WEB_SEARCH_START", "Starting web search operation")
                    if config.paid_search:
                        LoggingHelper.info("Calling SERPER search", user_id=user_id, project_id=project_id)
                        extracted_text= await utils.perform_search(prompt, search_logger=usage_logger, unified_logger=usage_logger, user_id=user_id, project_id=project_id)
                    else:
                        LoggingHelper.info("Calling DUCKDUCKGO search", user_id=user_id, project_id=project_id)
                        extracted_text = await utils.duckduckgo_query(prompt, search_logger=usage_logger, unified_logger=usage_logger, user_id=user_id, project_id=project_id)
                    if extracted_text!= False:
                        LoggingHelper.info("Successfully extracted text from web search", user_id=user_id, project_id=project_id)
                        llm_call = False
                        insight_data = ""

                        # Initialize complete_chat based on project status
                        # Use the project_data we already retrieved
                        if not exists:
                            complete_chat = []
                        else:
                            complete_chat = project_data.get("complete_chat", [])

                        LoggingHelper.info(f"Length of extracted text: {len(extracted_text)}",
                                          user_id=user_id, project_id=project_id,
                                          extra_data={"extracted_text_length": len(extracted_text)})
                        LoggingHelper.info("--------------------PAGES, web search---------------------",
                                          user_id=user_id, project_id=project_id)
                        LoggingHelper.info(f"Valid page numbers: {valid_page_numbers}",
                                          user_id=user_id, project_id=project_id,
                                          extra_data={"valid_page_numbers": valid_page_numbers})

                        extracted_text_per_page, valid_page_numbers = "", 0

                        async def event_generator():
                            nonlocal llm_call, insight_data
                            try:
                                # Log streaming start for web search path
                                LoggingHelper.info("STREAMING_START - Web search streaming initiated",
                                                  user_id=user_id, project_id=project_id,
                                                  extra_data={
                                                      "stream_type": "web_search",
                                                      "source_type": "web_search",
                                                      "query": source_to_select['query'][:100] if source_to_select.get('query') else None,
                                                      "response_type": response_type,
                                                      "visualization": visualization
                                                  })

                                usage_logger.log_step_summary("MAIN_LLM_RESPONSE", "Starting main LLM response generation for web search")
                                async for event in utils.fake_llm_resonse(
                                    source_to_select['query'], project_id,
                                    mongo_manager, extracted_text, chat_history,
                                    valid_page_numbers, response_type,
                                    visualization, datetime.now(timezone.utc).date(),
                                    "web search", usage_logger, user_id, usage_logger
                                ):
                                    event_data = json.loads(event)
                                    if event_data["type"] == "final_response":
                                        insight_data = event_data["content"]
                                        if isinstance(insight_data, str):
                                            insight_data = json.loads(insight_data)

                                        # Now process through pdf_processor using the main flow
                                        async for pdf_event in pdf_processor.process_pdf(
                                            file=file,
                                            prompt=prompt,
                                            project_id=project_id,
                                            user_id=user_id,
                                            page_number=page_number,
                                            file_name=file_name,
                                            file_type=file_type,
                                            title=title,
                                            extracted_text=extracted_text,
                                            page_numbers=page_numbers,
                                            extracted_text_per_page=extracted_text_per_page,
                                            valid_page_numbers=valid_page_numbers,
                                            intent=intent,
                                            previous_responses=previous_responses,
                                            previous_prompt=previous_prompt,
                                            llm_call=llm_call,
                                            insight_data=insight_data,
                                            custom_prompt_name = None,
                                            response_type=response_type,
                                            visualization=visualization,
                                            complete_chat=complete_chat,
                                            token_logger=usage_logger,
                                            unified_logger=usage_logger,
                                            source_type="web_search"
                                        ):
                                            yield pdf_event
                                    else:
                                        yield event
                            except Exception as e:
                                LoggingHelper.error(f"Error in event generator: {str(e)}",
                                                   user_id=user_id, project_id=project_id,
                                                   extra_data={"error": str(e)})
                                # Instead of raising exception, yield error event to frontend
                                try:
                                    handle_openai_error(e)
                                except Exception as api_error:
                                    # Yield error event that frontend can handle
                                    yield json.dumps({
                                        "type": "error",
                                        "content": str(api_error)
                                    })
                                    return  # Stop the generator
                            finally:
                                # Save usage events to MongoDB after streaming completes
                                usage_logger.log_step_summary("PROCESSING_COMPLETE", "Web search processing completed successfully")
                                try:
                                    await usage_logger.save_to_mongodb()
                                    LoggingHelper.info("Usage events saved to MongoDB for web search path",
                                                      user_id=user_id, project_id=project_id)
                                except Exception as e:
                                    LoggingHelper.error(f"Failed to save usage events to MongoDB for web search: {str(e)}",
                                                       user_id=user_id, project_id=project_id,
                                                       extra_data={"error": str(e)})



                        return EventSourceResponse(event_generator())



                    else:
                        LoggingHelper.info("Failed to extract text from web search. Falling back to use self answer.",
                                          user_id=user_id, project_id=project_id)
                        LoggingHelper.info("Processing self answer", user_id=user_id, project_id=project_id)
                        extracted_text = ""
                        llm_call = False
                        insight_data = ""

                        # Initialize complete_chat based on project status
                        # Use the project_data we already retrieved
                        if not exists:
                            complete_chat = []
                        else:
                            complete_chat = project_data.get("complete_chat", [])

                        LoggingHelper.info("--------------------PAGES, self answer---------------------",
                                          user_id=user_id, project_id=project_id)
                        LoggingHelper.info(f"Valid page numbers: {valid_page_numbers}",
                                          user_id=user_id, project_id=project_id,
                                          extra_data={"valid_page_numbers": valid_page_numbers})

                        extracted_text_per_page, valid_page_numbers = "", 0

                        async def event_generator():
                            nonlocal llm_call, insight_data
                            try:
                                # Log streaming start for self answer fallback path
                                LoggingHelper.info("STREAMING_START - Self answer fallback streaming initiated",
                                                  user_id=user_id, project_id=project_id,
                                                  extra_data={
                                                      "stream_type": "self_answer_fallback",
                                                      "source_type": "self_answer",
                                                      "query": source_to_select['query'][:100] if source_to_select.get('query') else None,
                                                      "response_type": response_type,
                                                      "visualization": visualization,
                                                      "reason": "web_search_failed"
                                                  })

                                usage_logger.log_step_summary("MAIN_LLM_RESPONSE", "Starting main LLM response generation for self answer (fallback)")
                                async for event in utils.fake_llm_resonse(
                                    source_to_select['query'], project_id,
                                    mongo_manager, extracted_text, chat_history,
                                    valid_page_numbers, response_type,
                                    visualization, datetime.now(timezone.utc).date(),
                                    "", usage_logger, user_id, usage_logger
                                ):
                                    event_data = json.loads(event)
                                    if event_data["type"] == "final_response":
                                        insight_data = event_data["content"]
                                        if isinstance(insight_data, str):
                                            insight_data = json.loads(insight_data)

                                        # Now process through pdf_processor using the main flow
                                        async for pdf_event in pdf_processor.process_pdf(
                                            file=file,
                                            prompt=prompt,
                                            project_id=project_id,
                                            user_id=user_id,
                                            page_number=page_number,
                                            file_name=file_name,
                                            file_type=file_type,
                                            title=title,
                                            extracted_text=extracted_text,
                                            page_numbers=page_numbers,
                                            extracted_text_per_page=extracted_text_per_page,
                                            valid_page_numbers=valid_page_numbers,
                                            intent=intent,
                                            previous_responses=previous_responses,
                                            previous_prompt=previous_prompt,
                                            llm_call=llm_call,
                                            insight_data=insight_data,
                                            custom_prompt_name = None,
                                            response_type=response_type,
                                            visualization=visualization,
                                            complete_chat=complete_chat,
                                            token_logger=usage_logger,
                                            unified_logger=usage_logger,
                                            source_type="self_answer"
                                        ):
                                            yield pdf_event
                                    else:
                                        yield event
                            except Exception as e:
                                LoggingHelper.error(f"Error in event generator: {str(e)}",
                                                   user_id=user_id, project_id=project_id,
                                                   extra_data={"error": str(e)})
                                # Instead of raising exception, yield error event to frontend
                                try:
                                    handle_openai_error(e)
                                except Exception as api_error:
                                    # Yield error event that frontend can handle
                                    yield json.dumps({
                                        "type": "error",
                                        "content": str(api_error)
                                    })
                                    return  # Stop the generator
                            finally:
                                # Save usage events to MongoDB after streaming completes
                                usage_logger.log_step_summary("PROCESSING_COMPLETE", "Self answer (fallback) processing completed successfully")
                                try:
                                    await usage_logger.save_to_mongodb()
                                    LoggingHelper.info("Usage events saved to MongoDB for self answer (fallback) path",
                                                      user_id=user_id, project_id=project_id)
                                except Exception as e:
                                    LoggingHelper.error(f"Failed to save usage events to MongoDB for self answer (fallback): {str(e)}",
                                                       user_id=user_id, project_id=project_id,
                                                       extra_data={"error": str(e)})



                        return EventSourceResponse(event_generator())



                elif source_to_select['source'][0]['SelfAnswer'] == True:
                    LoggingHelper.info("Processing self answer", user_id=user_id, project_id=project_id)
                    extracted_text = ""
                    llm_call = False
                    insight_data = ""

                    # Initialize complete_chat based on project status
                    # Use the project_data we already retrieved
                    if not exists:
                        complete_chat = []
                    else:
                        complete_chat = project_data.get("complete_chat", [])

                    LoggingHelper.info("--------------------PAGES, self answer---------------------",
                                      user_id=user_id, project_id=project_id)
                    LoggingHelper.info(f"Valid page numbers: {valid_page_numbers}",
                                      user_id=user_id, project_id=project_id,
                                      extra_data={"valid_page_numbers": valid_page_numbers})

                    extracted_text_per_page, valid_page_numbers = "", 0

                    async def event_generator():
                        nonlocal llm_call, insight_data
                        try:
                            # Log streaming start for self answer path
                            LoggingHelper.info("STREAMING_START - Self answer streaming initiated",
                                              user_id=user_id, project_id=project_id,
                                              extra_data={
                                                  "stream_type": "self_answer",
                                                  "source_type": "self_answer",
                                                  "query": source_to_select['query'][:100] if source_to_select.get('query') else None,
                                                  "response_type": response_type,
                                                  "visualization": visualization
                                              })

                            usage_logger.log_step_summary("MAIN_LLM_RESPONSE", "Starting main LLM response generation for self answer")
                            async for event in utils.fake_llm_resonse(
                                source_to_select['query'], project_id,
                                mongo_manager, extracted_text, chat_history,
                                valid_page_numbers, response_type,
                                visualization, datetime.now(timezone.utc).date(),
                                "", usage_logger, user_id, usage_logger
                            ):
                                event_data = json.loads(event)
                                if event_data["type"] == "final_response":
                                    insight_data = event_data["content"]
                                    if isinstance(insight_data, str):
                                        insight_data = json.loads(insight_data)

                                    # Now process through pdf_processor using the main flow
                                    async for pdf_event in pdf_processor.process_pdf(
                                        file=file,
                                        prompt=prompt,
                                        project_id=project_id,
                                        user_id=user_id,
                                        page_number=page_number,
                                        file_name=file_name,
                                        file_type=file_type,
                                        title=title,
                                        extracted_text=extracted_text,
                                        page_numbers=page_numbers,
                                        extracted_text_per_page=extracted_text_per_page,
                                        valid_page_numbers=valid_page_numbers,
                                        intent=intent,
                                        previous_responses=previous_responses,
                                        previous_prompt=previous_prompt,
                                        llm_call=llm_call,
                                        insight_data=insight_data,
                                        custom_prompt_name = None,
                                        response_type=response_type,
                                        visualization=visualization,
                                        complete_chat=complete_chat,
                                        token_logger=usage_logger,
                                        unified_logger=usage_logger,
                                        source_type="self_answer"
                                    ):
                                        yield pdf_event
                                else:
                                    yield event
                        except Exception as e:
                            LoggingHelper.error(f"Error in event generator: {str(e)}",
                                               user_id=user_id, project_id=project_id,
                                               extra_data={"error": str(e)})
                            # Instead of raising exception, yield error event to frontend
                            try:
                                handle_openai_error(e)
                            except Exception as api_error:
                                # Yield error event that frontend can handle
                                yield json.dumps({
                                    "type": "error",
                                    "content": str(api_error)
                                })
                                return  # Stop the generator
                        finally:
                            # Save usage events to MongoDB after streaming completes
                            usage_logger.log_step_summary("PROCESSING_COMPLETE", "Self answer processing completed successfully")
                            try:
                                await usage_logger.save_to_mongodb()
                                LoggingHelper.info("Usage events saved to MongoDB for self answer path",
                                                  user_id=user_id, project_id=project_id)
                            except Exception as e:
                                LoggingHelper.error(f"Failed to save usage events to MongoDB for self answer: {str(e)}",
                                                   user_id=user_id, project_id=project_id,
                                                   extra_data={"error": str(e)})

                    return EventSourceResponse(event_generator())

            except Exception as e:
                    LoggingHelper.error(f"Error in web search: {str(e)}",
                                       user_id=user_id, project_id=project_id,
                                       extra_data={"error": str(e)})

                    LoggingHelper.info("Switching to search in recently updated file",
                                      user_id=user_id, project_id=project_id)
            # Use the project_data we already retrieved
            if not exists:
                complete_chat=[]
                # complete_chat.append({
                #                 "filename": file_name or "",
                #                 "query": prompt,
                #                 "response": insight_data
                #             })
            else:
                complete_chat=project_data.get("complete_chat", [])
            print("file_context",len(extracted_text))

            print("--------------------PAGES, use existing file---------------------")
            print(valid_page_numbers)

            # if not llm_call:
            extracted_text_per_page,valid_page_numbers="",0

            async def event_generator():
                try:
                    # Log streaming start for PDF processing
                    LoggingHelper.info("STREAMING_START - PDF processing streaming initiated",
                                      user_id=user_id, project_id=project_id,
                                      extra_data={
                                          "stream_type": "pdf_processing",
                                          "file_name": file_name,
                                          "file_type": file_type,
                                          "prompt_length": len(prompt) if prompt else 0,
                                          "response_type": response_type,
                                          "visualization": visualization,
                                          "llm_call": llm_call
                                      })

                    async for event in pdf_processor.process_pdf(
                        file=file,
                        prompt=prompt,
                        project_id=project_id,
                        user_id=user_id,
                        page_number=page_number,
                        file_name=file_name,
                        file_type=file_type,
                        title=title,
                        extracted_text=extracted_text,
                        page_numbers=page_numbers,
                        extracted_text_per_page=extracted_text_per_page,
                        valid_page_numbers=valid_page_numbers,
                        intent=intent,
                        previous_responses=previous_responses,
                        previous_prompt=previous_prompt,
                        llm_call=llm_call,
                        insight_data=insight_data,
                        custom_prompt_name = None,
                        response_type=response_type,
                        visualization=visualization,
                        complete_chat=complete_chat,
                        token_logger=usage_logger,
                        unified_logger=usage_logger,
                        source_type="file"
                    ):
                        yield event
                finally:
                    # Log final usage consumption summary and save to MongoDB
                    usage_logger.log_step_summary("PROCESSING_COMPLETE", "File processing completed successfully")

                    # Save usage events to MongoDB
                    try:
                        await usage_logger.save_to_mongodb()
                        LoggingHelper.info("Usage events saved to MongoDB for final event generator path",
                                          user_id=user_id, project_id=project_id)
                    except Exception as e:
                        LoggingHelper.error(f"Failed to save usage events to MongoDB: {str(e)}",
                                           user_id=user_id, project_id=project_id,
                                           extra_data={"error": str(e)})



        return EventSourceResponse(event_generator())

    except Exception as e:
        LoggingHelper.error(f"Error in process_files: {str(e)}",
                           user_id=user_id, project_id=project_id,
                           extra_data={"error": str(e)})
        # Save usage events even on error if usage logger exists
        if 'usage_logger' in locals():
            # Generate a response_id for error scenario if not already set
            if not usage_logger.response_id:
                error_response_id = str(ObjectId())
                usage_logger.set_response_id(error_response_id)
                LoggingHelper.info(f"Generated response_id for error scenario: {error_response_id}",
                                  user_id=user_id, project_id=project_id,
                                  extra_data={"error_response_id": error_response_id})

            usage_logger.log_step_summary("PROCESSING_ERROR", f"File processing failed: {str(e)}")
            try:
                await usage_logger.save_to_mongodb()
                LoggingHelper.info("Usage events saved to MongoDB on error",
                                  user_id=user_id, project_id=project_id)
            except Exception as save_error:
                LoggingHelper.error(f"Failed to save usage events to MongoDB on error: {str(save_error)}",
                                   user_id=user_id, project_id=project_id,
                                   extra_data={"save_error": str(save_error)})
        raise HTTPException(status_code=500, detail=str(e))

