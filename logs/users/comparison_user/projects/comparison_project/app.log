2025-06-13 10:30:35,026 - INFO - project_user.comparison_user.comparison_project - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250613_050035_025440
2025-06-13 10:30:35,026 - INFO - project_user.comparison_user.comparison_project - 
2025-06-13 10:30:36,274 - INFO - project_user.comparison_user.comparison_project - USAGE_CONSUMPTION - Category: llm | Provider: openai | Operation: comparison | Subtype: openai_test | Input Tokens: 19 | Output Tokens: 7 | Total Tokens: 26 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250613_050035_025440, service_category: llm, service_provider: openai, service_tier: paid, operation_type: comparison, operation_subtype: openai_test, input_tokens: 19, output_tokens: 7, total_tokens: 26, success: True, timestamp: 2025-06-13T05:00:36.274539+00:00
2025-06-13 10:30:36,274 - INFO - project_user.comparison_user.comparison_project - 
2025-06-13 10:30:36,304 - INFO - project_user.comparison_user.comparison_project - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250613_050036_304364
2025-06-13 10:30:36,304 - INFO - project_user.comparison_user.comparison_project - 
2025-06-13 10:30:40,568 - INFO - project_user.comparison_user.comparison_project - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: comparison | Subtype: deepseek_test | Input Tokens: 16 | Output Tokens: 7 | Total Tokens: 23 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250613_050036_304364, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: comparison, operation_subtype: deepseek_test, input_tokens: 16, output_tokens: 7, total_tokens: 23, success: True, timestamp: 2025-06-13T05:00:40.568422+00:00
2025-06-13 10:30:40,569 - INFO - project_user.comparison_user.comparison_project - 
2025-06-13 10:30:40,574 - INFO - project_user.comparison_user.comparison_project - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250613_050040_574417
2025-06-13 10:30:40,574 - INFO - project_user.comparison_user.comparison_project - 
2025-06-13 10:30:42,437 - INFO - project_user.comparison_user.comparison_project - USAGE_CONSUMPTION - Category: vision | Provider: other | Operation: comparison | Subtype: gemini_test | Input Tokens: 16 | Output Tokens: 0 | Total Tokens: 16 | Running Total Operations: 1 | Success: False
  -> query_id: unified_query_20250613_050040_574417, service_category: vision, service_provider: other, service_tier: unknown, operation_type: comparison, operation_subtype: gemini_test, input_tokens: 16, output_tokens: 0, total_tokens: 16, success: False, timestamp: 2025-06-13T05:00:42.436781+00:00
2025-06-13 10:30:42,438 - INFO - project_user.comparison_user.comparison_project - 
