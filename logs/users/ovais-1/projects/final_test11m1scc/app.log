2025-06-12 11:17:25,424 - INFO - project_user.ovais-1.final_test11m1scc - File processing request started
  -> file_name: None, response_type: Brief, custom_prompt_name: None
2025-06-12 11:17:25,424 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:25,424 - INFO - project_user.ovais-1.final_test11m1scc - Token validation is turned OFF. Skipping authentication checks.
2025-06-12 11:17:25,425 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:25,425 - INFO - project_user.ovais-1.final_test11m1scc - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250612_054725_425234
2025-06-12 11:17:25,425 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:25,425 - INFO - project_user.ovais-1.final_test11m1scc - Unified usage tracking initialized
  -> usage_logger_initialized: True
2025-06-12 11:17:25,425 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:25,425 - INFO - project_user.ovais-1.final_test11m1scc - USAGE_STEP_SUMMARY - INITIALIZATION | Total Operations: 0 | Cumulative Input Tokens: 0 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 0 | Unified usage logger created
  -> step_name: INITIALIZATION, total_operations: 0, cumulative_input_tokens: 0, cumulative_output_tokens: 0, cumulative_total_tokens: 0, additional_info: Unified usage logger created
2025-06-12 11:17:25,425 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:33,551 - INFO - project_user.ovais-1.final_test11m1scc - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: content_moderation | Subtype: restricted_keywords_check | Input Tokens: 70 | Output Tokens: 1 | Total Tokens: 71 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250612_054725_425234, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: content_moderation, operation_subtype: restricted_keywords_check, input_tokens: 70, output_tokens: 1, total_tokens: 71, success: True, timestamp: 2025-06-12T05:47:33.551517+00:00
2025-06-12 11:17:33,552 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:33,553 - INFO - project_user.ovais-1.final_test11m1scc - USAGE_STEP_SUMMARY - RESTRICTED_KEYWORDS_CHECK | Total Operations: 1 | Cumulative Input Tokens: 70 | Cumulative Output Tokens: 1 | Cumulative Total Tokens: 71 | Completed content moderation check
  -> step_name: RESTRICTED_KEYWORDS_CHECK, total_operations: 1, cumulative_input_tokens: 70, cumulative_output_tokens: 1, cumulative_total_tokens: 71, additional_info: Completed content moderation check
2025-06-12 11:17:33,553 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:33,553 - INFO - project_user.ovais-1.final_test11m1scc - Processing file...
2025-06-12 11:17:33,553 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:33,553 - INFO - project_user.ovais-1.final_test11m1scc - Request parameters
  -> file: None, prompt: What is GDPR, page_number: None, title: None, response_type: Brief, custom_prompt_name: None
2025-06-12 11:17:33,553 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:33,689 - INFO - project_user.ovais-1.final_test11m1scc - Set default title to 'Untitled' for new project
  -> title: Untitled, exists: False
2025-06-12 11:17:33,689 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:33,690 - INFO - project_user.ovais-1.final_test11m1scc - Entered else block in process file route
2025-06-12 11:17:33,690 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:33,750 - INFO - project_user.ovais-1.final_test11m1scc - Length of previous_responses before removing images: 0
  -> previous_responses_length: 0
2025-06-12 11:17:33,751 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:33,751 - INFO - project_user.ovais-1.final_test11m1scc - Removed images from previous_responses
2025-06-12 11:17:33,751 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:33,752 - INFO - project_user.ovais-1.final_test11m1scc - Length of previous_responses after removing images: 0
  -> previous_responses_length_after: 0
2025-06-12 11:17:33,752 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:47,014 - INFO - project_user.ovais-1.final_test11m1scc - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: query_routing | Subtype: source_selection | Input Tokens: 1105 | Output Tokens: 127 | Total Tokens: 1232 | Running Total Operations: 2 | Success: True
  -> query_id: unified_query_20250612_054725_425234, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: query_routing, operation_subtype: source_selection, input_tokens: 1105, output_tokens: 127, total_tokens: 1232, success: True, timestamp: 2025-06-12T05:47:47.014452+00:00
2025-06-12 11:17:47,014 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:47,014 - INFO - project_user.ovais-1.final_test11m1scc - source_to_select: {'intent': 'self-answer', 'query': 'What is GDPR?', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': 'GDPR stands for General Data Protection Regulation. It is a regulation in EU law on data protection and privacy in the European Union and the European Economic Area. It also addresses the transfer of personal data outside the EU and EEA areas. The GDPR aims to give individuals control over their personal data and to simplify the regulatory environment for international business by unifying the regulation within the EU.'}
  -> source_to_select: {'intent': 'self-answer', 'query': 'What is GDPR?', 'source': [{'Filename': [], 'FileTool': False...
2025-06-12 11:17:47,014 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:47,015 - INFO - project_user.ovais-1.final_test11m1scc - type of source_to_select: <class 'dict'>
  -> source_type: <class 'dict'>
2025-06-12 11:17:47,015 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:47,015 - INFO - project_user.ovais-1.final_test11m1scc - source_to_select after processing: {'intent': 'self-answer', 'query': 'What is GDPR?', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': 'GDPR stands for General Data Protection Regulation. It is a regulation in EU law on data protection and privacy in the European Union and the European Economic Area. It also addresses the transfer of personal data outside the EU and EEA areas. The GDPR aims to give individuals control over their personal data and to simplify the regulatory environment for international business by unifying the regulation within the EU.'}
  -> source_to_select_processed: {'intent': 'self-answer', 'query': 'What is GDPR?', 'source': [{'Filename': [], 'FileTool': False...
2025-06-12 11:17:47,015 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:47,015 - INFO - project_user.ovais-1.final_test11m1scc - USAGE_STEP_SUMMARY - SOURCE_SELECTION | Total Operations: 2 | Cumulative Input Tokens: 1175 | Cumulative Output Tokens: 128 | Cumulative Total Tokens: 1303 | Selected source: self-answer
  -> step_name: SOURCE_SELECTION, total_operations: 2, cumulative_input_tokens: 1175, cumulative_output_tokens: 128, cumulative_total_tokens: 1303, additional_info: Selected source: self-answer
2025-06-12 11:17:47,015 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:47,015 - INFO - project_user.ovais-1.final_test11m1scc - source_to_select['source'][0]['FileTool']: False
  -> file_tool: False
2025-06-12 11:17:47,015 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:47,016 - INFO - project_user.ovais-1.final_test11m1scc - source_to_select['source'][0]['WebSeach']: False
  -> web_search: False
2025-06-12 11:17:47,016 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:47,016 - INFO - project_user.ovais-1.final_test11m1scc - source_to_select['source'][0]['SelfAnswer']: True
  -> self_answer: True
2025-06-12 11:17:47,016 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:47,016 - INFO - project_user.ovais-1.final_test11m1scc - Processing self answer
2025-06-12 11:17:47,016 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:47,016 - INFO - project_user.ovais-1.final_test11m1scc - --------------------PAGES, self answer---------------------
2025-06-12 11:17:47,016 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:47,016 - INFO - project_user.ovais-1.final_test11m1scc - Valid page numbers: []
  -> valid_page_numbers: []
2025-06-12 11:17:47,016 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:47,017 - INFO - project_user.ovais-1.final_test11m1scc - STREAMING_START - Self answer streaming initiated
  -> stream_type: self_answer, source_type: self_answer, query: What is GDPR?, response_type: Brief, visualization: False
2025-06-12 11:17:47,017 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:47,017 - INFO - project_user.ovais-1.final_test11m1scc - USAGE_STEP_SUMMARY - MAIN_LLM_RESPONSE | Total Operations: 2 | Cumulative Input Tokens: 1175 | Cumulative Output Tokens: 128 | Cumulative Total Tokens: 1303 | Starting main LLM response generation for self answer
  -> step_name: MAIN_LLM_RESPONSE, total_operations: 2, cumulative_input_tokens: 1175, cumulative_output_tokens: 128, cumulative_total_tokens: 1303, additional_info: Starting main LLM response generation for self answer
2025-06-12 11:17:47,017 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:47,018 - INFO - project_user.ovais-1.final_test11m1scc - STREAMING_START - Non-data analysis real-time streaming initiated
  -> stream_type: non_data_analysis_realtime, function: fake_llm_resonse, source_type: , prompt_length: 13, extracted_text_length: 0, chat_history_length: 0, response_type: Brief, visualization: False
2025-06-12 11:17:47,018 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:47,104 - INFO - project_user.ovais-1.final_test11m1scc - Starting real-time insight generation
2025-06-12 11:17:47,105 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:50,278 - INFO - project_user.ovais-1.final_test11m1scc - Received chunk #1
  -> chunk_length: 0, chunk_preview: EMPTY
2025-06-12 11:17:50,278 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:50,279 - INFO - project_user.ovais-1.final_test11m1scc - Received chunk #2
  -> chunk_length: 3, chunk_preview: ```
2025-06-12 11:17:50,279 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:51,211 - INFO - project_user.ovais-1.final_test11m1scc - Received chunk #3
  -> chunk_length: 4, chunk_preview: json
2025-06-12 11:17:51,211 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:51,294 - INFO - project_user.ovais-1.final_test11m1scc - Received chunk #4
  -> chunk_length: 1, chunk_preview: 

2025-06-12 11:17:51,294 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:17:51,373 - INFO - project_user.ovais-1.final_test11m1scc - Received chunk #5
  -> chunk_length: 2, chunk_preview: {

2025-06-12 11:17:51,374 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:09,022 - INFO - project_user.ovais-1.final_test11m1scc - Summary streaming completed - boundary detected
  -> summary_length: 2047
2025-06-12 11:18:09,022 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:12,716 - INFO - project_user.ovais-1.final_test11m1scc - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: insight_generation | Subtype: realtime_streaming | Input Tokens: 1464 | Output Tokens: 491 | Total Tokens: 1955 | Running Total Operations: 3 | Success: True
  -> query_id: unified_query_20250612_054725_425234, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: insight_generation, operation_subtype: realtime_streaming, input_tokens: 1464, output_tokens: 491, total_tokens: 1955, success: True, timestamp: 2025-06-12T05:48:12.716177+00:00
2025-06-12 11:18:12,716 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:12,716 - INFO - project_user.ovais-1.final_test11m1scc - Real-time streaming completed successfully
  -> total_content_length: 2473, summary_length: 2039, streaming_summary: True, summary_complete: True, content_preview: ```json
{
    "summary": "**Understanding the General Data Protection Regulation (GDPR)**\n\nThe ..., content_ends_with: with GDPR?",
        "Can you provide examples of significant fines imposed under GDPR?"
    ]
}
```, total_chunks_received: 498
2025-06-12 11:18:12,716 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:12,717 - WARNING - project_user.ovais-1.final_test11m1scc - Failed to parse streamed content as JSON: Content is not JSON format: line 1 column 1 (char 0)
  -> content_length: 2473, content_preview: ```json
{
    "summary": "**Understanding the General Data Protection Regulation (GDPR)**\n\nThe ...
2025-06-12 11:18:12,717 - WARNING - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:12,717 - INFO - project_user.ovais-1.final_test11m1scc - Using already streamed content for fallback response
  -> streamed_content_length: 2039
2025-06-12 11:18:12,717 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:12,717 - INFO - project_user.ovais-1.final_test11m1scc - Created fallback structured response from extracted summary
  -> summary_length: 2039
2025-06-12 11:18:12,717 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:12,783 - INFO - project_user.ovais-1.final_test11m1scc - No file provided, use existing data from MongoDB for pdf processing
2025-06-12 11:18:12,783 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:12,784 - INFO - project_user.ovais-1.final_test11m1scc - file_context: 0
  -> extracted_text_length: 0
2025-06-12 11:18:12,784 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:12,784 - INFO - project_user.ovais-1.final_test11m1scc - valid_page_numbers incase of no file provided in request: []
  -> valid_page_numbers: []
2025-06-12 11:18:12,784 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:12,784 - INFO - project_user.ovais-1.final_test11m1scc - Entered not llm call block in pdf processor
2025-06-12 11:18:12,784 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:12,784 - INFO - project_user.ovais-1.final_test11m1scc - Using existing insight_data - skipping LLM call to prevent duplication
  -> insight_data_available: True, source_type: self_answer, insight_data_type: dict, has_summary: True
2025-06-12 11:18:12,784 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:12,785 - INFO - project_user.ovais-1.final_test11m1scc - Putting images as '' in insights data as it will be empty in modes other than data analysis.
2025-06-12 11:18:12,785 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:12,785 - INFO - project_user.ovais-1.final_test11m1scc - Added token usage to insight_data before saving - Input: 2639, Output: 619, Total: 3258
  -> input_tokens: 2639, output_tokens: 619, total_tokens: 3258, response_id: 684a6a1c1f46546fdbdd11dd
2025-06-12 11:18:12,785 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:12,785 - INFO - project_user.ovais-1.final_test11m1scc - Linked token events with response_id: 684a6a1c1f46546fdbdd11dd
  -> response_id: 684a6a1c1f46546fdbdd11dd
2025-06-12 11:18:12,785 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:12,785 - INFO - project_user.ovais-1.final_test11m1scc - Saving project data in mongo db insights collection for pdf processor
2025-06-12 11:18:12,785 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:12,786 - INFO - project_user.ovais-1.final_test11m1scc - Saving project data in mongo db
2025-06-12 11:18:12,786 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:12,845 - INFO - project_user.ovais-1.final_test11m1scc - Project exists in mongo db
2025-06-12 11:18:12,845 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:12,845 - INFO - project_user.ovais-1.final_test11m1scc - File does not exist
2025-06-12 11:18:12,845 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:12,930 - INFO - project_user.ovais-1.final_test11m1scc - Project data saved in mongo db when file does not exist
2025-06-12 11:18:12,930 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:13,006 - INFO - project_user.ovais-1.final_test11m1scc - Saved project data in mongo db for pdf processor
2025-06-12 11:18:13,006 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:13,006 - INFO - project_user.ovais-1.final_test11m1scc - Replacing the tables in insight data with actual table data for response sending
2025-06-12 11:18:13,006 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:13,072 - INFO - project_user.ovais-1.final_test11m1scc - Token usage already included in saved data - Input: 2639, Output: 619
  -> input_tokens: 2639, output_tokens: 619
2025-06-12 11:18:13,073 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:13,074 - INFO - project_user.ovais-1.final_test11m1scc - USAGE_STEP_SUMMARY - PROCESSING_COMPLETE | Total Operations: 3 | Cumulative Input Tokens: 2639 | Cumulative Output Tokens: 619 | Cumulative Total Tokens: 3258 | Self answer processing completed successfully
  -> step_name: PROCESSING_COMPLETE, total_operations: 3, cumulative_input_tokens: 2639, cumulative_output_tokens: 619, cumulative_total_tokens: 3258, additional_info: Self answer processing completed successfully
2025-06-12 11:18:13,074 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:13,075 - INFO - project_user.ovais-1.final_test11m1scc - Saving unified usage events
  -> event_count: 3, query_id: unified_query_20250612_054725_425234
2025-06-12 11:18:13,075 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:13,148 - INFO - project_user.ovais-1.final_test11m1scc - Successfully saved unified usage events
  -> document_id: 684a6a1d1f46546fdbdd11de, total_operations: 3, total_tokens: 3258
2025-06-12 11:18:13,149 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:13,150 - INFO - project_user.ovais-1.final_test11m1scc - Usage events linked to response
  -> response_id: 684a6a1c1f46546fdbdd11dd
2025-06-12 11:18:13,150 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:13,150 - INFO - project_user.ovais-1.final_test11m1scc - Successfully saved 3 usage events to MongoDB
  -> mongodb_id: 684a6a1d1f46546fdbdd11de, events_count: 3, response_id: 684a6a1c1f46546fdbdd11dd, query_id: unified_query_20250612_054725_425234
2025-06-12 11:18:13,150 - INFO - project_user.ovais-1.final_test11m1scc - 
2025-06-12 11:18:13,151 - INFO - project_user.ovais-1.final_test11m1scc - Usage events saved to MongoDB for self answer path
2025-06-12 11:18:13,151 - INFO - project_user.ovais-1.final_test11m1scc - 
