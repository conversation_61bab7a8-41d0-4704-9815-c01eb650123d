2025-06-12 12:32:49,698 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - File processing request started
  -> file_name: None, response_type: Brief, custom_prompt_name: None
2025-06-12 12:32:49,698 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:32:49,698 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Token validation is turned OFF. Skipping authentication checks.
2025-06-12 12:32:49,698 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:32:49,698 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250612_070249_698480
2025-06-12 12:32:49,698 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:32:49,698 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Unified usage tracking initialized
  -> usage_logger_initialized: True
2025-06-12 12:32:49,698 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:32:49,698 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - USAGE_STEP_SUMMARY - INITIALIZATION | Total Operations: 0 | Cumulative Input Tokens: 0 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 0 | Unified usage logger created
  -> step_name: INITIALIZATION, total_operations: 0, cumulative_input_tokens: 0, cumulative_output_tokens: 0, cumulative_total_tokens: 0, additional_info: Unified usage logger created
2025-06-12 12:32:49,698 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:32:57,639 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: content_moderation | Subtype: restricted_keywords_check | Input Tokens: 81 | Output Tokens: 1 | Total Tokens: 82 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250612_070249_698480, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: content_moderation, operation_subtype: restricted_keywords_check, input_tokens: 81, output_tokens: 1, total_tokens: 82, success: True, timestamp: 2025-06-12T07:02:57.639151+00:00
2025-06-12 12:32:57,639 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:32:57,640 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - USAGE_STEP_SUMMARY - RESTRICTED_KEYWORDS_CHECK | Total Operations: 1 | Cumulative Input Tokens: 81 | Cumulative Output Tokens: 1 | Cumulative Total Tokens: 82 | Completed content moderation check
  -> step_name: RESTRICTED_KEYWORDS_CHECK, total_operations: 1, cumulative_input_tokens: 81, cumulative_output_tokens: 1, cumulative_total_tokens: 82, additional_info: Completed content moderation check
2025-06-12 12:32:57,640 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:32:57,640 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Processing file...
2025-06-12 12:32:57,640 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:32:57,640 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Request parameters
  -> file: None, prompt: create a bar chart for units sold in east region from some dummy data, page_number: None, title: None, response_type: Brief, custom_prompt_name: None
2025-06-12 12:32:57,640 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:32:57,731 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Set default title to 'Untitled' for new project
  -> title: Untitled, exists: False
2025-06-12 12:32:57,731 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:32:57,731 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Entered else block in process file route
2025-06-12 12:32:57,731 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:32:57,777 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Length of previous_responses before removing images: 0
  -> previous_responses_length: 0
2025-06-12 12:32:57,777 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:32:57,777 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Removed images from previous_responses
2025-06-12 12:32:57,777 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:32:57,777 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Length of previous_responses after removing images: 0
  -> previous_responses_length_after: 0
2025-06-12 12:32:57,777 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:16,817 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: query_routing | Subtype: source_selection | Input Tokens: 1116 | Output Tokens: 47 | Total Tokens: 1163 | Running Total Operations: 2 | Success: True
  -> query_id: unified_query_20250612_070249_698480, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: query_routing, operation_subtype: source_selection, input_tokens: 1116, output_tokens: 47, total_tokens: 1163, success: True, timestamp: 2025-06-12T07:03:16.817860+00:00
2025-06-12 12:33:16,818 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:16,818 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - source_to_select: {'intent': 'general_query', 'query': '', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': ''}
  -> source_to_select: {'intent': 'general_query', 'query': '', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach...
2025-06-12 12:33:16,818 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:16,818 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - type of source_to_select: <class 'dict'>
  -> source_type: <class 'dict'>
2025-06-12 12:33:16,819 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:16,819 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - source_to_select after processing: {'intent': 'general_query', 'query': '', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': ''}
  -> source_to_select_processed: {'intent': 'general_query', 'query': '', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach...
2025-06-12 12:33:16,819 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:16,819 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - USAGE_STEP_SUMMARY - SOURCE_SELECTION | Total Operations: 2 | Cumulative Input Tokens: 1197 | Cumulative Output Tokens: 48 | Cumulative Total Tokens: 1245 | Selected source: general_query
  -> step_name: SOURCE_SELECTION, total_operations: 2, cumulative_input_tokens: 1197, cumulative_output_tokens: 48, cumulative_total_tokens: 1245, additional_info: Selected source: general_query
2025-06-12 12:33:16,819 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:16,819 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - source_to_select['source'][0]['FileTool']: False
  -> file_tool: False
2025-06-12 12:33:16,819 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:16,820 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - source_to_select['source'][0]['WebSeach']: False
  -> web_search: False
2025-06-12 12:33:16,820 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:16,820 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - source_to_select['source'][0]['SelfAnswer']: True
  -> self_answer: True
2025-06-12 12:33:16,820 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:16,820 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Processing self answer
2025-06-12 12:33:16,820 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:16,820 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - --------------------PAGES, self answer---------------------
2025-06-12 12:33:16,820 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:16,820 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Valid page numbers: []
  -> valid_page_numbers: []
2025-06-12 12:33:16,820 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:16,822 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - STREAMING_START - Self answer streaming initiated
  -> stream_type: self_answer, source_type: self_answer, query: None, response_type: Brief, visualization: False
2025-06-12 12:33:16,822 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:16,822 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - USAGE_STEP_SUMMARY - MAIN_LLM_RESPONSE | Total Operations: 2 | Cumulative Input Tokens: 1197 | Cumulative Output Tokens: 48 | Cumulative Total Tokens: 1245 | Starting main LLM response generation for self answer
  -> step_name: MAIN_LLM_RESPONSE, total_operations: 2, cumulative_input_tokens: 1197, cumulative_output_tokens: 48, cumulative_total_tokens: 1245, additional_info: Starting main LLM response generation for self answer
2025-06-12 12:33:16,822 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:16,822 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - STREAMING_START - Non-data analysis real-time streaming initiated
  -> stream_type: non_data_analysis_realtime, function: fake_llm_resonse, source_type: , prompt_length: 0, extracted_text_length: 0, chat_history_length: 0, response_type: Brief, visualization: False
2025-06-12 12:33:16,822 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:16,888 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Starting real-time insight generation
2025-06-12 12:33:16,888 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:20,946 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Received chunk #1
  -> chunk_length: 0, chunk_preview: EMPTY
2025-06-12 12:33:20,946 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:20,947 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Received chunk #2
  -> chunk_length: 3, chunk_preview: ```
2025-06-12 12:33:20,947 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:22,135 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Received chunk #3
  -> chunk_length: 4, chunk_preview: json
2025-06-12 12:33:22,135 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:22,222 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Received chunk #4
  -> chunk_length: 1, chunk_preview: 

2025-06-12 12:33:22,223 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:22,313 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Received chunk #5
  -> chunk_length: 2, chunk_preview: {

2025-06-12 12:33:22,313 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:28,177 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Summary streaming completed - boundary detected
  -> summary_length: 557
2025-06-12 12:33:28,178 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,628 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: insight_generation | Subtype: realtime_streaming | Input Tokens: 1460 | Output Tokens: 186 | Total Tokens: 1646 | Running Total Operations: 3 | Success: True
  -> query_id: unified_query_20250612_070249_698480, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: insight_generation, operation_subtype: realtime_streaming, input_tokens: 1460, output_tokens: 186, total_tokens: 1646, success: True, timestamp: 2025-06-12T07:03:31.628606+00:00
2025-06-12 12:33:31,628 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,629 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Real-time streaming completed successfully
  -> total_content_length: 912, summary_length: 533, streaming_summary: True, summary_complete: True, content_preview: ```json
{
    "summary": "**Welcome to Neuquip Buddy**\n\nHello! I'm Neuquip Buddy, your expert i..., content_ends_with: for showing data trends?",
        "How can I improve the accuracy of my data analysis?"
    ]
}
```, total_chunks_received: 195
2025-06-12 12:33:31,629 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,629 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Removed markdown code block wrapper from JSON
  -> original_length: 912, cleaned_length: 900
2025-06-12 12:33:31,629 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,629 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - JSON detection check
  -> is_json_like: True, starts_with_brace: True, ends_with_brace: True, content_start: {
    "summary": "**, content_end: a analysis?"
    ]
}
2025-06-12 12:33:31,629 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,629 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Transformed table data to match expected schema
  -> original_table_count: 0, transformed_table_count: 0, original_keys: [], transformed_keys: []
2025-06-12 12:33:31,629 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,630 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Skipped title addition - content was already streamed
  -> summary_length: 557
2025-06-12 12:33:31,630 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,630 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Successfully parsed streamed content as structured JSON
  -> summary_length: 557, tables_count: 0, questions_count: 4
2025-06-12 12:33:31,630 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,716 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - No file provided, use existing data from MongoDB for pdf processing
2025-06-12 12:33:31,716 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,716 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - file_context: 0
  -> extracted_text_length: 0
2025-06-12 12:33:31,716 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,717 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - valid_page_numbers incase of no file provided in request: []
  -> valid_page_numbers: []
2025-06-12 12:33:31,717 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,717 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Entered not llm call block in pdf processor
2025-06-12 12:33:31,717 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,717 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Using existing insight_data - skipping LLM call to prevent duplication
  -> insight_data_available: True, source_type: self_answer, insight_data_type: dict, has_summary: True
2025-06-12 12:33:31,717 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,717 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Putting images as '' in insights data as it will be empty in modes other than data analysis.
2025-06-12 12:33:31,717 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,717 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Added token usage to insight_data before saving - Input: 2657, Output: 234, Total: 2891
  -> input_tokens: 2657, output_tokens: 234, total_tokens: 2891, response_id: 684a7bc3ca474006387ce84f
2025-06-12 12:33:31,718 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,718 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Linked token events with response_id: 684a7bc3ca474006387ce84f
  -> response_id: 684a7bc3ca474006387ce84f
2025-06-12 12:33:31,718 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,718 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Saving project data in mongo db insights collection for pdf processor
2025-06-12 12:33:31,718 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,718 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Saving project data in mongo db
2025-06-12 12:33:31,719 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,806 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Project exists in mongo db
2025-06-12 12:33:31,806 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,806 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - File does not exist
2025-06-12 12:33:31,806 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,896 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Project data saved in mongo db when file does not exist
2025-06-12 12:33:31,897 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,986 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Saved project data in mongo db for pdf processor
2025-06-12 12:33:31,986 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:31,986 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Replacing the tables in insight data with actual table data for response sending
2025-06-12 12:33:31,987 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:32,072 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Token usage already included in saved data - Input: 2657, Output: 234
  -> input_tokens: 2657, output_tokens: 234
2025-06-12 12:33:32,072 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:32,073 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - USAGE_STEP_SUMMARY - PROCESSING_COMPLETE | Total Operations: 3 | Cumulative Input Tokens: 2657 | Cumulative Output Tokens: 234 | Cumulative Total Tokens: 2891 | Self answer processing completed successfully
  -> step_name: PROCESSING_COMPLETE, total_operations: 3, cumulative_input_tokens: 2657, cumulative_output_tokens: 234, cumulative_total_tokens: 2891, additional_info: Self answer processing completed successfully
2025-06-12 12:33:32,073 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:32,073 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Saving unified usage events
  -> event_count: 3, query_id: unified_query_20250612_070249_698480
2025-06-12 12:33:32,073 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:32,162 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Successfully saved unified usage events
  -> document_id: 684a7bc4ca474006387ce850, total_operations: 3, total_tokens: 2891
2025-06-12 12:33:32,163 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:32,163 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Usage events linked to response
  -> response_id: 684a7bc3ca474006387ce84f
2025-06-12 12:33:32,163 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:32,164 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Successfully saved 3 usage events to MongoDB
  -> mongodb_id: 684a7bc4ca474006387ce850, events_count: 3, response_id: 684a7bc3ca474006387ce84f, query_id: unified_query_20250612_070249_698480
2025-06-12 12:33:32,164 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
2025-06-12 12:33:32,164 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - Usage events saved to MongoDB for self answer path
2025-06-12 12:33:32,165 - INFO - project_user.ovais-1.final_test11m1sccnvnsd - 
