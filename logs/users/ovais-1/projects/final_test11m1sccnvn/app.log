2025-06-12 11:28:59,019 - INFO - project_user.ovais-1.final_test11m1sccnvn - File processing request started
  -> file_name: None, response_type: Brief, custom_prompt_name: None
2025-06-12 11:28:59,019 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:28:59,020 - INFO - project_user.ovais-1.final_test11m1sccnvn - Token validation is turned OFF. Skipping authentication checks.
2025-06-12 11:28:59,020 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:28:59,020 - INFO - project_user.ovais-1.final_test11m1sccnvn - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250612_055859_020158
2025-06-12 11:28:59,020 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:28:59,020 - INFO - project_user.ovais-1.final_test11m1sccnvn - Unified usage tracking initialized
  -> usage_logger_initialized: True
2025-06-12 11:28:59,020 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:28:59,020 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_STEP_SUMMARY - INITIALIZATION | Total Operations: 0 | Cumulative Input Tokens: 0 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 0 | Unified usage logger created
  -> step_name: INITIALIZATION, total_operations: 0, cumulative_input_tokens: 0, cumulative_output_tokens: 0, cumulative_total_tokens: 0, additional_info: Unified usage logger created
2025-06-12 11:28:59,020 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:06,046 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: content_moderation | Subtype: restricted_keywords_check | Input Tokens: 70 | Output Tokens: 1 | Total Tokens: 71 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250612_055859_020158, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: content_moderation, operation_subtype: restricted_keywords_check, input_tokens: 70, output_tokens: 1, total_tokens: 71, success: True, timestamp: 2025-06-12T05:59:06.046382+00:00
2025-06-12 11:29:06,046 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:06,047 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_STEP_SUMMARY - RESTRICTED_KEYWORDS_CHECK | Total Operations: 1 | Cumulative Input Tokens: 70 | Cumulative Output Tokens: 1 | Cumulative Total Tokens: 71 | Completed content moderation check
  -> step_name: RESTRICTED_KEYWORDS_CHECK, total_operations: 1, cumulative_input_tokens: 70, cumulative_output_tokens: 1, cumulative_total_tokens: 71, additional_info: Completed content moderation check
2025-06-12 11:29:06,047 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:06,047 - INFO - project_user.ovais-1.final_test11m1sccnvn - Processing file...
2025-06-12 11:29:06,047 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:06,047 - INFO - project_user.ovais-1.final_test11m1sccnvn - Request parameters
  -> file: None, prompt: What is GDPR, page_number: None, title: None, response_type: Brief, custom_prompt_name: None
2025-06-12 11:29:06,047 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:06,206 - INFO - project_user.ovais-1.final_test11m1sccnvn - Set default title to 'Untitled' for new project
  -> title: Untitled, exists: False
2025-06-12 11:29:06,206 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:06,206 - INFO - project_user.ovais-1.final_test11m1sccnvn - Entered else block in process file route
2025-06-12 11:29:06,206 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:06,294 - INFO - project_user.ovais-1.final_test11m1sccnvn - Length of previous_responses before removing images: 0
  -> previous_responses_length: 0
2025-06-12 11:29:06,294 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:06,294 - INFO - project_user.ovais-1.final_test11m1sccnvn - Removed images from previous_responses
2025-06-12 11:29:06,294 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:06,295 - INFO - project_user.ovais-1.final_test11m1sccnvn - Length of previous_responses after removing images: 0
  -> previous_responses_length_after: 0
2025-06-12 11:29:06,295 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:18,982 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: query_routing | Subtype: source_selection | Input Tokens: 1105 | Output Tokens: 148 | Total Tokens: 1253 | Running Total Operations: 2 | Success: True
  -> query_id: unified_query_20250612_055859_020158, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: query_routing, operation_subtype: source_selection, input_tokens: 1105, output_tokens: 148, total_tokens: 1253, success: True, timestamp: 2025-06-12T05:59:18.982588+00:00
2025-06-12 11:29:18,982 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:18,982 - INFO - project_user.ovais-1.final_test11m1sccnvn - source_to_select: {'intent': 'self-answer', 'query': 'What is GDPR?', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': 'The General Data Protection Regulation (GDPR) is a regulation in EU law on data protection and privacy in the European Union and the European Economic Area. It also addresses the transfer of personal data outside the EU and EEA areas. The GDPR aims to give individuals control over their personal data and to simplify the regulatory environment for international business by unifying the regulation within the EU. It was adopted on 14 April 2016 and became enforceable on 25 May 2018.'}
  -> source_to_select: {'intent': 'self-answer', 'query': 'What is GDPR?', 'source': [{'Filename': [], 'FileTool': False...
2025-06-12 11:29:18,983 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:18,983 - INFO - project_user.ovais-1.final_test11m1sccnvn - type of source_to_select: <class 'dict'>
  -> source_type: <class 'dict'>
2025-06-12 11:29:18,983 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:18,983 - INFO - project_user.ovais-1.final_test11m1sccnvn - source_to_select after processing: {'intent': 'self-answer', 'query': 'What is GDPR?', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': 'The General Data Protection Regulation (GDPR) is a regulation in EU law on data protection and privacy in the European Union and the European Economic Area. It also addresses the transfer of personal data outside the EU and EEA areas. The GDPR aims to give individuals control over their personal data and to simplify the regulatory environment for international business by unifying the regulation within the EU. It was adopted on 14 April 2016 and became enforceable on 25 May 2018.'}
  -> source_to_select_processed: {'intent': 'self-answer', 'query': 'What is GDPR?', 'source': [{'Filename': [], 'FileTool': False...
2025-06-12 11:29:18,983 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:18,983 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_STEP_SUMMARY - SOURCE_SELECTION | Total Operations: 2 | Cumulative Input Tokens: 1175 | Cumulative Output Tokens: 149 | Cumulative Total Tokens: 1324 | Selected source: self-answer
  -> step_name: SOURCE_SELECTION, total_operations: 2, cumulative_input_tokens: 1175, cumulative_output_tokens: 149, cumulative_total_tokens: 1324, additional_info: Selected source: self-answer
2025-06-12 11:29:18,983 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:18,983 - INFO - project_user.ovais-1.final_test11m1sccnvn - source_to_select['source'][0]['FileTool']: False
  -> file_tool: False
2025-06-12 11:29:18,983 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:18,983 - INFO - project_user.ovais-1.final_test11m1sccnvn - source_to_select['source'][0]['WebSeach']: False
  -> web_search: False
2025-06-12 11:29:18,983 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:18,984 - INFO - project_user.ovais-1.final_test11m1sccnvn - source_to_select['source'][0]['SelfAnswer']: True
  -> self_answer: True
2025-06-12 11:29:18,984 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:18,984 - INFO - project_user.ovais-1.final_test11m1sccnvn - Processing self answer
2025-06-12 11:29:18,984 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:18,984 - INFO - project_user.ovais-1.final_test11m1sccnvn - --------------------PAGES, self answer---------------------
2025-06-12 11:29:18,984 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:18,984 - INFO - project_user.ovais-1.final_test11m1sccnvn - Valid page numbers: []
  -> valid_page_numbers: []
2025-06-12 11:29:18,984 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:18,985 - INFO - project_user.ovais-1.final_test11m1sccnvn - STREAMING_START - Self answer streaming initiated
  -> stream_type: self_answer, source_type: self_answer, query: What is GDPR?, response_type: Brief, visualization: False
2025-06-12 11:29:18,985 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:18,986 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_STEP_SUMMARY - MAIN_LLM_RESPONSE | Total Operations: 2 | Cumulative Input Tokens: 1175 | Cumulative Output Tokens: 149 | Cumulative Total Tokens: 1324 | Starting main LLM response generation for self answer
  -> step_name: MAIN_LLM_RESPONSE, total_operations: 2, cumulative_input_tokens: 1175, cumulative_output_tokens: 149, cumulative_total_tokens: 1324, additional_info: Starting main LLM response generation for self answer
2025-06-12 11:29:18,986 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:18,986 - INFO - project_user.ovais-1.final_test11m1sccnvn - STREAMING_START - Non-data analysis real-time streaming initiated
  -> stream_type: non_data_analysis_realtime, function: fake_llm_resonse, source_type: , prompt_length: 13, extracted_text_length: 0, chat_history_length: 0, response_type: Brief, visualization: False
2025-06-12 11:29:18,986 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:19,085 - INFO - project_user.ovais-1.final_test11m1sccnvn - Starting real-time insight generation
2025-06-12 11:29:19,085 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:22,751 - INFO - project_user.ovais-1.final_test11m1sccnvn - Received chunk #1
  -> chunk_length: 0, chunk_preview: EMPTY
2025-06-12 11:29:22,752 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:22,752 - INFO - project_user.ovais-1.final_test11m1sccnvn - Received chunk #2
  -> chunk_length: 3, chunk_preview: ```
2025-06-12 11:29:22,753 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:24,264 - INFO - project_user.ovais-1.final_test11m1sccnvn - Received chunk #3
  -> chunk_length: 4, chunk_preview: json
2025-06-12 11:29:24,264 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:24,351 - INFO - project_user.ovais-1.final_test11m1sccnvn - Received chunk #4
  -> chunk_length: 1, chunk_preview: 

2025-06-12 11:29:24,352 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:24,435 - INFO - project_user.ovais-1.final_test11m1sccnvn - Received chunk #5
  -> chunk_length: 2, chunk_preview: {

2025-06-12 11:29:24,435 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:39,629 - INFO - project_user.ovais-1.final_test11m1sccnvn - Summary streaming completed - boundary detected
  -> summary_length: 1683
2025-06-12 11:29:39,630 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:42,801 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: insight_generation | Subtype: realtime_streaming | Input Tokens: 1464 | Output Tokens: 427 | Total Tokens: 1891 | Running Total Operations: 3 | Success: True
  -> query_id: unified_query_20250612_055859_020158, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: insight_generation, operation_subtype: realtime_streaming, input_tokens: 1464, output_tokens: 427, total_tokens: 1891, success: True, timestamp: 2025-06-12T05:59:42.801279+00:00
2025-06-12 11:29:42,801 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:42,801 - INFO - project_user.ovais-1.final_test11m1sccnvn - Real-time streaming completed successfully
  -> total_content_length: 2062, summary_length: 1682, streaming_summary: True, summary_complete: True, content_preview: ```json
{
    "summary": "**Understanding the General Data Protection Regulation (GDPR)**\n\nThe ..., content_ends_with: ance?",
        "Can you explain the 'right to be forgotten' under GDPR in more detail?"
    ]
}
```, total_chunks_received: 433
2025-06-12 11:29:42,801 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:42,801 - INFO - project_user.ovais-1.final_test11m1sccnvn - Removed markdown code block wrapper from JSON
  -> original_length: 2062, cleaned_length: 2050
2025-06-12 11:29:42,801 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:42,802 - INFO - project_user.ovais-1.final_test11m1sccnvn - Skipped title addition - content was already streamed
  -> summary_length: 1683
2025-06-12 11:29:42,802 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:42,802 - INFO - project_user.ovais-1.final_test11m1sccnvn - Successfully parsed streamed content as structured JSON
  -> summary_length: 1683, tables_count: 0, questions_count: 4
2025-06-12 11:29:42,802 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:42,891 - INFO - project_user.ovais-1.final_test11m1sccnvn - No file provided, use existing data from MongoDB for pdf processing
2025-06-12 11:29:42,891 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:42,891 - INFO - project_user.ovais-1.final_test11m1sccnvn - file_context: 0
  -> extracted_text_length: 0
2025-06-12 11:29:42,891 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:42,891 - INFO - project_user.ovais-1.final_test11m1sccnvn - valid_page_numbers incase of no file provided in request: []
  -> valid_page_numbers: []
2025-06-12 11:29:42,891 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:42,891 - INFO - project_user.ovais-1.final_test11m1sccnvn - Entered not llm call block in pdf processor
2025-06-12 11:29:42,891 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:42,891 - INFO - project_user.ovais-1.final_test11m1sccnvn - Using existing insight_data - skipping LLM call to prevent duplication
  -> insight_data_available: True, source_type: self_answer, insight_data_type: dict, has_summary: True
2025-06-12 11:29:42,892 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:42,892 - INFO - project_user.ovais-1.final_test11m1sccnvn - Putting images as '' in insights data as it will be empty in modes other than data analysis.
2025-06-12 11:29:42,892 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:42,892 - INFO - project_user.ovais-1.final_test11m1sccnvn - Added token usage to insight_data before saving - Input: 2639, Output: 576, Total: 3215
  -> input_tokens: 2639, output_tokens: 576, total_tokens: 3215, response_id: 684a6cce7295613e9eb11c72
2025-06-12 11:29:42,893 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:42,893 - INFO - project_user.ovais-1.final_test11m1sccnvn - Linked token events with response_id: 684a6cce7295613e9eb11c72
  -> response_id: 684a6cce7295613e9eb11c72
2025-06-12 11:29:42,893 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:42,893 - INFO - project_user.ovais-1.final_test11m1sccnvn - Saving project data in mongo db insights collection for pdf processor
2025-06-12 11:29:42,893 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:42,893 - INFO - project_user.ovais-1.final_test11m1sccnvn - Saving project data in mongo db
2025-06-12 11:29:42,893 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:42,963 - INFO - project_user.ovais-1.final_test11m1sccnvn - Project exists in mongo db
2025-06-12 11:29:42,963 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:42,964 - INFO - project_user.ovais-1.final_test11m1sccnvn - File does not exist
2025-06-12 11:29:42,964 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:43,068 - INFO - project_user.ovais-1.final_test11m1sccnvn - Project data saved in mongo db when file does not exist
2025-06-12 11:29:43,068 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:43,154 - INFO - project_user.ovais-1.final_test11m1sccnvn - Saved project data in mongo db for pdf processor
2025-06-12 11:29:43,155 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:43,155 - INFO - project_user.ovais-1.final_test11m1sccnvn - Replacing the tables in insight data with actual table data for response sending
2025-06-12 11:29:43,155 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:43,230 - INFO - project_user.ovais-1.final_test11m1sccnvn - Token usage already included in saved data - Input: 2639, Output: 576
  -> input_tokens: 2639, output_tokens: 576
2025-06-12 11:29:43,231 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:43,232 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_STEP_SUMMARY - PROCESSING_COMPLETE | Total Operations: 3 | Cumulative Input Tokens: 2639 | Cumulative Output Tokens: 576 | Cumulative Total Tokens: 3215 | Self answer processing completed successfully
  -> step_name: PROCESSING_COMPLETE, total_operations: 3, cumulative_input_tokens: 2639, cumulative_output_tokens: 576, cumulative_total_tokens: 3215, additional_info: Self answer processing completed successfully
2025-06-12 11:29:43,232 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:43,233 - INFO - project_user.ovais-1.final_test11m1sccnvn - Saving unified usage events
  -> event_count: 3, query_id: unified_query_20250612_055859_020158
2025-06-12 11:29:43,233 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:43,314 - INFO - project_user.ovais-1.final_test11m1sccnvn - Successfully saved unified usage events
  -> document_id: 684a6ccf7295613e9eb11c73, total_operations: 3, total_tokens: 3215
2025-06-12 11:29:43,314 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:43,315 - INFO - project_user.ovais-1.final_test11m1sccnvn - Usage events linked to response
  -> response_id: 684a6cce7295613e9eb11c72
2025-06-12 11:29:43,315 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:43,315 - INFO - project_user.ovais-1.final_test11m1sccnvn - Successfully saved 3 usage events to MongoDB
  -> mongodb_id: 684a6ccf7295613e9eb11c73, events_count: 3, response_id: 684a6cce7295613e9eb11c72, query_id: unified_query_20250612_055859_020158
2025-06-12 11:29:43,315 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:29:43,315 - INFO - project_user.ovais-1.final_test11m1sccnvn - Usage events saved to MongoDB for self answer path
2025-06-12 11:29:43,315 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:14,295 - INFO - project_user.ovais-1.final_test11m1sccnvn - File processing request started
  -> file_name: None, response_type: Brief, custom_prompt_name: None
2025-06-12 11:48:14,295 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:14,295 - INFO - project_user.ovais-1.final_test11m1sccnvn - Token validation is turned OFF. Skipping authentication checks.
2025-06-12 11:48:14,295 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:14,296 - INFO - project_user.ovais-1.final_test11m1sccnvn - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250612_061814_295977
2025-06-12 11:48:14,296 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:14,296 - INFO - project_user.ovais-1.final_test11m1sccnvn - Unified usage tracking initialized
  -> usage_logger_initialized: True
2025-06-12 11:48:14,296 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:14,296 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_STEP_SUMMARY - INITIALIZATION | Total Operations: 0 | Cumulative Input Tokens: 0 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 0 | Unified usage logger created
  -> step_name: INITIALIZATION, total_operations: 0, cumulative_input_tokens: 0, cumulative_output_tokens: 0, cumulative_total_tokens: 0, additional_info: Unified usage logger created
2025-06-12 11:48:14,296 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:18,837 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: content_moderation | Subtype: restricted_keywords_check | Input Tokens: 81 | Output Tokens: 1 | Total Tokens: 82 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250612_061814_295977, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: content_moderation, operation_subtype: restricted_keywords_check, input_tokens: 81, output_tokens: 1, total_tokens: 82, success: True, timestamp: 2025-06-12T06:18:18.837092+00:00
2025-06-12 11:48:18,838 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:18,840 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_STEP_SUMMARY - RESTRICTED_KEYWORDS_CHECK | Total Operations: 1 | Cumulative Input Tokens: 81 | Cumulative Output Tokens: 1 | Cumulative Total Tokens: 82 | Completed content moderation check
  -> step_name: RESTRICTED_KEYWORDS_CHECK, total_operations: 1, cumulative_input_tokens: 81, cumulative_output_tokens: 1, cumulative_total_tokens: 82, additional_info: Completed content moderation check
2025-06-12 11:48:18,841 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:18,841 - INFO - project_user.ovais-1.final_test11m1sccnvn - Processing file...
2025-06-12 11:48:18,841 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:18,841 - INFO - project_user.ovais-1.final_test11m1sccnvn - Request parameters
  -> file: None, prompt: create a bar chart for units sold in east region from some dummy data, page_number: None, title: None, response_type: Brief, custom_prompt_name: None
2025-06-12 11:48:18,841 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:18,918 - INFO - project_user.ovais-1.final_test11m1sccnvn - Using existing title: Untitled for project final_test11m1sccnvn
  -> title: Untitled, exists: True
2025-06-12 11:48:18,918 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:18,918 - INFO - project_user.ovais-1.final_test11m1sccnvn - Entered else block in process file route
2025-06-12 11:48:18,918 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:18,991 - INFO - project_user.ovais-1.final_test11m1sccnvn - Length of previous_responses before removing images: 1
  -> previous_responses_length: 1
2025-06-12 11:48:18,991 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:18,991 - INFO - project_user.ovais-1.final_test11m1sccnvn - Removed images from previous_responses
2025-06-12 11:48:18,991 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:18,991 - INFO - project_user.ovais-1.final_test11m1sccnvn - Length of previous_responses after removing images: 1
  -> previous_responses_length_after: 1
2025-06-12 11:48:18,991 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:37,242 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: query_routing | Subtype: source_selection | Input Tokens: 1620 | Output Tokens: 47 | Total Tokens: 1667 | Running Total Operations: 2 | Success: True
  -> query_id: unified_query_20250612_061814_295977, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: query_routing, operation_subtype: source_selection, input_tokens: 1620, output_tokens: 47, total_tokens: 1667, success: True, timestamp: 2025-06-12T06:18:37.242661+00:00
2025-06-12 11:48:37,243 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:37,243 - INFO - project_user.ovais-1.final_test11m1sccnvn - source_to_select: {'intent': 'general_query', 'query': '', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': ''}
  -> source_to_select: {'intent': 'general_query', 'query': '', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach...
2025-06-12 11:48:37,243 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:37,243 - INFO - project_user.ovais-1.final_test11m1sccnvn - type of source_to_select: <class 'dict'>
  -> source_type: <class 'dict'>
2025-06-12 11:48:37,244 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:37,244 - INFO - project_user.ovais-1.final_test11m1sccnvn - source_to_select after processing: {'intent': 'general_query', 'query': '', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': ''}
  -> source_to_select_processed: {'intent': 'general_query', 'query': '', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach...
2025-06-12 11:48:37,244 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:37,244 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_STEP_SUMMARY - SOURCE_SELECTION | Total Operations: 2 | Cumulative Input Tokens: 1701 | Cumulative Output Tokens: 48 | Cumulative Total Tokens: 1749 | Selected source: general_query
  -> step_name: SOURCE_SELECTION, total_operations: 2, cumulative_input_tokens: 1701, cumulative_output_tokens: 48, cumulative_total_tokens: 1749, additional_info: Selected source: general_query
2025-06-12 11:48:37,244 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:37,244 - INFO - project_user.ovais-1.final_test11m1sccnvn - source_to_select['source'][0]['FileTool']: False
  -> file_tool: False
2025-06-12 11:48:37,244 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:37,244 - INFO - project_user.ovais-1.final_test11m1sccnvn - source_to_select['source'][0]['WebSeach']: False
  -> web_search: False
2025-06-12 11:48:37,244 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:37,244 - INFO - project_user.ovais-1.final_test11m1sccnvn - source_to_select['source'][0]['SelfAnswer']: True
  -> self_answer: True
2025-06-12 11:48:37,244 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:37,244 - INFO - project_user.ovais-1.final_test11m1sccnvn - Processing self answer
2025-06-12 11:48:37,245 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:37,245 - INFO - project_user.ovais-1.final_test11m1sccnvn - --------------------PAGES, self answer---------------------
2025-06-12 11:48:37,245 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:37,245 - INFO - project_user.ovais-1.final_test11m1sccnvn - Valid page numbers: []
  -> valid_page_numbers: []
2025-06-12 11:48:37,245 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:37,246 - INFO - project_user.ovais-1.final_test11m1sccnvn - STREAMING_START - Self answer streaming initiated
  -> stream_type: self_answer, source_type: self_answer, query: None, response_type: Brief, visualization: False
2025-06-12 11:48:37,246 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:37,246 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_STEP_SUMMARY - MAIN_LLM_RESPONSE | Total Operations: 2 | Cumulative Input Tokens: 1701 | Cumulative Output Tokens: 48 | Cumulative Total Tokens: 1749 | Starting main LLM response generation for self answer
  -> step_name: MAIN_LLM_RESPONSE, total_operations: 2, cumulative_input_tokens: 1701, cumulative_output_tokens: 48, cumulative_total_tokens: 1749, additional_info: Starting main LLM response generation for self answer
2025-06-12 11:48:37,246 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:37,246 - INFO - project_user.ovais-1.final_test11m1sccnvn - STREAMING_START - Non-data analysis real-time streaming initiated
  -> stream_type: non_data_analysis_realtime, function: fake_llm_resonse, source_type: , prompt_length: 0, extracted_text_length: 0, chat_history_length: 1, response_type: Brief, visualization: False
2025-06-12 11:48:37,247 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:37,354 - INFO - project_user.ovais-1.final_test11m1sccnvn - Starting real-time insight generation
2025-06-12 11:48:37,354 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:40,810 - INFO - project_user.ovais-1.final_test11m1sccnvn - Received chunk #1
  -> chunk_length: 0, chunk_preview: EMPTY
2025-06-12 11:48:40,811 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:40,812 - INFO - project_user.ovais-1.final_test11m1sccnvn - Received chunk #2
  -> chunk_length: 3, chunk_preview: ```
2025-06-12 11:48:40,812 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:41,573 - INFO - project_user.ovais-1.final_test11m1sccnvn - Received chunk #3
  -> chunk_length: 4, chunk_preview: json
2025-06-12 11:48:41,574 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:41,653 - INFO - project_user.ovais-1.final_test11m1sccnvn - Received chunk #4
  -> chunk_length: 1, chunk_preview: 

2025-06-12 11:48:41,654 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:41,734 - INFO - project_user.ovais-1.final_test11m1sccnvn - Received chunk #5
  -> chunk_length: 2, chunk_preview: {

2025-06-12 11:48:41,734 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:55,962 - INFO - project_user.ovais-1.final_test11m1sccnvn - Summary streaming completed - boundary detected
  -> summary_length: 1683
2025-06-12 11:48:55,964 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,072 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: insight_generation | Subtype: realtime_streaming | Input Tokens: 1964 | Output Tokens: 427 | Total Tokens: 2391 | Running Total Operations: 3 | Success: True
  -> query_id: unified_query_20250612_061814_295977, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: insight_generation, operation_subtype: realtime_streaming, input_tokens: 1964, output_tokens: 427, total_tokens: 2391, success: True, timestamp: 2025-06-12T06:18:59.072388+00:00
2025-06-12 11:48:59,072 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,072 - INFO - project_user.ovais-1.final_test11m1sccnvn - Real-time streaming completed successfully
  -> total_content_length: 2062, summary_length: 1682, streaming_summary: True, summary_complete: True, content_preview: ```json
{
    "summary": "**Understanding the General Data Protection Regulation (GDPR)**\n\nThe ..., content_ends_with: ance?",
        "Can you explain the 'right to be forgotten' under GDPR in more detail?"
    ]
}
```, total_chunks_received: 433
2025-06-12 11:48:59,073 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,073 - INFO - project_user.ovais-1.final_test11m1sccnvn - Removed markdown code block wrapper from JSON
  -> original_length: 2062, cleaned_length: 2050
2025-06-12 11:48:59,073 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,073 - INFO - project_user.ovais-1.final_test11m1sccnvn - Skipped title addition - content was already streamed
  -> summary_length: 1683
2025-06-12 11:48:59,073 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,073 - INFO - project_user.ovais-1.final_test11m1sccnvn - Successfully parsed streamed content as structured JSON
  -> summary_length: 1683, tables_count: 0, questions_count: 4
2025-06-12 11:48:59,073 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,144 - INFO - project_user.ovais-1.final_test11m1sccnvn - No file provided, use existing data from MongoDB for pdf processing
2025-06-12 11:48:59,144 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,144 - INFO - project_user.ovais-1.final_test11m1sccnvn - file_context: 0
  -> extracted_text_length: 0
2025-06-12 11:48:59,144 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,145 - INFO - project_user.ovais-1.final_test11m1sccnvn - valid_page_numbers incase of no file provided in request: []
  -> valid_page_numbers: []
2025-06-12 11:48:59,145 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,145 - INFO - project_user.ovais-1.final_test11m1sccnvn - Entered not llm call block in pdf processor
2025-06-12 11:48:59,145 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,145 - INFO - project_user.ovais-1.final_test11m1sccnvn - Using existing insight_data - skipping LLM call to prevent duplication
  -> insight_data_available: True, source_type: self_answer, insight_data_type: dict, has_summary: True
2025-06-12 11:48:59,145 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,145 - INFO - project_user.ovais-1.final_test11m1sccnvn - Putting images as '' in insights data as it will be empty in modes other than data analysis.
2025-06-12 11:48:59,145 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,147 - INFO - project_user.ovais-1.final_test11m1sccnvn - Added token usage to insight_data before saving - Input: 3665, Output: 475, Total: 4140
  -> input_tokens: 3665, output_tokens: 475, total_tokens: 4140, response_id: 684a71537295613e9eb11c74
2025-06-12 11:48:59,147 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,147 - INFO - project_user.ovais-1.final_test11m1sccnvn - Linked token events with response_id: 684a71537295613e9eb11c74
  -> response_id: 684a71537295613e9eb11c74
2025-06-12 11:48:59,147 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,148 - INFO - project_user.ovais-1.final_test11m1sccnvn - Saving project data in mongo db insights collection for pdf processor
2025-06-12 11:48:59,148 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,148 - INFO - project_user.ovais-1.final_test11m1sccnvn - Saving project data in mongo db
2025-06-12 11:48:59,148 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,219 - INFO - project_user.ovais-1.final_test11m1sccnvn - Project exists in mongo db
2025-06-12 11:48:59,219 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,219 - INFO - project_user.ovais-1.final_test11m1sccnvn - File does not exist
2025-06-12 11:48:59,219 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,301 - INFO - project_user.ovais-1.final_test11m1sccnvn - Project data saved in mongo db when file does not exist
2025-06-12 11:48:59,302 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,397 - INFO - project_user.ovais-1.final_test11m1sccnvn - Saved project data in mongo db for pdf processor
2025-06-12 11:48:59,398 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,398 - INFO - project_user.ovais-1.final_test11m1sccnvn - Replacing the tables in insight data with actual table data for response sending
2025-06-12 11:48:59,398 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,472 - INFO - project_user.ovais-1.final_test11m1sccnvn - Token usage already included in saved data - Input: 3665, Output: 475
  -> input_tokens: 3665, output_tokens: 475
2025-06-12 11:48:59,473 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,473 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_STEP_SUMMARY - PROCESSING_COMPLETE | Total Operations: 3 | Cumulative Input Tokens: 3665 | Cumulative Output Tokens: 475 | Cumulative Total Tokens: 4140 | Self answer processing completed successfully
  -> step_name: PROCESSING_COMPLETE, total_operations: 3, cumulative_input_tokens: 3665, cumulative_output_tokens: 475, cumulative_total_tokens: 4140, additional_info: Self answer processing completed successfully
2025-06-12 11:48:59,474 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,474 - INFO - project_user.ovais-1.final_test11m1sccnvn - Saving unified usage events
  -> event_count: 3, query_id: unified_query_20250612_061814_295977
2025-06-12 11:48:59,474 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,551 - INFO - project_user.ovais-1.final_test11m1sccnvn - Successfully saved unified usage events
  -> document_id: 684a71537295613e9eb11c75, total_operations: 3, total_tokens: 4140
2025-06-12 11:48:59,552 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,552 - INFO - project_user.ovais-1.final_test11m1sccnvn - Usage events linked to response
  -> response_id: 684a71537295613e9eb11c74
2025-06-12 11:48:59,552 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,552 - INFO - project_user.ovais-1.final_test11m1sccnvn - Successfully saved 3 usage events to MongoDB
  -> mongodb_id: 684a71537295613e9eb11c75, events_count: 3, response_id: 684a71537295613e9eb11c74, query_id: unified_query_20250612_061814_295977
2025-06-12 11:48:59,552 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 11:48:59,552 - INFO - project_user.ovais-1.final_test11m1sccnvn - Usage events saved to MongoDB for self answer path
2025-06-12 11:48:59,552 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:14,315 - INFO - project_user.ovais-1.final_test11m1sccnvn - File processing request started
  -> file_name: None, response_type: Brief, custom_prompt_name: None
2025-06-12 12:08:14,315 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:14,315 - INFO - project_user.ovais-1.final_test11m1sccnvn - Token validation is turned OFF. Skipping authentication checks.
2025-06-12 12:08:14,315 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:14,315 - INFO - project_user.ovais-1.final_test11m1sccnvn - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250612_063814_315913
2025-06-12 12:08:14,315 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:14,316 - INFO - project_user.ovais-1.final_test11m1sccnvn - Unified usage tracking initialized
  -> usage_logger_initialized: True
2025-06-12 12:08:14,316 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:14,316 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_STEP_SUMMARY - INITIALIZATION | Total Operations: 0 | Cumulative Input Tokens: 0 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 0 | Unified usage logger created
  -> step_name: INITIALIZATION, total_operations: 0, cumulative_input_tokens: 0, cumulative_output_tokens: 0, cumulative_total_tokens: 0, additional_info: Unified usage logger created
2025-06-12 12:08:14,316 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:21,291 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: content_moderation | Subtype: restricted_keywords_check | Input Tokens: 81 | Output Tokens: 1 | Total Tokens: 82 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250612_063814_315913, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: content_moderation, operation_subtype: restricted_keywords_check, input_tokens: 81, output_tokens: 1, total_tokens: 82, success: True, timestamp: 2025-06-12T06:38:21.291188+00:00
2025-06-12 12:08:21,292 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:21,293 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_STEP_SUMMARY - RESTRICTED_KEYWORDS_CHECK | Total Operations: 1 | Cumulative Input Tokens: 81 | Cumulative Output Tokens: 1 | Cumulative Total Tokens: 82 | Completed content moderation check
  -> step_name: RESTRICTED_KEYWORDS_CHECK, total_operations: 1, cumulative_input_tokens: 81, cumulative_output_tokens: 1, cumulative_total_tokens: 82, additional_info: Completed content moderation check
2025-06-12 12:08:21,293 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:21,293 - INFO - project_user.ovais-1.final_test11m1sccnvn - Processing file...
2025-06-12 12:08:21,293 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:21,293 - INFO - project_user.ovais-1.final_test11m1sccnvn - Request parameters
  -> file: None, prompt: create a bar chart for units sold in east region from some dummy data, page_number: None, title: None, response_type: Brief, custom_prompt_name: None
2025-06-12 12:08:21,293 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:21,432 - INFO - project_user.ovais-1.final_test11m1sccnvn - Using existing title: Untitled for project final_test11m1sccnvn
  -> title: Untitled, exists: True
2025-06-12 12:08:21,432 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:21,432 - INFO - project_user.ovais-1.final_test11m1sccnvn - Entered else block in process file route
2025-06-12 12:08:21,432 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:21,505 - INFO - project_user.ovais-1.final_test11m1sccnvn - Length of previous_responses before removing images: 2
  -> previous_responses_length: 2
2025-06-12 12:08:21,506 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:21,506 - INFO - project_user.ovais-1.final_test11m1sccnvn - Removed images from previous_responses
2025-06-12 12:08:21,506 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:21,507 - INFO - project_user.ovais-1.final_test11m1sccnvn - Length of previous_responses after removing images: 2
  -> previous_responses_length_after: 2
2025-06-12 12:08:21,507 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:37,468 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: query_routing | Subtype: source_selection | Input Tokens: 2134 | Output Tokens: 206 | Total Tokens: 2340 | Running Total Operations: 2 | Success: True
  -> query_id: unified_query_20250612_063814_315913, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: query_routing, operation_subtype: source_selection, input_tokens: 2134, output_tokens: 206, total_tokens: 2340, success: True, timestamp: 2025-06-12T06:38:37.468581+00:00
2025-06-12 12:08:37,469 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:37,469 - INFO - project_user.ovais-1.final_test11m1sccnvn - source_to_select: {'intent': 'dataset', 'query': 'create a bar chart for units sold in east region from some dummy data', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': "To create a bar chart for units sold in the East region from dummy data, I can generate a sample dataset for you. Here's an example of how the data might look:\n\n| Month    | Units Sold (East) |\n|----------|-------------------|\n| January  | 120               |\n| February | 150               |\n| March    | 180               |\n| April    | 200               |\n| May      | 220               |\n| June     | 250               |\n\nWould you like me to proceed with creating the bar chart based on this dummy data? If you have specific data points or a different structure in mind, please let me know."}
  -> source_to_select: {'intent': 'dataset', 'query': 'create a bar chart for units sold in east region from some dummy ...
2025-06-12 12:08:37,470 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:37,470 - INFO - project_user.ovais-1.final_test11m1sccnvn - type of source_to_select: <class 'dict'>
  -> source_type: <class 'dict'>
2025-06-12 12:08:37,470 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:37,470 - INFO - project_user.ovais-1.final_test11m1sccnvn - source_to_select after processing: {'intent': 'dataset', 'query': 'create a bar chart for units sold in east region from some dummy data', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': "To create a bar chart for units sold in the East region from dummy data, I can generate a sample dataset for you. Here's an example of how the data might look:\n\n| Month    | Units Sold (East) |\n|----------|-------------------|\n| January  | 120               |\n| February | 150               |\n| March    | 180               |\n| April    | 200               |\n| May      | 220               |\n| June     | 250               |\n\nWould you like me to proceed with creating the bar chart based on this dummy data? If you have specific data points or a different structure in mind, please let me know."}
  -> source_to_select_processed: {'intent': 'dataset', 'query': 'create a bar chart for units sold in east region from some dummy ...
2025-06-12 12:08:37,470 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:37,470 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_STEP_SUMMARY - SOURCE_SELECTION | Total Operations: 2 | Cumulative Input Tokens: 2215 | Cumulative Output Tokens: 207 | Cumulative Total Tokens: 2422 | Selected source: dataset
  -> step_name: SOURCE_SELECTION, total_operations: 2, cumulative_input_tokens: 2215, cumulative_output_tokens: 207, cumulative_total_tokens: 2422, additional_info: Selected source: dataset
2025-06-12 12:08:37,471 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:37,471 - INFO - project_user.ovais-1.final_test11m1sccnvn - source_to_select['source'][0]['FileTool']: False
  -> file_tool: False
2025-06-12 12:08:37,471 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:37,471 - INFO - project_user.ovais-1.final_test11m1sccnvn - source_to_select['source'][0]['WebSeach']: False
  -> web_search: False
2025-06-12 12:08:37,471 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:37,471 - INFO - project_user.ovais-1.final_test11m1sccnvn - source_to_select['source'][0]['SelfAnswer']: True
  -> self_answer: True
2025-06-12 12:08:37,471 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:37,471 - INFO - project_user.ovais-1.final_test11m1sccnvn - Processing self answer
2025-06-12 12:08:37,471 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:37,471 - INFO - project_user.ovais-1.final_test11m1sccnvn - --------------------PAGES, self answer---------------------
2025-06-12 12:08:37,471 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:37,471 - INFO - project_user.ovais-1.final_test11m1sccnvn - Valid page numbers: []
  -> valid_page_numbers: []
2025-06-12 12:08:37,471 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:37,473 - INFO - project_user.ovais-1.final_test11m1sccnvn - STREAMING_START - Self answer streaming initiated
  -> stream_type: self_answer, source_type: self_answer, query: create a bar chart for units sold in east region from some dummy data, response_type: Brief, visualization: False
2025-06-12 12:08:37,473 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:37,473 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_STEP_SUMMARY - MAIN_LLM_RESPONSE | Total Operations: 2 | Cumulative Input Tokens: 2215 | Cumulative Output Tokens: 207 | Cumulative Total Tokens: 2422 | Starting main LLM response generation for self answer
  -> step_name: MAIN_LLM_RESPONSE, total_operations: 2, cumulative_input_tokens: 2215, cumulative_output_tokens: 207, cumulative_total_tokens: 2422, additional_info: Starting main LLM response generation for self answer
2025-06-12 12:08:37,473 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:37,473 - INFO - project_user.ovais-1.final_test11m1sccnvn - STREAMING_START - Non-data analysis real-time streaming initiated
  -> stream_type: non_data_analysis_realtime, function: fake_llm_resonse, source_type: , prompt_length: 69, extracted_text_length: 0, chat_history_length: 2, response_type: Brief, visualization: False
2025-06-12 12:08:37,473 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:37,643 - INFO - project_user.ovais-1.final_test11m1sccnvn - Starting real-time insight generation
2025-06-12 12:08:37,643 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:42,213 - INFO - project_user.ovais-1.final_test11m1sccnvn - Received chunk #1
  -> chunk_length: 0, chunk_preview: EMPTY
2025-06-12 12:08:42,213 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:42,223 - INFO - project_user.ovais-1.final_test11m1sccnvn - Received chunk #2
  -> chunk_length: 3, chunk_preview: ```
2025-06-12 12:08:42,223 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:43,673 - INFO - project_user.ovais-1.final_test11m1sccnvn - Received chunk #3
  -> chunk_length: 4, chunk_preview: json
2025-06-12 12:08:43,674 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:43,832 - INFO - project_user.ovais-1.final_test11m1sccnvn - Received chunk #4
  -> chunk_length: 1, chunk_preview: 

2025-06-12 12:08:43,832 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:43,834 - INFO - project_user.ovais-1.final_test11m1sccnvn - Received chunk #5
  -> chunk_length: 2, chunk_preview: {

2025-06-12 12:08:43,834 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:49,463 - INFO - project_user.ovais-1.final_test11m1sccnvn - Summary streaming completed - boundary detected
  -> summary_length: 588
2025-06-12 12:08:49,464 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,387 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: insight_generation | Subtype: realtime_streaming | Input Tokens: 2493 | Output Tokens: 295 | Total Tokens: 2788 | Running Total Operations: 3 | Success: True
  -> query_id: unified_query_20250612_063814_315913, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: insight_generation, operation_subtype: realtime_streaming, input_tokens: 2493, output_tokens: 295, total_tokens: 2788, success: True, timestamp: 2025-06-12T06:38:57.386971+00:00
2025-06-12 12:08:57,387 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,387 - INFO - project_user.ovais-1.final_test11m1sccnvn - Real-time streaming completed successfully
  -> total_content_length: 1391, summary_length: 578, streaming_summary: True, summary_complete: True, content_preview: ```json
{
    "summary": "**Bar Chart for Units Sold in East Region (Dummy Data)**\n\nBelow is a ..., content_ends_with:  "Can you provide a trend analysis of units sold in the East region over the past year?"
    ]
}
```, total_chunks_received: 304
2025-06-12 12:08:57,387 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,388 - INFO - project_user.ovais-1.final_test11m1sccnvn - Removed markdown code block wrapper from JSON
  -> original_length: 1391, cleaned_length: 1379
2025-06-12 12:08:57,388 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,391 - WARNING - project_user.ovais-1.final_test11m1sccnvn - Failed to parse streamed content as JSON: 5 validation errors for InsightExtraction
tables.0.chart_name
  Field required [type=missing, input_value={'name': 'Units Sold in E...120, 150, 90, 200, 180]}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
tables.0.type_of_chart
  Field required [type=missing, input_value={'name': 'Units Sold in E...120, 150, 90, 200, 180]}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
tables.0.chart_decription
  Field required [type=missing, input_value={'name': 'Units Sold in E...120, 150, 90, 200, 180]}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
tables.0.x_labels
  Field required [type=missing, input_value={'name': 'Units Sold in E...120, 150, 90, 200, 180]}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
tables.0.y_labels
  Field required [type=missing, input_value={'name': 'Units Sold in E...120, 150, 90, 200, 180]}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
  -> content_length: 1379, content_preview: {
    "summary": "**Bar Chart for Units Sold in East Region (Dummy Data)**\n\nBelow is a bar char...
2025-06-12 12:08:57,391 - WARNING - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,391 - INFO - project_user.ovais-1.final_test11m1sccnvn - Using already streamed content for fallback response
  -> streamed_content_length: 578
2025-06-12 12:08:57,392 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,392 - INFO - project_user.ovais-1.final_test11m1sccnvn - Created fallback structured response from extracted summary
  -> summary_length: 578
2025-06-12 12:08:57,392 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,576 - INFO - project_user.ovais-1.final_test11m1sccnvn - No file provided, use existing data from MongoDB for pdf processing
2025-06-12 12:08:57,577 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,577 - INFO - project_user.ovais-1.final_test11m1sccnvn - file_context: 0
  -> extracted_text_length: 0
2025-06-12 12:08:57,577 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,577 - INFO - project_user.ovais-1.final_test11m1sccnvn - valid_page_numbers incase of no file provided in request: []
  -> valid_page_numbers: []
2025-06-12 12:08:57,578 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,578 - INFO - project_user.ovais-1.final_test11m1sccnvn - Entered not llm call block in pdf processor
2025-06-12 12:08:57,578 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,578 - INFO - project_user.ovais-1.final_test11m1sccnvn - Using existing insight_data - skipping LLM call to prevent duplication
  -> insight_data_available: True, source_type: self_answer, insight_data_type: dict, has_summary: True
2025-06-12 12:08:57,579 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,579 - INFO - project_user.ovais-1.final_test11m1sccnvn - Putting images as '' in insights data as it will be empty in modes other than data analysis.
2025-06-12 12:08:57,579 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,581 - INFO - project_user.ovais-1.final_test11m1sccnvn - Added token usage to insight_data before saving - Input: 4708, Output: 502, Total: 5210
  -> input_tokens: 4708, output_tokens: 502, total_tokens: 5210, response_id: 684a76017295613e9eb11c76
2025-06-12 12:08:57,581 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,581 - INFO - project_user.ovais-1.final_test11m1sccnvn - Linked token events with response_id: 684a76017295613e9eb11c76
  -> response_id: 684a76017295613e9eb11c76
2025-06-12 12:08:57,581 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,581 - INFO - project_user.ovais-1.final_test11m1sccnvn - Saving project data in mongo db insights collection for pdf processor
2025-06-12 12:08:57,581 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,581 - INFO - project_user.ovais-1.final_test11m1sccnvn - Saving project data in mongo db
2025-06-12 12:08:57,581 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,655 - INFO - project_user.ovais-1.final_test11m1sccnvn - Project exists in mongo db
2025-06-12 12:08:57,656 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,656 - INFO - project_user.ovais-1.final_test11m1sccnvn - File does not exist
2025-06-12 12:08:57,656 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,760 - INFO - project_user.ovais-1.final_test11m1sccnvn - Project data saved in mongo db when file does not exist
2025-06-12 12:08:57,761 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,860 - INFO - project_user.ovais-1.final_test11m1sccnvn - Saved project data in mongo db for pdf processor
2025-06-12 12:08:57,861 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,861 - INFO - project_user.ovais-1.final_test11m1sccnvn - Replacing the tables in insight data with actual table data for response sending
2025-06-12 12:08:57,861 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,946 - INFO - project_user.ovais-1.final_test11m1sccnvn - Token usage already included in saved data - Input: 4708, Output: 502
  -> input_tokens: 4708, output_tokens: 502
2025-06-12 12:08:57,946 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,947 - INFO - project_user.ovais-1.final_test11m1sccnvn - USAGE_STEP_SUMMARY - PROCESSING_COMPLETE | Total Operations: 3 | Cumulative Input Tokens: 4708 | Cumulative Output Tokens: 502 | Cumulative Total Tokens: 5210 | Self answer processing completed successfully
  -> step_name: PROCESSING_COMPLETE, total_operations: 3, cumulative_input_tokens: 4708, cumulative_output_tokens: 502, cumulative_total_tokens: 5210, additional_info: Self answer processing completed successfully
2025-06-12 12:08:57,947 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:57,948 - INFO - project_user.ovais-1.final_test11m1sccnvn - Saving unified usage events
  -> event_count: 3, query_id: unified_query_20250612_063814_315913
2025-06-12 12:08:57,948 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:58,037 - INFO - project_user.ovais-1.final_test11m1sccnvn - Successfully saved unified usage events
  -> document_id: 684a76017295613e9eb11c77, total_operations: 3, total_tokens: 5210
2025-06-12 12:08:58,037 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:58,038 - INFO - project_user.ovais-1.final_test11m1sccnvn - Usage events linked to response
  -> response_id: 684a76017295613e9eb11c76
2025-06-12 12:08:58,038 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:58,038 - INFO - project_user.ovais-1.final_test11m1sccnvn - Successfully saved 3 usage events to MongoDB
  -> mongodb_id: 684a76017295613e9eb11c77, events_count: 3, response_id: 684a76017295613e9eb11c76, query_id: unified_query_20250612_063814_315913
2025-06-12 12:08:58,039 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
2025-06-12 12:08:58,039 - INFO - project_user.ovais-1.final_test11m1sccnvn - Usage events saved to MongoDB for self answer path
2025-06-12 12:08:58,039 - INFO - project_user.ovais-1.final_test11m1sccnvn - 
