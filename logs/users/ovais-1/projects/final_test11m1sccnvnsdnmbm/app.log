2025-06-12 12:45:18,802 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - File processing request started
  -> file_name: None, response_type: Brief, custom_prompt_name: None
2025-06-12 12:45:18,803 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:18,803 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Token validation is turned OFF. Skipping authentication checks.
2025-06-12 12:45:18,803 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:18,803 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250612_071518_803232
2025-06-12 12:45:18,803 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:18,803 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Unified usage tracking initialized
  -> usage_logger_initialized: True
2025-06-12 12:45:18,803 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:18,803 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - USAGE_STEP_SUMMARY - INITIALIZATION | Total Operations: 0 | Cumulative Input Tokens: 0 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 0 | Unified usage logger created
  -> step_name: INITIALIZATION, total_operations: 0, cumulative_input_tokens: 0, cumulative_output_tokens: 0, cumulative_total_tokens: 0, additional_info: Unified usage logger created
2025-06-12 12:45:18,803 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:24,251 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: content_moderation | Subtype: restricted_keywords_check | Input Tokens: 81 | Output Tokens: 1 | Total Tokens: 82 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250612_071518_803232, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: content_moderation, operation_subtype: restricted_keywords_check, input_tokens: 81, output_tokens: 1, total_tokens: 82, success: True, timestamp: 2025-06-12T07:15:24.251290+00:00
2025-06-12 12:45:24,252 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:24,252 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - USAGE_STEP_SUMMARY - RESTRICTED_KEYWORDS_CHECK | Total Operations: 1 | Cumulative Input Tokens: 81 | Cumulative Output Tokens: 1 | Cumulative Total Tokens: 82 | Completed content moderation check
  -> step_name: RESTRICTED_KEYWORDS_CHECK, total_operations: 1, cumulative_input_tokens: 81, cumulative_output_tokens: 1, cumulative_total_tokens: 82, additional_info: Completed content moderation check
2025-06-12 12:45:24,252 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:24,253 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Processing file...
2025-06-12 12:45:24,253 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:24,253 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Request parameters
  -> file: None, prompt: create a bar chart for units sold in east region from some dummy data, page_number: None, title: None, response_type: Brief, custom_prompt_name: None
2025-06-12 12:45:24,253 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:24,363 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Set default title to 'Untitled' for new project
  -> title: Untitled, exists: False
2025-06-12 12:45:24,363 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:24,363 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Entered else block in process file route
2025-06-12 12:45:24,363 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:24,414 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Length of previous_responses before removing images: 0
  -> previous_responses_length: 0
2025-06-12 12:45:24,414 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:24,414 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Removed images from previous_responses
2025-06-12 12:45:24,414 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:24,414 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Length of previous_responses after removing images: 0
  -> previous_responses_length_after: 0
2025-06-12 12:45:24,414 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:43,636 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: query_routing | Subtype: source_selection | Input Tokens: 1116 | Output Tokens: 321 | Total Tokens: 1437 | Running Total Operations: 2 | Success: True
  -> query_id: unified_query_20250612_071518_803232, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: query_routing, operation_subtype: source_selection, input_tokens: 1116, output_tokens: 321, total_tokens: 1437, success: True, timestamp: 2025-06-12T07:15:43.636300+00:00
2025-06-12 12:45:43,636 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:43,636 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - source_to_select: {'intent': 'instruction', 'query': 'create a bar chart for units sold in east region from some dummy data', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': "I can generate a bar chart for units sold in the East region using dummy data. Here's an example of how the data might look and the corresponding bar chart:\n\n**Dummy Data:**\n| Month | Units Sold (East) |\n|-------|-------------------|\n| Jan   | 150               |\n| Feb   | 200               |\n| Mar   | 180               |\n| Apr   | 220               |\n| May   | 250               |\n| Jun   | 300               |\n\n**Bar Chart:**\n```\nUnits Sold in East Region\n  300 |                   █\n  250 |               █   █\n  200 |           █   █   █\n  150 |       █   █   █   █\n  100 |   █   █   █   █   █\n       -------------------\n        Jan Feb Mar Apr May Jun\n```\n\nWould you like me to adjust the data or the chart in any way?"}
  -> source_to_select: {'intent': 'instruction', 'query': 'create a bar chart for units sold in east region from some du...
2025-06-12 12:45:43,636 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:43,636 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - type of source_to_select: <class 'dict'>
  -> source_type: <class 'dict'>
2025-06-12 12:45:43,636 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:43,637 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - source_to_select after processing: {'intent': 'instruction', 'query': 'create a bar chart for units sold in east region from some dummy data', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': "I can generate a bar chart for units sold in the East region using dummy data. Here's an example of how the data might look and the corresponding bar chart:\n\n**Dummy Data:**\n| Month | Units Sold (East) |\n|-------|-------------------|\n| Jan   | 150               |\n| Feb   | 200               |\n| Mar   | 180               |\n| Apr   | 220               |\n| May   | 250               |\n| Jun   | 300               |\n\n**Bar Chart:**\n```\nUnits Sold in East Region\n  300 |                   █\n  250 |               █   █\n  200 |           █   █   █\n  150 |       █   █   █   █\n  100 |   █   █   █   █   █\n       -------------------\n        Jan Feb Mar Apr May Jun\n```\n\nWould you like me to adjust the data or the chart in any way?"}
  -> source_to_select_processed: {'intent': 'instruction', 'query': 'create a bar chart for units sold in east region from some du...
2025-06-12 12:45:43,637 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:43,637 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - USAGE_STEP_SUMMARY - SOURCE_SELECTION | Total Operations: 2 | Cumulative Input Tokens: 1197 | Cumulative Output Tokens: 322 | Cumulative Total Tokens: 1519 | Selected source: instruction
  -> step_name: SOURCE_SELECTION, total_operations: 2, cumulative_input_tokens: 1197, cumulative_output_tokens: 322, cumulative_total_tokens: 1519, additional_info: Selected source: instruction
2025-06-12 12:45:43,637 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:43,637 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - source_to_select['source'][0]['FileTool']: False
  -> file_tool: False
2025-06-12 12:45:43,637 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:43,637 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - source_to_select['source'][0]['WebSeach']: False
  -> web_search: False
2025-06-12 12:45:43,637 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:43,637 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - source_to_select['source'][0]['SelfAnswer']: True
  -> self_answer: True
2025-06-12 12:45:43,638 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:43,638 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Processing self answer
2025-06-12 12:45:43,638 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:43,638 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - --------------------PAGES, self answer---------------------
2025-06-12 12:45:43,638 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:43,638 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Valid page numbers: []
  -> valid_page_numbers: []
2025-06-12 12:45:43,638 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:43,640 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - STREAMING_START - Self answer streaming initiated
  -> stream_type: self_answer, source_type: self_answer, query: create a bar chart for units sold in east region from some dummy data, response_type: Brief, visualization: False
2025-06-12 12:45:43,640 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:43,640 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - USAGE_STEP_SUMMARY - MAIN_LLM_RESPONSE | Total Operations: 2 | Cumulative Input Tokens: 1197 | Cumulative Output Tokens: 322 | Cumulative Total Tokens: 1519 | Starting main LLM response generation for self answer
  -> step_name: MAIN_LLM_RESPONSE, total_operations: 2, cumulative_input_tokens: 1197, cumulative_output_tokens: 322, cumulative_total_tokens: 1519, additional_info: Starting main LLM response generation for self answer
2025-06-12 12:45:43,640 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:43,640 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - STREAMING_START - Non-data analysis real-time streaming initiated
  -> stream_type: non_data_analysis_realtime, function: fake_llm_resonse, source_type: , prompt_length: 69, extracted_text_length: 0, chat_history_length: 0, response_type: Brief, visualization: False
2025-06-12 12:45:43,640 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:43,708 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Starting real-time insight generation
2025-06-12 12:45:43,709 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:54,523 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Received chunk #1
  -> chunk_length: 0, chunk_preview: EMPTY
2025-06-12 12:45:54,523 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:54,524 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Received chunk #2
  -> chunk_length: 3, chunk_preview: ```
2025-06-12 12:45:54,524 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:55,349 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Received chunk #3
  -> chunk_length: 4, chunk_preview: json
2025-06-12 12:45:55,349 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:55,436 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Received chunk #4
  -> chunk_length: 1, chunk_preview: 

2025-06-12 12:45:55,436 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:45:55,520 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Received chunk #5
  -> chunk_length: 2, chunk_preview: {

2025-06-12 12:45:55,521 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:02,159 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Summary streaming completed - boundary detected
  -> summary_length: 682
2025-06-12 12:46:02,160 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,499 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: insight_generation | Subtype: realtime_streaming | Input Tokens: 1475 | Output Tokens: 339 | Total Tokens: 1814 | Running Total Operations: 3 | Success: True
  -> query_id: unified_query_20250612_071518_803232, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: insight_generation, operation_subtype: realtime_streaming, input_tokens: 1475, output_tokens: 339, total_tokens: 1814, success: True, timestamp: 2025-06-12T07:16:11.498906+00:00
2025-06-12 12:46:11,499 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,499 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Real-time streaming completed successfully
  -> total_content_length: 1532, summary_length: 673, streaming_summary: True, summary_complete: True, content_preview: ```json
{
    "summary": "**Units Sold in East Region - Dummy Data Analysis**\n\nBased on the use..., content_ends_with:   "What percentage of total company sales came from the East region during this period?"
    ]
}
```, total_chunks_received: 349
2025-06-12 12:46:11,499 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,499 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Removed markdown code block wrapper from JSON
  -> original_length: 1532, cleaned_length: 1520
2025-06-12 12:46:11,499 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,499 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - JSON detection check
  -> is_json_like: True, starts_with_brace: True, ends_with_brace: True, content_start: {
    "summary": "**, content_end: his period?"
    ]
}
2025-06-12 12:46:11,499 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,500 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Transformed table data to match expected schema
  -> original_table_count: 1, transformed_table_count: 1, original_keys: [['name', 'type', 'description', 'labels', 'values', 'x_label', 'y_label', 'color']], transformed_keys: [['chart_name', 'type_of_chart', 'chart_decription', 'x_labels', 'values', 'x_label', 'y_label', ...
2025-06-12 12:46:11,500 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,500 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Skipped title addition - content was already streamed
  -> summary_length: 682
2025-06-12 12:46:11,500 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,500 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Successfully parsed streamed content as structured JSON
  -> summary_length: 682, tables_count: 1, questions_count: 4
2025-06-12 12:46:11,500 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,551 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - No file provided, use existing data from MongoDB for pdf processing
2025-06-12 12:46:11,552 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,552 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - file_context: 0
  -> extracted_text_length: 0
2025-06-12 12:46:11,552 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,552 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - valid_page_numbers incase of no file provided in request: []
  -> valid_page_numbers: []
2025-06-12 12:46:11,552 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,552 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Entered not llm call block in pdf processor
2025-06-12 12:46:11,552 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,553 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Using existing insight_data - skipping LLM call to prevent duplication
  -> insight_data_available: True, source_type: self_answer, insight_data_type: dict, has_summary: True
2025-06-12 12:46:11,553 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,553 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Tables found in insight data, Preparing to save tables in mongo db tables collection
2025-06-12 12:46:11,553 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,553 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Starting to save tables to tables collection
2025-06-12 12:46:11,553 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,666 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Tables saved in mongo db tables collection with id: %s
  -> tables_collection_id: 684a7ebbe52e67b3dd845e58
2025-06-12 12:46:11,667 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,667 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Replacing the tables in insight data with table id only for saving in insights collection
2025-06-12 12:46:11,667 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,667 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Putting images as '' in insights data as it will be empty in modes other than data analysis.
2025-06-12 12:46:11,668 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,668 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Added token usage to insight_data before saving - Input: 2672, Output: 661, Total: 3333
  -> input_tokens: 2672, output_tokens: 661, total_tokens: 3333, response_id: 684a7ebbe52e67b3dd845e59
2025-06-12 12:46:11,668 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,668 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Linked token events with response_id: 684a7ebbe52e67b3dd845e59
  -> response_id: 684a7ebbe52e67b3dd845e59
2025-06-12 12:46:11,669 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,669 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Saving project data in mongo db insights collection for pdf processor
2025-06-12 12:46:11,669 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,669 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Saving project data in mongo db
2025-06-12 12:46:11,669 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,719 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Project exists in mongo db
2025-06-12 12:46:11,720 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,720 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - File does not exist
2025-06-12 12:46:11,720 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,774 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Project data saved in mongo db when file does not exist
2025-06-12 12:46:11,775 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,829 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Saved project data in mongo db for pdf processor
2025-06-12 12:46:11,829 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,829 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Replacing the tables in insight data with actual table data for response sending
2025-06-12 12:46:11,829 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,879 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Token usage already included in saved data - Input: 2672, Output: 661
  -> input_tokens: 2672, output_tokens: 661
2025-06-12 12:46:11,879 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,880 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - USAGE_STEP_SUMMARY - PROCESSING_COMPLETE | Total Operations: 3 | Cumulative Input Tokens: 2672 | Cumulative Output Tokens: 661 | Cumulative Total Tokens: 3333 | Self answer processing completed successfully
  -> step_name: PROCESSING_COMPLETE, total_operations: 3, cumulative_input_tokens: 2672, cumulative_output_tokens: 661, cumulative_total_tokens: 3333, additional_info: Self answer processing completed successfully
2025-06-12 12:46:11,880 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,881 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Saving unified usage events
  -> event_count: 3, query_id: unified_query_20250612_071518_803232
2025-06-12 12:46:11,881 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,935 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Successfully saved unified usage events
  -> document_id: 684a7ebbe52e67b3dd845e5a, total_operations: 3, total_tokens: 3333
2025-06-12 12:46:11,936 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,936 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Usage events linked to response
  -> response_id: 684a7ebbe52e67b3dd845e59
2025-06-12 12:46:11,936 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,936 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Successfully saved 3 usage events to MongoDB
  -> mongodb_id: 684a7ebbe52e67b3dd845e5a, events_count: 3, response_id: 684a7ebbe52e67b3dd845e59, query_id: unified_query_20250612_071518_803232
2025-06-12 12:46:11,936 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
2025-06-12 12:46:11,936 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - Usage events saved to MongoDB for self answer path
2025-06-12 12:46:11,936 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbm - 
