2025-06-12 15:57:44,503 - INFO - project_user.ovais-1.comtest1.0hvhvhc - File processing request started
  -> file_name: Medicaldataset.csv, response_type: Brief, custom_prompt_name: Featured_1
2025-06-12 15:57:44,503 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:44,503 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Token validation is turned OFF. Skipping authentication checks.
2025-06-12 15:57:44,503 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:44,503 - INFO - project_user.ovais-1.comtest1.0hvhvhc - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250612_102744_503407
2025-06-12 15:57:44,503 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:44,503 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Unified usage tracking initialized
  -> usage_logger_initialized: True
2025-06-12 15:57:44,503 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:44,503 - INFO - project_user.ovais-1.comtest1.0hvhvhc - USAGE_STEP_SUMMARY - INITIALIZATION | Total Operations: 0 | Cumulative Input Tokens: 0 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 0 | Unified usage logger created
  -> step_name: INITIALIZATION, total_operations: 0, cumulative_input_tokens: 0, cumulative_output_tokens: 0, cumulative_total_tokens: 0, additional_info: Unified usage logger created
2025-06-12 15:57:44,503 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:44,503 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Skipping restricted content check for empty or None prompt
2025-06-12 15:57:44,503 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:44,503 - INFO - project_user.ovais-1.comtest1.0hvhvhc - USAGE_STEP_SUMMARY - RESTRICTED_KEYWORDS_CHECK | Total Operations: 0 | Cumulative Input Tokens: 0 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 0 | Completed content moderation check
  -> step_name: RESTRICTED_KEYWORDS_CHECK, total_operations: 0, cumulative_input_tokens: 0, cumulative_output_tokens: 0, cumulative_total_tokens: 0, additional_info: Completed content moderation check
2025-06-12 15:57:44,503 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:44,503 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Processing file...
2025-06-12 15:57:44,503 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:44,503 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Request parameters
  -> file: Medicaldataset.csv, prompt: None, page_number: None, title: None, response_type: Brief, custom_prompt_name: Featured_1
2025-06-12 15:57:44,503 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:45,276 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Set default title to 'Untitled' for new project
  -> title: Untitled, exists: False
2025-06-12 15:57:45,277 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:45,279 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Created dirs: files/comtest1.0hvhvhc, files/comtest1.0hvhvhc/data_analysis, files/comtest1.0hvhvhc/non_data_analysis
  -> project_dir: files/comtest1.0hvhvhc, data_analysis_dir: files/comtest1.0hvhvhc/data_analysis, non_data_analysis_dir: files/comtest1.0hvhvhc/non_data_analysis
2025-06-12 15:57:45,279 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:45,280 - INFO - project_user.ovais-1.comtest1.0hvhvhc - File type determined as: csv from extension: csv and filename is : Medicaldataset
  -> file_type: csv, filename: Medicaldataset.csv, file_name_without_ext: Medicaldataset
2025-06-12 15:57:45,280 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:45,304 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Saved encrypted file to: files/comtest1.0hvhvhc/data_analysis/Medicaldataset.enc
  -> encrypted_file_path: files/comtest1.0hvhvhc/data_analysis/Medicaldataset.enc, file_name: Medicaldataset.csv
2025-06-12 15:57:45,304 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:45,329 - INFO - project_user.ovais-1.comtest1.0hvhvhc - CSV file processed successfully.
  -> file_name: Medicaldataset.csv
2025-06-12 15:57:45,330 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:45,330 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Saved file to local path: files/comtest1.0hvhvhc/data_analysis/Medicaldataset.csv
  -> file_path: files/comtest1.0hvhvhc/data_analysis/Medicaldataset.csv, file_name: Medicaldataset.csv
2025-06-12 15:57:45,330 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:45,342 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Generating intent from 707 characters of data analysis file text
  -> text_length: 707, file_name: Medicaldataset.csv
2025-06-12 15:57:45,342 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:55,008 - INFO - project_user.ovais-1.comtest1.0hvhvhc - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: intent_generation | Subtype: file_upload | Input Tokens: 530 | Output Tokens: 82 | Total Tokens: 612 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250612_102744_503407, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: intent_generation, operation_subtype: file_upload, input_tokens: 530, output_tokens: 82, total_tokens: 612, success: True, timestamp: 2025-06-12T10:27:55.008842+00:00
2025-06-12 15:57:55,009 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:55,009 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Intent generated successfully for data analysis mode: {'context_intent': {'intent_description': 'The context presents a dataset of patient health metrics and test results, likely for the purpose of medical analysis or diagnosis prediction.', 'keywords': ['patient data', 'health metrics', 'heart rate', 'blood pressure', 'blood sugar', 'CK-MB', 'Troponin', 'medical test results', 'diagnosis', 'positive', 'negative']}}
  -> intent: {'context_intent': {'intent_description': 'The context presents a dataset of patient health metri..., file_name: Medicaldataset.csv
2025-06-12 15:57:55,009 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:55,009 - INFO - project_user.ovais-1.comtest1.0hvhvhc - USAGE_STEP_SUMMARY - INTENT_GENERATION | Total Operations: 1 | Cumulative Input Tokens: 530 | Cumulative Output Tokens: 82 | Cumulative Total Tokens: 612 | Generated intent for data analysis file: Medicaldataset.csv
  -> step_name: INTENT_GENERATION, total_operations: 1, cumulative_input_tokens: 530, cumulative_output_tokens: 82, cumulative_total_tokens: 612, additional_info: Generated intent for data analysis file: Medicaldataset.csv
2025-06-12 15:57:55,009 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:55,009 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Entered data analysis mode
  -> custom_prompt_name: Featured_1, item: None
2025-06-12 15:57:55,009 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:55,009 - INFO - project_user.ovais-1.comtest1.0hvhvhc - USAGE_STEP_SUMMARY - DATA_ANALYSIS_START | Total Operations: 1 | Cumulative Input Tokens: 530 | Cumulative Output Tokens: 82 | Cumulative Total Tokens: 612 | Entered data analysis mode
  -> step_name: DATA_ANALYSIS_START, total_operations: 1, cumulative_input_tokens: 530, cumulative_output_tokens: 82, cumulative_total_tokens: 612, additional_info: Entered data analysis mode
2025-06-12 15:57:55,009 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:55,108 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Using detailed response assistant instructions
  -> response_type: Brief
2025-06-12 15:57:55,108 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:55,156 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Retrieved existing assistant ID
  -> existing_assistant_id: 
2025-06-12 15:57:55,156 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:55,205 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Retrieved existing thread ID
  -> existing_thread_id: 
2025-06-12 15:57:55,206 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:55,206 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Initializing new assistant
2025-06-12 15:57:55,206 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:55,253 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Retrieved model for project
  -> requested_model: deepseek-chat
2025-06-12 15:57:55,253 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:55,253 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Using assistant model
  -> assistant_model: gpt-4o-2024-08-06
2025-06-12 15:57:55,253 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:55,877 - INFO - project_user.ovais-1.comtest1.0hvhvhc - USAGE_CONSUMPTION - Category: llm | Provider: openai | Operation: assistant_management | Subtype: assistant_creation | Input Tokens: 632 | Output Tokens: 0 | Total Tokens: 632 | Running Total Operations: 2 | Success: True
  -> query_id: unified_query_20250612_102744_503407, service_category: llm, service_provider: openai, service_tier: paid, operation_type: assistant_management, operation_subtype: assistant_creation, input_tokens: 632, output_tokens: 0, total_tokens: 632, success: True, timestamp: 2025-06-12T10:27:55.877183+00:00
2025-06-12 15:57:55,877 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:55,877 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Initialized new assistant
  -> assistant_id: asst_OJq1hgFzsuHimEqTdOv2RH0c
2025-06-12 15:57:55,877 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:55,877 - INFO - project_user.ovais-1.comtest1.0hvhvhc - USAGE_STEP_SUMMARY - ASSISTANT_INITIALIZATION | Total Operations: 2 | Cumulative Input Tokens: 1162 | Cumulative Output Tokens: 82 | Cumulative Total Tokens: 1244 | Initialized assistant with model: gpt-4o-2024-08-06
  -> step_name: ASSISTANT_INITIALIZATION, total_operations: 2, cumulative_input_tokens: 1162, cumulative_output_tokens: 82, cumulative_total_tokens: 1244, additional_info: Initialized assistant with model: gpt-4o-2024-08-06
2025-06-12 15:57:55,877 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:56,347 - INFO - project_user.ovais-1.comtest1.0hvhvhc - USAGE_CONSUMPTION - Category: llm | Provider: openai | Operation: assistant_management | Subtype: thread_creation | Input Tokens: 0 | Output Tokens: 0 | Total Tokens: 0 | Running Total Operations: 3 | Success: True
  -> query_id: unified_query_20250612_102744_503407, service_category: llm, service_provider: openai, service_tier: paid, operation_type: assistant_management, operation_subtype: thread_creation, input_tokens: 0, output_tokens: 0, total_tokens: 0, success: True, timestamp: 2025-06-12T10:27:56.347719+00:00
2025-06-12 15:57:56,347 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:56,348 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Created new thread
  -> thread_id: thread_QQnQ5poAQNZQXg1LWpKWkQUY
2025-06-12 15:57:56,348 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:56,399 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Saved new assistant and thread IDs
  -> assistant_id: asst_OJq1hgFzsuHimEqTdOv2RH0c, thread_id: thread_QQnQ5poAQNZQXg1LWpKWkQUY
2025-06-12 15:57:56,399 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:56,448 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Retrieved model to be used for project
  -> users_selected_model: deepseek-chat
2025-06-12 15:57:56,448 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:56,448 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Processing file upload
  -> file_path: files/comtest1.0hvhvhc/data_analysis/Medicaldataset.csv, file_name: Medicaldataset.csv
2025-06-12 15:57:56,448 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:57,441 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Files uploaded successfully
  -> files_uploaded: [{'file_id': 'file-TJzTS9TiZsn54iMMY9M3Pi', 'file_name': 'Medicaldataset.csv'}]
2025-06-12 15:57:57,441 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:57,441 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Retrieved new file ID
  -> new_file_id: file-TJzTS9TiZsn54iMMY9M3Pi
2025-06-12 15:57:57,441 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:57,489 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Processing custom prompt
  -> custom_prompt_name: Featured_1
2025-06-12 15:57:57,489 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:57,489 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Extracted custom prompt content
  -> custom_prompt_content: Summarize the document
2025-06-12 15:57:57,489 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:57,541 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Appended new file data to project
2025-06-12 15:57:57,541 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:57,541 - INFO - project_user.ovais-1.comtest1.0hvhvhc - File uploaded, drafting response for file upload
2025-06-12 15:57:57,541 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:57,542 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Using custom prompt for file processing
  -> custom_prompt_name: Featured_1
2025-06-12 15:57:57,542 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:57:57,597 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Successfully read file content
  -> file_type: csv, content_length: 162359
2025-06-12 15:57:57,597 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:59:23,459 - INFO - project_user.ovais-1.comtest1.0hvhvhc - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: custom_prompt | Subtype: completion | Input Tokens: 45241 | Output Tokens: 1540 | Total Tokens: 46781 | Running Total Operations: 4 | Success: True
  -> query_id: unified_query_20250612_102744_503407, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: custom_prompt, operation_subtype: completion, input_tokens: 45241, output_tokens: 1540, total_tokens: 46781, success: True, timestamp: 2025-06-12T10:29:23.459492+00:00
2025-06-12 15:59:23,461 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:59:23,519 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Linked token events with response_id
  -> response_id: 684aac037dd4a606a0656748
2025-06-12 15:59:23,520 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:59:23,520 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Added token usage to response
  -> input_tokens: 46403, output_tokens: 1622, total_tokens: 48025
2025-06-12 15:59:23,520 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:59:23,520 - INFO - project_user.ovais-1.comtest1.0hvhvhc - File upload response prepared
  -> source_type: dataset, response_type: Brief
2025-06-12 15:59:23,520 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:59:23,681 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Saved response and prompt to mongo
  -> using_empty_prompt_for_custom: True
2025-06-12 15:59:23,681 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:59:23,730 - INFO - project_user.ovais-1.comtest1.0hvhvhc - USAGE_STEP_SUMMARY - DATA_ANALYSIS_COMPLETE | Total Operations: 4 | Cumulative Input Tokens: 46403 | Cumulative Output Tokens: 1622 | Cumulative Total Tokens: 48025 | Data analysis processing completed successfully
  -> step_name: DATA_ANALYSIS_COMPLETE, total_operations: 4, cumulative_input_tokens: 46403, cumulative_output_tokens: 1622, cumulative_total_tokens: 48025, additional_info: Data analysis processing completed successfully
2025-06-12 15:59:23,730 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:59:23,731 - INFO - project_user.ovais-1.comtest1.0hvhvhc - USAGE_STEP_SUMMARY - PROCESSING_COMPLETE | Total Operations: 4 | Cumulative Input Tokens: 46403 | Cumulative Output Tokens: 1622 | Cumulative Total Tokens: 48025 | Data analysis file processing completed successfully
  -> step_name: PROCESSING_COMPLETE, total_operations: 4, cumulative_input_tokens: 46403, cumulative_output_tokens: 1622, cumulative_total_tokens: 48025, additional_info: Data analysis file processing completed successfully
2025-06-12 15:59:23,731 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:59:23,731 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Saving unified usage events
  -> event_count: 4, query_id: unified_query_20250612_102744_503407
2025-06-12 15:59:23,731 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:59:23,780 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Successfully saved unified usage events
  -> document_id: 684aac037dd4a606a0656749, total_operations: 4, total_tokens: 48025
2025-06-12 15:59:23,780 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:59:23,781 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Usage events linked to response
  -> response_id: 684aac037dd4a606a0656748
2025-06-12 15:59:23,781 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:59:23,781 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Successfully saved 4 usage events to MongoDB
  -> mongodb_id: 684aac037dd4a606a0656749, events_count: 4, response_id: 684aac037dd4a606a0656748, query_id: unified_query_20250612_102744_503407
2025-06-12 15:59:23,781 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
2025-06-12 15:59:23,781 - INFO - project_user.ovais-1.comtest1.0hvhvhc - Usage events saved to MongoDB for data analysis file upload path
2025-06-12 15:59:23,781 - INFO - project_user.ovais-1.comtest1.0hvhvhc - 
