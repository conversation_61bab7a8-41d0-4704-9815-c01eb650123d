2025-06-12 15:45:41,405 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Get project files called
2025-06-12 15:45:41,405 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:45:41,405 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Token validation is turned OFF. Skipping authentication checks.
2025-06-12 15:45:41,405 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:45:42,070 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Converting object_id to project_id
  -> object_id: 684aa464325f3855df5387ef, actual_project_id: comtest1.0
2025-06-12 15:45:42,071 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:45:42,379 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - File details retrieved successfully
  -> file_count: 1, actual_project_id: comtest1.0
2025-06-12 15:45:42,379 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:46:57,183 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Compliance report generation started
  -> framework_type: PII, generate_pdf: True
2025-06-12 15:46:57,184 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:46:57,184 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Token validation is turned OFF. Skipping authentication checks.
2025-06-12 15:46:57,184 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:46:57,185 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250612_101657_185126
2025-06-12 15:46:57,185 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:46:57,185 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Unified usage tracking initialized for compliance report generation
  -> usage_logger_query_id: unified_query_20250612_101657_185126
2025-06-12 15:46:57,185 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:46:57,185 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - USAGE_STEP_SUMMARY - INITIALIZATION | Total Operations: 0 | Cumulative Input Tokens: 0 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 0 | Unified usage logger created for compliance report generation
  -> step_name: INITIALIZATION, total_operations: 0, cumulative_input_tokens: 0, cumulative_output_tokens: 0, cumulative_total_tokens: 0, additional_info: Unified usage logger created for compliance report generation
2025-06-12 15:46:57,185 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:46:57,232 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - File IDs received for processing
  -> file_ids_raw: ["54f62a57-e2cb-44c3-ae72-c9fd5a3cc671"], generate_pdf: True, file_ids_type: str
2025-06-12 15:46:57,232 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:46:57,233 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - File IDs parsed successfully
  -> file_ids: ['54f62a57-e2cb-44c3-ae72-c9fd5a3cc671'], file_ids_count: 1, parsed_type: list
2025-06-12 15:46:57,233 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:46:57,717 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - File names resolved from file IDs
  -> file_names: ['big file.pdf'], file_count: 1
2025-06-12 15:46:57,717 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:46:57,718 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Generating combined PDF compliance report
  -> framework_type: PII, file_count: 1
2025-06-12 15:46:57,718 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:46:57,718 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - PDF file path generated
  -> pdf_path: files/comtest1.0/compliance_reports/temp_8f58b9bc6eb84652b845aa078152ad2e_PII_compliance.pdf, temp_filename: temp_8f58b9bc6eb84652b845aa078152ad2e_PII_compliance.pdf
2025-06-12 15:46:57,718 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:46:57,719 - WARNING - project_user.ovais-1.684aa464325f3855df5387ef - Report file not found: files/comtest1.0/compliance_reports/big file_PII_compliance.json
  -> file_name: big file.pdf, report_path: files/comtest1.0/compliance_reports/big file_PII_compliance.json, framework_type: PII
2025-06-12 15:46:57,719 - WARNING - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:46:57,719 - ERROR - project_user.ovais-1.684aa464325f3855df5387ef - No valid compliance reports found for the requested files. Missing reports for: big file.pdf
  -> missing_files: ['big file.pdf'], framework_type: PII
2025-06-12 15:46:57,719 - ERROR - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:47:05,946 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Compliance report generation started
  -> framework_type: PII, generate_pdf: False
2025-06-12 15:47:05,947 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:47:05,947 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Token validation is turned OFF. Skipping authentication checks.
2025-06-12 15:47:05,947 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:47:05,947 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250612_101705_947821
2025-06-12 15:47:05,947 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:47:05,948 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Unified usage tracking initialized for compliance report generation
  -> usage_logger_query_id: unified_query_20250612_101705_947821
2025-06-12 15:47:05,948 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:47:05,948 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - USAGE_STEP_SUMMARY - INITIALIZATION | Total Operations: 0 | Cumulative Input Tokens: 0 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 0 | Unified usage logger created for compliance report generation
  -> step_name: INITIALIZATION, total_operations: 0, cumulative_input_tokens: 0, cumulative_output_tokens: 0, cumulative_total_tokens: 0, additional_info: Unified usage logger created for compliance report generation
2025-06-12 15:47:05,948 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:47:05,995 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - File IDs received for processing
  -> file_ids_raw: ["54f62a57-e2cb-44c3-ae72-c9fd5a3cc671"], generate_pdf: False, file_ids_type: str
2025-06-12 15:47:05,995 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:47:05,996 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - File IDs parsed successfully
  -> file_ids: ['54f62a57-e2cb-44c3-ae72-c9fd5a3cc671'], file_ids_count: 1, parsed_type: list
2025-06-12 15:47:05,996 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:47:06,299 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - File names resolved from file IDs
  -> file_names: ['big file.pdf'], file_count: 1
2025-06-12 15:47:06,300 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:47:06,300 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Generating individual compliance reports
  -> file_count: 1, framework_type: PII
2025-06-12 15:47:06,300 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:47:06,300 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Processing file: big file.pdf
  -> file_name: big file.pdf, framework_type: PII
2025-06-12 15:47:06,300 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:47:06,301 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Reading pickle file: files/comtest1.0/non_data_analysis/big file.pkl
  -> file_name: big file.pdf, pkl_file_path: files/comtest1.0/non_data_analysis/big file.pkl, file_ext: pdf
2025-06-12 15:47:06,301 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:47:06,319 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - File content read successfully
  -> file_name: big file.pdf, content_size_bytes: 612610
2025-06-12 15:47:06,319 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:47:06,319 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Generating compliance report for big file.pdf
  -> file_name: big file.pdf, framework_type: PII
2025-06-12 15:47:06,319 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:47:06,700 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: compliance_report | Subtype: generation | Input Tokens: 131755 | Output Tokens: 0 | Total Tokens: 131755 | Running Total Operations: 1 | Success: False
  -> query_id: unified_query_20250612_101705_947821, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: compliance_report, operation_subtype: generation, input_tokens: 131755, output_tokens: 0, total_tokens: 131755, success: False, timestamp: 2025-06-12T10:17:06.700754+00:00
2025-06-12 15:47:06,700 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:47:06,700 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: compliance_report | Subtype: generation | Input Tokens: 131755 | Output Tokens: 0 | Total Tokens: 131755 | Running Total Operations: 2 | Success: False
  -> query_id: unified_query_20250612_101705_947821, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: compliance_report, operation_subtype: generation, input_tokens: 131755, output_tokens: 0, total_tokens: 131755, success: False, timestamp: 2025-06-12T10:17:06.700886+00:00
2025-06-12 15:47:06,700 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:47:06,701 - ERROR - project_user.ovais-1.684aa464325f3855df5387ef - Error processing file big file.pdf: Your request contains too much text to process. Please reduce the amount of text and try again.
  -> file_name: big file.pdf, http_status: 400, detail: Your request contains too much text to process. Please reduce the amount of text and try again., framework_type: PII
2025-06-12 15:47:06,701 - ERROR - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:48:09,637 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Compliance report generation started
  -> framework_type: PII, generate_pdf: False
2025-06-12 15:48:09,638 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:48:09,638 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Token validation is turned OFF. Skipping authentication checks.
2025-06-12 15:48:09,638 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:48:09,639 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250612_101809_638984
2025-06-12 15:48:09,639 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:48:09,639 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Unified usage tracking initialized for compliance report generation
  -> usage_logger_query_id: unified_query_20250612_101809_638984
2025-06-12 15:48:09,639 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:48:09,639 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - USAGE_STEP_SUMMARY - INITIALIZATION | Total Operations: 0 | Cumulative Input Tokens: 0 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 0 | Unified usage logger created for compliance report generation
  -> step_name: INITIALIZATION, total_operations: 0, cumulative_input_tokens: 0, cumulative_output_tokens: 0, cumulative_total_tokens: 0, additional_info: Unified usage logger created for compliance report generation
2025-06-12 15:48:09,639 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:48:10,992 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - File IDs received for processing
  -> file_ids_raw: ["54f62a57-e2cb-44c3-ae72-c9fd5a3cc671"], generate_pdf: False, file_ids_type: str
2025-06-12 15:48:10,993 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:48:10,993 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - File IDs parsed successfully
  -> file_ids: ['54f62a57-e2cb-44c3-ae72-c9fd5a3cc671'], file_ids_count: 1, parsed_type: list
2025-06-12 15:48:10,993 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:48:11,981 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - File names resolved from file IDs
  -> file_names: ['big file.pdf'], file_count: 1
2025-06-12 15:48:11,982 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:48:11,982 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Generating individual compliance reports
  -> file_count: 1, framework_type: PII
2025-06-12 15:48:11,982 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:48:11,982 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Processing file: big file.pdf
  -> file_name: big file.pdf, framework_type: PII
2025-06-12 15:48:11,982 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:48:11,983 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Reading pickle file: files/comtest1.0/non_data_analysis/big file.pkl
  -> file_name: big file.pdf, pkl_file_path: files/comtest1.0/non_data_analysis/big file.pkl, file_ext: pdf
2025-06-12 15:48:11,983 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:48:11,998 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - File content read successfully
  -> file_name: big file.pdf, content_size_bytes: 612610
2025-06-12 15:48:11,998 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:48:11,998 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - Generating compliance report for big file.pdf
  -> file_name: big file.pdf, framework_type: PII
2025-06-12 15:48:11,999 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:48:12,522 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: compliance_report | Subtype: generation | Input Tokens: 131755 | Output Tokens: 0 | Total Tokens: 131755 | Running Total Operations: 1 | Success: False
  -> query_id: unified_query_20250612_101809_638984, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: compliance_report, operation_subtype: generation, input_tokens: 131755, output_tokens: 0, total_tokens: 131755, success: False, timestamp: 2025-06-12T10:18:12.522512+00:00
2025-06-12 15:48:12,522 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:48:12,522 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: compliance_report | Subtype: generation | Input Tokens: 131755 | Output Tokens: 0 | Total Tokens: 131755 | Running Total Operations: 2 | Success: False
  -> query_id: unified_query_20250612_101809_638984, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: compliance_report, operation_subtype: generation, input_tokens: 131755, output_tokens: 0, total_tokens: 131755, success: False, timestamp: 2025-06-12T10:18:12.522664+00:00
2025-06-12 15:48:12,522 - INFO - project_user.ovais-1.684aa464325f3855df5387ef - 
2025-06-12 15:48:12,523 - ERROR - project_user.ovais-1.684aa464325f3855df5387ef - Error processing file big file.pdf: Your request contains too much text to process. Please reduce the amount of text and try again.
  -> file_name: big file.pdf, http_status: 400, detail: Your request contains too much text to process. Please reduce the amount of text and try again., framework_type: PII
2025-06-12 15:48:12,523 - ERROR - project_user.ovais-1.684aa464325f3855df5387ef - 
