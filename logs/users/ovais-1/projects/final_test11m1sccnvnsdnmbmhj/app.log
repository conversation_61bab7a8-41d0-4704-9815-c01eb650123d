2025-06-12 12:46:51,110 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - File processing request started
  -> file_name: None, response_type: Brief, custom_prompt_name: None
2025-06-12 12:46:51,110 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:46:51,110 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Token validation is turned OFF. Skipping authentication checks.
2025-06-12 12:46:51,110 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:46:51,110 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250612_071651_110841
2025-06-12 12:46:51,110 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:46:51,110 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Unified usage tracking initialized
  -> usage_logger_initialized: True
2025-06-12 12:46:51,111 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:46:51,111 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - USAGE_STEP_SUMMARY - INITIALIZATION | Total Operations: 0 | Cumulative Input Tokens: 0 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 0 | Unified usage logger created
  -> step_name: INITIALIZATION, total_operations: 0, cumulative_input_tokens: 0, cumulative_output_tokens: 0, cumulative_total_tokens: 0, additional_info: Unified usage logger created
2025-06-12 12:46:51,111 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:46:55,712 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: content_moderation | Subtype: restricted_keywords_check | Input Tokens: 81 | Output Tokens: 1 | Total Tokens: 82 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250612_071651_110841, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: content_moderation, operation_subtype: restricted_keywords_check, input_tokens: 81, output_tokens: 1, total_tokens: 82, success: True, timestamp: 2025-06-12T07:16:55.711889+00:00
2025-06-12 12:46:55,712 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:46:55,713 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - USAGE_STEP_SUMMARY - RESTRICTED_KEYWORDS_CHECK | Total Operations: 1 | Cumulative Input Tokens: 81 | Cumulative Output Tokens: 1 | Cumulative Total Tokens: 82 | Completed content moderation check
  -> step_name: RESTRICTED_KEYWORDS_CHECK, total_operations: 1, cumulative_input_tokens: 81, cumulative_output_tokens: 1, cumulative_total_tokens: 82, additional_info: Completed content moderation check
2025-06-12 12:46:55,713 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:46:55,713 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Processing file...
2025-06-12 12:46:55,713 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:46:55,713 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Request parameters
  -> file: None, prompt: create a line chart for units sold in east region from some dummy data, page_number: None, title: None, response_type: Brief, custom_prompt_name: None
2025-06-12 12:46:55,713 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:46:55,816 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Set default title to 'Untitled' for new project
  -> title: Untitled, exists: False
2025-06-12 12:46:55,817 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:46:55,817 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Entered else block in process file route
2025-06-12 12:46:55,817 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:46:55,868 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Length of previous_responses before removing images: 0
  -> previous_responses_length: 0
2025-06-12 12:46:55,868 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:46:55,869 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Removed images from previous_responses
2025-06-12 12:46:55,869 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:46:55,869 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Length of previous_responses after removing images: 0
  -> previous_responses_length_after: 0
2025-06-12 12:46:55,869 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:14,860 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: query_routing | Subtype: source_selection | Input Tokens: 1116 | Output Tokens: 265 | Total Tokens: 1381 | Running Total Operations: 2 | Success: True
  -> query_id: unified_query_20250612_071651_110841, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: query_routing, operation_subtype: source_selection, input_tokens: 1116, output_tokens: 265, total_tokens: 1381, success: True, timestamp: 2025-06-12T07:17:14.860649+00:00
2025-06-12 12:47:14,860 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:14,861 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - source_to_select: {'intent': 'instruction', 'query': 'create a line chart for units sold in east region from some dummy data', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': "I can generate a line chart for units sold in the East region using dummy data. Here's an example of how the data might look and the corresponding chart:\n\n**Dummy Data:**\n| Month | Units Sold (East) |\n|-------|-------------------|\n| Jan   | 150               |\n| Feb   | 200               |\n| Mar   | 180               |\n| Apr   | 220               |\n| May   | 250               |\n| Jun   | 230               |\n\n**Line Chart:**\n```\nUnits Sold in East Region\n300 |\n    |       /\\\n250 |     /  \\\n    |   /    \\\n200 | /      \\\n    |        \\\n150 |         \\\n    ------------------\n    Jan Feb Mar Apr May Jun\n```\n\nWould you like me to refine the chart or adjust the data in any way?"}
  -> source_to_select: {'intent': 'instruction', 'query': 'create a line chart for units sold in east region from some d...
2025-06-12 12:47:14,861 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:14,861 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - type of source_to_select: <class 'dict'>
  -> source_type: <class 'dict'>
2025-06-12 12:47:14,861 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:14,861 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - source_to_select after processing: {'intent': 'instruction', 'query': 'create a line chart for units sold in east region from some dummy data', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': "I can generate a line chart for units sold in the East region using dummy data. Here's an example of how the data might look and the corresponding chart:\n\n**Dummy Data:**\n| Month | Units Sold (East) |\n|-------|-------------------|\n| Jan   | 150               |\n| Feb   | 200               |\n| Mar   | 180               |\n| Apr   | 220               |\n| May   | 250               |\n| Jun   | 230               |\n\n**Line Chart:**\n```\nUnits Sold in East Region\n300 |\n    |       /\\\n250 |     /  \\\n    |   /    \\\n200 | /      \\\n    |        \\\n150 |         \\\n    ------------------\n    Jan Feb Mar Apr May Jun\n```\n\nWould you like me to refine the chart or adjust the data in any way?"}
  -> source_to_select_processed: {'intent': 'instruction', 'query': 'create a line chart for units sold in east region from some d...
2025-06-12 12:47:14,862 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:14,862 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - USAGE_STEP_SUMMARY - SOURCE_SELECTION | Total Operations: 2 | Cumulative Input Tokens: 1197 | Cumulative Output Tokens: 266 | Cumulative Total Tokens: 1463 | Selected source: instruction
  -> step_name: SOURCE_SELECTION, total_operations: 2, cumulative_input_tokens: 1197, cumulative_output_tokens: 266, cumulative_total_tokens: 1463, additional_info: Selected source: instruction
2025-06-12 12:47:14,862 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:14,862 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - source_to_select['source'][0]['FileTool']: False
  -> file_tool: False
2025-06-12 12:47:14,862 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:14,862 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - source_to_select['source'][0]['WebSeach']: False
  -> web_search: False
2025-06-12 12:47:14,863 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:14,863 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - source_to_select['source'][0]['SelfAnswer']: True
  -> self_answer: True
2025-06-12 12:47:14,863 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:14,863 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Processing self answer
2025-06-12 12:47:14,863 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:14,863 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - --------------------PAGES, self answer---------------------
2025-06-12 12:47:14,863 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:14,863 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Valid page numbers: []
  -> valid_page_numbers: []
2025-06-12 12:47:14,863 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:14,864 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - STREAMING_START - Self answer streaming initiated
  -> stream_type: self_answer, source_type: self_answer, query: create a line chart for units sold in east region from some dummy data, response_type: Brief, visualization: False
2025-06-12 12:47:14,864 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:14,864 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - USAGE_STEP_SUMMARY - MAIN_LLM_RESPONSE | Total Operations: 2 | Cumulative Input Tokens: 1197 | Cumulative Output Tokens: 266 | Cumulative Total Tokens: 1463 | Starting main LLM response generation for self answer
  -> step_name: MAIN_LLM_RESPONSE, total_operations: 2, cumulative_input_tokens: 1197, cumulative_output_tokens: 266, cumulative_total_tokens: 1463, additional_info: Starting main LLM response generation for self answer
2025-06-12 12:47:14,864 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:14,864 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - STREAMING_START - Non-data analysis real-time streaming initiated
  -> stream_type: non_data_analysis_realtime, function: fake_llm_resonse, source_type: , prompt_length: 70, extracted_text_length: 0, chat_history_length: 0, response_type: Brief, visualization: False
2025-06-12 12:47:14,864 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:14,932 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Starting real-time insight generation
2025-06-12 12:47:14,932 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:19,029 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Received chunk #1
  -> chunk_length: 0, chunk_preview: EMPTY
2025-06-12 12:47:19,029 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:19,030 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Received chunk #2
  -> chunk_length: 3, chunk_preview: ```
2025-06-12 12:47:19,030 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:19,298 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Received chunk #3
  -> chunk_length: 4, chunk_preview: json
2025-06-12 12:47:19,298 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:19,383 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Received chunk #4
  -> chunk_length: 1, chunk_preview: 

2025-06-12 12:47:19,383 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:19,474 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Received chunk #5
  -> chunk_length: 2, chunk_preview: {

2025-06-12 12:47:19,474 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:24,821 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Summary streaming completed - boundary detected
  -> summary_length: 523
2025-06-12 12:47:24,821 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,605 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: insight_generation | Subtype: realtime_streaming | Input Tokens: 1475 | Output Tokens: 339 | Total Tokens: 1814 | Running Total Operations: 3 | Success: True
  -> query_id: unified_query_20250612_071651_110841, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: insight_generation, operation_subtype: realtime_streaming, input_tokens: 1475, output_tokens: 339, total_tokens: 1814, success: True, timestamp: 2025-06-12T07:17:35.605377+00:00
2025-06-12 12:47:35,605 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,605 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Real-time streaming completed successfully
  -> total_content_length: 1422, summary_length: 515, streaming_summary: True, summary_complete: True, content_preview: ```json
{
    "summary": "**Units Sold in East Region - Dummy Data Analysis**\n\nBased on the req..., content_ends_with:  marketing strategies were implemented during the peak sales months in the East region?"
    ]
}
```, total_chunks_received: 349
2025-06-12 12:47:35,606 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,606 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Removed markdown code block wrapper from JSON
  -> original_length: 1422, cleaned_length: 1410
2025-06-12 12:47:35,606 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,606 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - JSON detection check
  -> is_json_like: True, starts_with_brace: True, ends_with_brace: True, content_start: {
    "summary": "**, content_end: ast region?"
    ]
}
2025-06-12 12:47:35,606 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,606 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Transformed table data to match expected schema
  -> original_table_count: 1, transformed_table_count: 1, original_keys: [['name', 'type', 'description', 'labels', 'values']], transformed_keys: [['chart_name', 'type_of_chart', 'chart_decription', 'x_labels', 'values', 'y_labels']]
2025-06-12 12:47:35,606 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,607 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Skipped title addition - content was already streamed
  -> summary_length: 523
2025-06-12 12:47:35,607 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,607 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Successfully parsed streamed content as structured JSON
  -> summary_length: 523, tables_count: 1, questions_count: 4
2025-06-12 12:47:35,607 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,659 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - No file provided, use existing data from MongoDB for pdf processing
2025-06-12 12:47:35,660 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,660 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - file_context: 0
  -> extracted_text_length: 0
2025-06-12 12:47:35,660 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,660 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - valid_page_numbers incase of no file provided in request: []
  -> valid_page_numbers: []
2025-06-12 12:47:35,660 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,661 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Entered not llm call block in pdf processor
2025-06-12 12:47:35,661 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,661 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Using existing insight_data - skipping LLM call to prevent duplication
  -> insight_data_available: True, source_type: self_answer, insight_data_type: dict, has_summary: True
2025-06-12 12:47:35,661 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,661 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Tables found in insight data, Preparing to save tables in mongo db tables collection
2025-06-12 12:47:35,661 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,661 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Starting to save tables to tables collection
2025-06-12 12:47:35,661 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,773 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Tables saved in mongo db tables collection with id: %s
  -> tables_collection_id: 684a7f0fe52e67b3dd845e5c
2025-06-12 12:47:35,773 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,774 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Replacing the tables in insight data with table id only for saving in insights collection
2025-06-12 12:47:35,774 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,774 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Putting images as '' in insights data as it will be empty in modes other than data analysis.
2025-06-12 12:47:35,774 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,774 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Added token usage to insight_data before saving - Input: 2672, Output: 605, Total: 3277
  -> input_tokens: 2672, output_tokens: 605, total_tokens: 3277, response_id: 684a7f0fe52e67b3dd845e5d
2025-06-12 12:47:35,774 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,774 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Linked token events with response_id: 684a7f0fe52e67b3dd845e5d
  -> response_id: 684a7f0fe52e67b3dd845e5d
2025-06-12 12:47:35,774 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,775 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Saving project data in mongo db insights collection for pdf processor
2025-06-12 12:47:35,775 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,775 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Saving project data in mongo db
2025-06-12 12:47:35,775 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,826 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Project exists in mongo db
2025-06-12 12:47:35,827 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,827 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - File does not exist
2025-06-12 12:47:35,828 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,884 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Project data saved in mongo db when file does not exist
2025-06-12 12:47:35,884 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,939 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Saved project data in mongo db for pdf processor
2025-06-12 12:47:35,939 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,939 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Replacing the tables in insight data with actual table data for response sending
2025-06-12 12:47:35,939 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,989 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Token usage already included in saved data - Input: 2672, Output: 605
  -> input_tokens: 2672, output_tokens: 605
2025-06-12 12:47:35,990 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,991 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - USAGE_STEP_SUMMARY - PROCESSING_COMPLETE | Total Operations: 3 | Cumulative Input Tokens: 2672 | Cumulative Output Tokens: 605 | Cumulative Total Tokens: 3277 | Self answer processing completed successfully
  -> step_name: PROCESSING_COMPLETE, total_operations: 3, cumulative_input_tokens: 2672, cumulative_output_tokens: 605, cumulative_total_tokens: 3277, additional_info: Self answer processing completed successfully
2025-06-12 12:47:35,991 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:35,992 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Saving unified usage events
  -> event_count: 3, query_id: unified_query_20250612_071651_110841
2025-06-12 12:47:35,992 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:36,046 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Successfully saved unified usage events
  -> document_id: 684a7f0fe52e67b3dd845e5e, total_operations: 3, total_tokens: 3277
2025-06-12 12:47:36,047 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:36,047 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Usage events linked to response
  -> response_id: 684a7f0fe52e67b3dd845e5d
2025-06-12 12:47:36,047 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:36,048 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Successfully saved 3 usage events to MongoDB
  -> mongodb_id: 684a7f0fe52e67b3dd845e5e, events_count: 3, response_id: 684a7f0fe52e67b3dd845e5d, query_id: unified_query_20250612_071651_110841
2025-06-12 12:47:36,048 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:47:36,048 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Usage events saved to MongoDB for self answer path
2025-06-12 12:47:36,048 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:42,881 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - File processing request started
  -> file_name: None, response_type: Brief, custom_prompt_name: None
2025-06-12 12:52:42,882 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:42,882 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Token validation is turned OFF. Skipping authentication checks.
2025-06-12 12:52:42,882 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:42,882 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250612_072242_882780
2025-06-12 12:52:42,882 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:42,882 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Unified usage tracking initialized
  -> usage_logger_initialized: True
2025-06-12 12:52:42,882 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:42,883 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - USAGE_STEP_SUMMARY - INITIALIZATION | Total Operations: 0 | Cumulative Input Tokens: 0 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 0 | Unified usage logger created
  -> step_name: INITIALIZATION, total_operations: 0, cumulative_input_tokens: 0, cumulative_output_tokens: 0, cumulative_total_tokens: 0, additional_info: Unified usage logger created
2025-06-12 12:52:42,883 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:48,963 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: content_moderation | Subtype: restricted_keywords_check | Input Tokens: 70 | Output Tokens: 1 | Total Tokens: 71 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250612_072242_882780, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: content_moderation, operation_subtype: restricted_keywords_check, input_tokens: 70, output_tokens: 1, total_tokens: 71, success: True, timestamp: 2025-06-12T07:22:48.963026+00:00
2025-06-12 12:52:48,964 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:48,964 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - USAGE_STEP_SUMMARY - RESTRICTED_KEYWORDS_CHECK | Total Operations: 1 | Cumulative Input Tokens: 70 | Cumulative Output Tokens: 1 | Cumulative Total Tokens: 71 | Completed content moderation check
  -> step_name: RESTRICTED_KEYWORDS_CHECK, total_operations: 1, cumulative_input_tokens: 70, cumulative_output_tokens: 1, cumulative_total_tokens: 71, additional_info: Completed content moderation check
2025-06-12 12:52:48,964 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:48,964 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Processing file...
2025-06-12 12:52:48,964 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:48,964 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Request parameters
  -> file: None, prompt: who are you, page_number: None, title: None, response_type: Brief, custom_prompt_name: None
2025-06-12 12:52:48,964 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:49,029 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Using existing title: Untitled for project final_test11m1sccnvnsdnmbmhj
  -> title: Untitled, exists: True
2025-06-12 12:52:49,029 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:49,029 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Entered else block in process file route
2025-06-12 12:52:49,029 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:49,086 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Length of previous_responses before removing images: 1
  -> previous_responses_length: 1
2025-06-12 12:52:49,087 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:49,087 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Removed images from previous_responses
2025-06-12 12:52:49,087 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:49,087 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Length of previous_responses after removing images: 1
  -> previous_responses_length_after: 1
2025-06-12 12:52:49,087 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:59,112 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: query_routing | Subtype: source_selection | Input Tokens: 1416 | Output Tokens: 47 | Total Tokens: 1463 | Running Total Operations: 2 | Success: True
  -> query_id: unified_query_20250612_072242_882780, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: query_routing, operation_subtype: source_selection, input_tokens: 1416, output_tokens: 47, total_tokens: 1463, success: True, timestamp: 2025-06-12T07:22:59.111864+00:00
2025-06-12 12:52:59,112 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:59,112 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - source_to_select: {'intent': 'general_query', 'query': '', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': ''}
  -> source_to_select: {'intent': 'general_query', 'query': '', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach...
2025-06-12 12:52:59,112 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:59,113 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - type of source_to_select: <class 'dict'>
  -> source_type: <class 'dict'>
2025-06-12 12:52:59,113 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:59,113 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - source_to_select after processing: {'intent': 'general_query', 'query': '', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': ''}
  -> source_to_select_processed: {'intent': 'general_query', 'query': '', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach...
2025-06-12 12:52:59,113 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:59,113 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - USAGE_STEP_SUMMARY - SOURCE_SELECTION | Total Operations: 2 | Cumulative Input Tokens: 1486 | Cumulative Output Tokens: 48 | Cumulative Total Tokens: 1534 | Selected source: general_query
  -> step_name: SOURCE_SELECTION, total_operations: 2, cumulative_input_tokens: 1486, cumulative_output_tokens: 48, cumulative_total_tokens: 1534, additional_info: Selected source: general_query
2025-06-12 12:52:59,113 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:59,113 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - source_to_select['source'][0]['FileTool']: False
  -> file_tool: False
2025-06-12 12:52:59,113 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:59,113 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - source_to_select['source'][0]['WebSeach']: False
  -> web_search: False
2025-06-12 12:52:59,113 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:59,113 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - source_to_select['source'][0]['SelfAnswer']: True
  -> self_answer: True
2025-06-12 12:52:59,113 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:59,113 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Processing self answer
2025-06-12 12:52:59,113 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:59,114 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - --------------------PAGES, self answer---------------------
2025-06-12 12:52:59,114 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:59,114 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Valid page numbers: []
  -> valid_page_numbers: []
2025-06-12 12:52:59,114 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:59,116 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - STREAMING_START - Self answer streaming initiated
  -> stream_type: self_answer, source_type: self_answer, query: None, response_type: Brief, visualization: False
2025-06-12 12:52:59,116 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:59,116 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - USAGE_STEP_SUMMARY - MAIN_LLM_RESPONSE | Total Operations: 2 | Cumulative Input Tokens: 1486 | Cumulative Output Tokens: 48 | Cumulative Total Tokens: 1534 | Starting main LLM response generation for self answer
  -> step_name: MAIN_LLM_RESPONSE, total_operations: 2, cumulative_input_tokens: 1486, cumulative_output_tokens: 48, cumulative_total_tokens: 1534, additional_info: Starting main LLM response generation for self answer
2025-06-12 12:52:59,116 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:59,116 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - STREAMING_START - Non-data analysis real-time streaming initiated
  -> stream_type: non_data_analysis_realtime, function: fake_llm_resonse, source_type: , prompt_length: 0, extracted_text_length: 0, chat_history_length: 1, response_type: Brief, visualization: False
2025-06-12 12:52:59,116 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:52:59,185 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Starting real-time insight generation
2025-06-12 12:52:59,186 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:53:02,643 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Received chunk #1
  -> chunk_length: 0, chunk_preview: EMPTY
2025-06-12 12:53:02,644 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:53:02,644 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Received chunk #2
  -> chunk_length: 3, chunk_preview: ```
2025-06-12 12:53:02,644 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:53:03,376 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Received chunk #3
  -> chunk_length: 4, chunk_preview: json
2025-06-12 12:53:03,376 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:53:03,471 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Received chunk #4
  -> chunk_length: 1, chunk_preview: 

2025-06-12 12:53:03,472 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
2025-06-12 12:53:03,567 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - Received chunk #5
  -> chunk_length: 2, chunk_preview: {

2025-06-12 12:53:03,568 - INFO - project_user.ovais-1.final_test11m1sccnvnsdnmbmhj - 
