2025-06-12 11:06:35,915 - INFO - project_user.ovais-1.final_test11m1 - File processing request started
  -> file_name: None, response_type: Brief, custom_prompt_name: None
2025-06-12 11:06:35,915 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:35,915 - INFO - project_user.ovais-1.final_test11m1 - Token validation is turned OFF. Skipping authentication checks.
2025-06-12 11:06:35,916 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:35,916 - INFO - project_user.ovais-1.final_test11m1 - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250612_053635_916102
2025-06-12 11:06:35,916 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:35,916 - INFO - project_user.ovais-1.final_test11m1 - Unified usage tracking initialized
  -> usage_logger_initialized: True
2025-06-12 11:06:35,916 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:35,916 - INFO - project_user.ovais-1.final_test11m1 - USAGE_STEP_SUMMARY - INITIALIZATION | Total Operations: 0 | Cumulative Input Tokens: 0 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 0 | Unified usage logger created
  -> step_name: INITIALIZATION, total_operations: 0, cumulative_input_tokens: 0, cumulative_output_tokens: 0, cumulative_total_tokens: 0, additional_info: Unified usage logger created
2025-06-12 11:06:35,916 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:41,727 - INFO - project_user.ovais-1.final_test11m1 - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: content_moderation | Subtype: restricted_keywords_check | Input Tokens: 70 | Output Tokens: 1 | Total Tokens: 71 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250612_053635_916102, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: content_moderation, operation_subtype: restricted_keywords_check, input_tokens: 70, output_tokens: 1, total_tokens: 71, success: True, timestamp: 2025-06-12T05:36:41.727221+00:00
2025-06-12 11:06:41,727 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:41,728 - INFO - project_user.ovais-1.final_test11m1 - USAGE_STEP_SUMMARY - RESTRICTED_KEYWORDS_CHECK | Total Operations: 1 | Cumulative Input Tokens: 70 | Cumulative Output Tokens: 1 | Cumulative Total Tokens: 71 | Completed content moderation check
  -> step_name: RESTRICTED_KEYWORDS_CHECK, total_operations: 1, cumulative_input_tokens: 70, cumulative_output_tokens: 1, cumulative_total_tokens: 71, additional_info: Completed content moderation check
2025-06-12 11:06:41,728 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:41,728 - INFO - project_user.ovais-1.final_test11m1 - Processing file...
2025-06-12 11:06:41,728 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:41,728 - INFO - project_user.ovais-1.final_test11m1 - Request parameters
  -> file: None, prompt: What is GDPR, page_number: None, title: None, response_type: Brief, custom_prompt_name: None
2025-06-12 11:06:41,728 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:41,881 - INFO - project_user.ovais-1.final_test11m1 - Set default title to 'Untitled' for new project
  -> title: Untitled, exists: False
2025-06-12 11:06:41,881 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:41,881 - INFO - project_user.ovais-1.final_test11m1 - Entered else block in process file route
2025-06-12 11:06:41,881 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:41,949 - INFO - project_user.ovais-1.final_test11m1 - Length of previous_responses before removing images: 0
  -> previous_responses_length: 0
2025-06-12 11:06:41,949 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:41,949 - INFO - project_user.ovais-1.final_test11m1 - Removed images from previous_responses
2025-06-12 11:06:41,949 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:41,949 - INFO - project_user.ovais-1.final_test11m1 - Length of previous_responses after removing images: 0
  -> previous_responses_length_after: 0
2025-06-12 11:06:41,949 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:54,765 - INFO - project_user.ovais-1.final_test11m1 - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: query_routing | Subtype: source_selection | Input Tokens: 1105 | Output Tokens: 126 | Total Tokens: 1231 | Running Total Operations: 2 | Success: True
  -> query_id: unified_query_20250612_053635_916102, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: query_routing, operation_subtype: source_selection, input_tokens: 1105, output_tokens: 126, total_tokens: 1231, success: True, timestamp: 2025-06-12T05:36:54.765290+00:00
2025-06-12 11:06:54,766 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:54,766 - INFO - project_user.ovais-1.final_test11m1 - source_to_select: {'intent': 'self-answer', 'query': 'What is GDPR?', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': 'The General Data Protection Regulation (GDPR) is a regulation in EU law on data protection and privacy in the European Union and the European Economic Area. It also addresses the transfer of personal data outside the EU and EEA areas. The GDPR aims to give individuals control over their personal data and to simplify the regulatory environment for international business by unifying the regulation within the EU.'}
  -> source_to_select: {'intent': 'self-answer', 'query': 'What is GDPR?', 'source': [{'Filename': [], 'FileTool': False...
2025-06-12 11:06:54,767 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:54,767 - INFO - project_user.ovais-1.final_test11m1 - type of source_to_select: <class 'dict'>
  -> source_type: <class 'dict'>
2025-06-12 11:06:54,768 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:54,769 - INFO - project_user.ovais-1.final_test11m1 - source_to_select after processing: {'intent': 'self-answer', 'query': 'What is GDPR?', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': 'The General Data Protection Regulation (GDPR) is a regulation in EU law on data protection and privacy in the European Union and the European Economic Area. It also addresses the transfer of personal data outside the EU and EEA areas. The GDPR aims to give individuals control over their personal data and to simplify the regulatory environment for international business by unifying the regulation within the EU.'}
  -> source_to_select_processed: {'intent': 'self-answer', 'query': 'What is GDPR?', 'source': [{'Filename': [], 'FileTool': False...
2025-06-12 11:06:54,769 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:54,770 - INFO - project_user.ovais-1.final_test11m1 - USAGE_STEP_SUMMARY - SOURCE_SELECTION | Total Operations: 2 | Cumulative Input Tokens: 1175 | Cumulative Output Tokens: 127 | Cumulative Total Tokens: 1302 | Selected source: self-answer
  -> step_name: SOURCE_SELECTION, total_operations: 2, cumulative_input_tokens: 1175, cumulative_output_tokens: 127, cumulative_total_tokens: 1302, additional_info: Selected source: self-answer
2025-06-12 11:06:54,770 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:54,771 - INFO - project_user.ovais-1.final_test11m1 - source_to_select['source'][0]['FileTool']: False
  -> file_tool: False
2025-06-12 11:06:54,771 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:54,772 - INFO - project_user.ovais-1.final_test11m1 - source_to_select['source'][0]['WebSeach']: False
  -> web_search: False
2025-06-12 11:06:54,772 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:54,772 - INFO - project_user.ovais-1.final_test11m1 - source_to_select['source'][0]['SelfAnswer']: True
  -> self_answer: True
2025-06-12 11:06:54,773 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:54,773 - INFO - project_user.ovais-1.final_test11m1 - Processing self answer
2025-06-12 11:06:54,773 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:54,773 - INFO - project_user.ovais-1.final_test11m1 - --------------------PAGES, self answer---------------------
2025-06-12 11:06:54,774 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:54,774 - INFO - project_user.ovais-1.final_test11m1 - Valid page numbers: []
  -> valid_page_numbers: []
2025-06-12 11:06:54,774 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:54,775 - INFO - project_user.ovais-1.final_test11m1 - STREAMING_START - Self answer streaming initiated
  -> stream_type: self_answer, source_type: self_answer, query: What is GDPR?, response_type: Brief, visualization: False
2025-06-12 11:06:54,775 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:54,775 - INFO - project_user.ovais-1.final_test11m1 - USAGE_STEP_SUMMARY - MAIN_LLM_RESPONSE | Total Operations: 2 | Cumulative Input Tokens: 1175 | Cumulative Output Tokens: 127 | Cumulative Total Tokens: 1302 | Starting main LLM response generation for self answer
  -> step_name: MAIN_LLM_RESPONSE, total_operations: 2, cumulative_input_tokens: 1175, cumulative_output_tokens: 127, cumulative_total_tokens: 1302, additional_info: Starting main LLM response generation for self answer
2025-06-12 11:06:54,775 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:54,775 - INFO - project_user.ovais-1.final_test11m1 - STREAMING_START - Non-data analysis real-time streaming initiated
  -> stream_type: non_data_analysis_realtime, function: fake_llm_resonse, source_type: , prompt_length: 13, extracted_text_length: 0, chat_history_length: 0, response_type: Brief, visualization: False
2025-06-12 11:06:54,776 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:06:54,855 - INFO - project_user.ovais-1.final_test11m1 - Starting real-time insight generation
2025-06-12 11:06:54,856 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:15,919 - INFO - project_user.ovais-1.final_test11m1 - Summary streaming completed - boundary detected
  -> summary_length: 1952
2025-06-12 11:07:15,921 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,240 - INFO - project_user.ovais-1.final_test11m1 - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: insight_generation | Subtype: realtime_streaming | Input Tokens: 1464 | Output Tokens: 476 | Total Tokens: 1940 | Running Total Operations: 3 | Success: True
  -> query_id: unified_query_20250612_053635_916102, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: insight_generation, operation_subtype: realtime_streaming, input_tokens: 1464, output_tokens: 476, total_tokens: 1940, success: True, timestamp: 2025-06-12T05:37:19.240501+00:00
2025-06-12 11:07:19,240 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,241 - INFO - project_user.ovais-1.final_test11m1 - Real-time streaming completed successfully
  -> total_content_length: 2378, summary_length: 1936, streaming_summary: True, summary_complete: True
2025-06-12 11:07:19,241 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,241 - WARNING - project_user.ovais-1.final_test11m1 - Failed to parse streamed content as JSON: Expecting value: line 1 column 1 (char 0)
2025-06-12 11:07:19,241 - WARNING - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,241 - INFO - project_user.ovais-1.final_test11m1 - Using already streamed content for fallback response
  -> streamed_content_length: 1936
2025-06-12 11:07:19,241 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,241 - INFO - project_user.ovais-1.final_test11m1 - Created fallback structured response from extracted summary
  -> summary_length: 1936
2025-06-12 11:07:19,241 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,307 - INFO - project_user.ovais-1.final_test11m1 - No file provided, use existing data from MongoDB for pdf processing
2025-06-12 11:07:19,308 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,308 - INFO - project_user.ovais-1.final_test11m1 - file_context: 0
  -> extracted_text_length: 0
2025-06-12 11:07:19,308 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,308 - INFO - project_user.ovais-1.final_test11m1 - valid_page_numbers incase of no file provided in request: []
  -> valid_page_numbers: []
2025-06-12 11:07:19,309 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,309 - INFO - project_user.ovais-1.final_test11m1 - Entered not llm call block in pdf processor
2025-06-12 11:07:19,309 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,309 - INFO - project_user.ovais-1.final_test11m1 - Using existing insight_data - skipping LLM call to prevent duplication
  -> insight_data_available: True, source_type: self_answer, insight_data_type: dict, has_summary: True
2025-06-12 11:07:19,309 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,309 - INFO - project_user.ovais-1.final_test11m1 - Putting images as '' in insights data as it will be empty in modes other than data analysis.
2025-06-12 11:07:19,309 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,310 - INFO - project_user.ovais-1.final_test11m1 - Added token usage to insight_data before saving - Input: 2639, Output: 603, Total: 3242
  -> input_tokens: 2639, output_tokens: 603, total_tokens: 3242, response_id: 684a678fddb2d6374033720c
2025-06-12 11:07:19,310 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,310 - INFO - project_user.ovais-1.final_test11m1 - Linked token events with response_id: 684a678fddb2d6374033720c
  -> response_id: 684a678fddb2d6374033720c
2025-06-12 11:07:19,310 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,310 - INFO - project_user.ovais-1.final_test11m1 - Saving project data in mongo db insights collection for pdf processor
2025-06-12 11:07:19,310 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,311 - INFO - project_user.ovais-1.final_test11m1 - Saving project data in mongo db
2025-06-12 11:07:19,311 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,373 - INFO - project_user.ovais-1.final_test11m1 - Project exists in mongo db
2025-06-12 11:07:19,373 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,373 - INFO - project_user.ovais-1.final_test11m1 - File does not exist
2025-06-12 11:07:19,374 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,463 - INFO - project_user.ovais-1.final_test11m1 - Project data saved in mongo db when file does not exist
2025-06-12 11:07:19,463 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,549 - INFO - project_user.ovais-1.final_test11m1 - Saved project data in mongo db for pdf processor
2025-06-12 11:07:19,549 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,550 - INFO - project_user.ovais-1.final_test11m1 - Replacing the tables in insight data with actual table data for response sending
2025-06-12 11:07:19,550 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,618 - INFO - project_user.ovais-1.final_test11m1 - Token usage already included in saved data - Input: 2639, Output: 603
  -> input_tokens: 2639, output_tokens: 603
2025-06-12 11:07:19,618 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,619 - INFO - project_user.ovais-1.final_test11m1 - USAGE_STEP_SUMMARY - PROCESSING_COMPLETE | Total Operations: 3 | Cumulative Input Tokens: 2639 | Cumulative Output Tokens: 603 | Cumulative Total Tokens: 3242 | Self answer processing completed successfully
  -> step_name: PROCESSING_COMPLETE, total_operations: 3, cumulative_input_tokens: 2639, cumulative_output_tokens: 603, cumulative_total_tokens: 3242, additional_info: Self answer processing completed successfully
2025-06-12 11:07:19,619 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,620 - INFO - project_user.ovais-1.final_test11m1 - Saving unified usage events
  -> event_count: 3, query_id: unified_query_20250612_053635_916102
2025-06-12 11:07:19,620 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,695 - INFO - project_user.ovais-1.final_test11m1 - Successfully saved unified usage events
  -> document_id: 684a678fddb2d6374033720d, total_operations: 3, total_tokens: 3242
2025-06-12 11:07:19,695 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,696 - INFO - project_user.ovais-1.final_test11m1 - Usage events linked to response
  -> response_id: 684a678fddb2d6374033720c
2025-06-12 11:07:19,696 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,696 - INFO - project_user.ovais-1.final_test11m1 - Successfully saved 3 usage events to MongoDB
  -> mongodb_id: 684a678fddb2d6374033720d, events_count: 3, response_id: 684a678fddb2d6374033720c, query_id: unified_query_20250612_053635_916102
2025-06-12 11:07:19,696 - INFO - project_user.ovais-1.final_test11m1 - 
2025-06-12 11:07:19,696 - INFO - project_user.ovais-1.final_test11m1 - Usage events saved to MongoDB for self answer path
2025-06-12 11:07:19,696 - INFO - project_user.ovais-1.final_test11m1 - 
