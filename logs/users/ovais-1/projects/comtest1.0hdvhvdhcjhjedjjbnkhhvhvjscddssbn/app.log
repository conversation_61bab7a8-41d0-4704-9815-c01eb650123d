2025-06-13 10:39:21,244 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - File processing request started
  -> file_name: None, response_type: Brief, custom_prompt_name: None
2025-06-13 10:39:21,244 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:21,244 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Token validation is turned OFF. Skipping authentication checks.
2025-06-13 10:39:21,244 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:21,244 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250613_050921_244688
2025-06-13 10:39:21,244 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:21,244 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Unified usage tracking initialized
  -> usage_logger_initialized: True
2025-06-13 10:39:21,244 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:21,244 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - USAGE_STEP_SUMMARY - INITIALIZATION | Total Operations: 0 | Cumulative Input Tokens: 0 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 0 | Unified usage logger created
  -> step_name: INITIALIZATION, total_operations: 0, cumulative_input_tokens: 0, cumulative_output_tokens: 0, cumulative_total_tokens: 0, additional_info: Unified usage logger created
2025-06-13 10:39:21,244 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:22,728 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - USAGE_CONSUMPTION - Category: llm | Provider: other | Operation: content_moderation | Subtype: restricted_keywords_check | Input Tokens: 76 | Output Tokens: 0 | Total Tokens: 76 | Running Total Operations: 1 | Success: False
  -> query_id: unified_query_20250613_050921_244688, service_category: llm, service_provider: other, service_tier: unknown, operation_type: content_moderation, operation_subtype: restricted_keywords_check, input_tokens: 76, output_tokens: 0, total_tokens: 76, success: False, timestamp: 2025-06-13T05:09:22.728028+00:00
2025-06-13 10:39:22,728 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:22,729 - ERROR - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Error checking restricted keywords: 500: We're experiencing some technical difficulties. Please try again in a few moments. If the issue persists, contact your administrator.
  -> error: 500: We're experiencing some technical difficulties. Please try again in a few moments. If the is...
2025-06-13 10:39:22,729 - ERROR - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:22,729 - WARNING - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Continuing with processing despite error in restricted content check
2025-06-13 10:39:22,729 - WARNING - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:22,729 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - USAGE_STEP_SUMMARY - RESTRICTED_KEYWORDS_CHECK | Total Operations: 1 | Cumulative Input Tokens: 76 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 76 | Completed content moderation check
  -> step_name: RESTRICTED_KEYWORDS_CHECK, total_operations: 1, cumulative_input_tokens: 76, cumulative_output_tokens: 0, cumulative_total_tokens: 76, additional_info: Completed content moderation check
2025-06-13 10:39:22,729 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:22,729 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Processing file...
2025-06-13 10:39:22,729 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:22,729 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Request parameters
  -> file: None, prompt: create a dummy bar chart from some dummy data, page_number: None, title: None, response_type: Brief, custom_prompt_name: None
2025-06-13 10:39:22,729 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:22,899 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Set default title to 'Untitled' for new project
  -> title: Untitled, exists: False
2025-06-13 10:39:22,899 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:22,899 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Entered else block in process file route
2025-06-13 10:39:22,899 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:22,967 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Length of previous_responses before removing images: 0
  -> previous_responses_length: 0
2025-06-13 10:39:22,968 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:22,968 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Removed images from previous_responses
2025-06-13 10:39:22,968 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:22,968 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Length of previous_responses after removing images: 0
  -> previous_responses_length_after: 0
2025-06-13 10:39:22,968 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,052 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - USAGE_CONSUMPTION - Category: llm | Provider: other | Operation: query_routing | Subtype: source_selection | Input Tokens: 1111 | Output Tokens: 25 | Total Tokens: 1136 | Running Total Operations: 2 | Success: True
  -> query_id: unified_query_20250613_050921_244688, service_category: llm, service_provider: other, service_tier: unknown, operation_type: query_routing, operation_subtype: source_selection, input_tokens: 1111, output_tokens: 25, total_tokens: 1136, success: True, timestamp: 2025-06-13T05:09:23.052713+00:00
2025-06-13 10:39:23,052 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,052 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - source_to_select: {'intent': 'general_query', 'query': '', 'source': [], 'clarification': '', 'answer': ''}
  -> source_to_select: {'intent': 'general_query', 'query': '', 'source': [], 'clarification': '', 'answer': ''}
2025-06-13 10:39:23,052 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,053 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - type of source_to_select: <class 'dict'>
  -> source_type: <class 'dict'>
2025-06-13 10:39:23,053 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,053 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - source_to_select after processing: {'intent': 'general_query', 'query': '', 'source': [], 'clarification': '', 'answer': ''}
  -> source_to_select_processed: {'intent': 'general_query', 'query': '', 'source': [], 'clarification': '', 'answer': ''}
2025-06-13 10:39:23,053 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,053 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - USAGE_STEP_SUMMARY - SOURCE_SELECTION | Total Operations: 2 | Cumulative Input Tokens: 1187 | Cumulative Output Tokens: 25 | Cumulative Total Tokens: 1212 | Selected source: general_query
  -> step_name: SOURCE_SELECTION, total_operations: 2, cumulative_input_tokens: 1187, cumulative_output_tokens: 25, cumulative_total_tokens: 1212, additional_info: Selected source: general_query
2025-06-13 10:39:23,053 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,053 - ERROR - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Error in web search: list index out of range
  -> error: list index out of range
2025-06-13 10:39:23,053 - ERROR - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,053 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Switching to search in recently updated file
2025-06-13 10:39:23,053 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,054 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - STREAMING_START - PDF processing streaming initiated
  -> stream_type: pdf_processing, file_name: None, file_type: None, prompt_length: 45, response_type: Brief, visualization: False, llm_call: False
2025-06-13 10:39:23,054 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,140 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - No file provided, use existing data from MongoDB for pdf processing
2025-06-13 10:39:23,141 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,141 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - file_context: 0
  -> extracted_text_length: 0
2025-06-13 10:39:23,141 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,142 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - valid_page_numbers incase of no file provided in request: []
  -> valid_page_numbers: []
2025-06-13 10:39:23,142 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,142 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Entered not llm call block in pdf processor
2025-06-13 10:39:23,142 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,142 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - No existing insight_data available - calling generate_insight
  -> insight_data_available: False
2025-06-13 10:39:23,142 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,212 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - STREAMING_START - PDF processor streaming initiated
  -> stream_type: pdf_processor, prompt_length: 45, extracted_text_length: 0, response_type: Brief, visualization: False, valid_page_numbers: []
2025-06-13 10:39:23,212 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,213 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - STREAMING_START - Non-data analysis real-time streaming initiated
  -> stream_type: non_data_analysis_realtime, function: fake_llm_resonse, source_type: file, prompt_length: 45, extracted_text_length: 0, chat_history_length: 0, response_type: Brief, visualization: False
2025-06-13 10:39:23,213 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,287 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Starting real-time insight generation
2025-06-13 10:39:23,287 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,295 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - USAGE_CONSUMPTION - Category: vision | Provider: other | Operation: insight_generation | Subtype: realtime_streaming | Input Tokens: 1512 | Output Tokens: 0 | Total Tokens: 1512 | Running Total Operations: 3 | Success: False
  -> query_id: unified_query_20250613_050921_244688, service_category: vision, service_provider: other, service_tier: unknown, operation_type: insight_generation, operation_subtype: realtime_streaming, input_tokens: 1512, output_tokens: 0, total_tokens: 1512, success: False, timestamp: 2025-06-13T05:09:23.295456+00:00
2025-06-13 10:39:23,295 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,296 - ERROR - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Real-time streaming failed: GenerativeServiceAsyncClient.stream_generate_content() got an unexpected keyword argument 'temperature'
2025-06-13 10:39:23,296 - ERROR - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,296 - ERROR - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Error in fake_llm_response: {str(e)}
2025-06-13 10:39:23,296 - ERROR - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,297 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - USAGE_STEP_SUMMARY - PROCESSING_COMPLETE | Total Operations: 3 | Cumulative Input Tokens: 2699 | Cumulative Output Tokens: 25 | Cumulative Total Tokens: 2724 | File processing completed successfully
  -> step_name: PROCESSING_COMPLETE, total_operations: 3, cumulative_input_tokens: 2699, cumulative_output_tokens: 25, cumulative_total_tokens: 2724, additional_info: File processing completed successfully
2025-06-13 10:39:23,297 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,297 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Saving unified usage events
  -> event_count: 3, query_id: unified_query_20250613_050921_244688
2025-06-13 10:39:23,298 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,370 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Successfully saved unified usage events
  -> document_id: 684bb283b492ae78459a09a8, total_operations: 3, total_tokens: 2724
2025-06-13 10:39:23,371 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,371 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Successfully saved 3 usage events to MongoDB
  -> mongodb_id: 684bb283b492ae78459a09a8, events_count: 3, response_id: None, query_id: unified_query_20250613_050921_244688
2025-06-13 10:39:23,371 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:39:23,372 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Usage events saved to MongoDB for final event generator path
2025-06-13 10:39:23,372 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:40,767 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - File processing request started
  -> file_name: None, response_type: Brief, custom_prompt_name: None
2025-06-13 10:47:40,768 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:40,768 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Token validation is turned OFF. Skipping authentication checks.
2025-06-13 10:47:40,768 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:40,768 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250613_051740_768285
2025-06-13 10:47:40,768 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:40,768 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Unified usage tracking initialized
  -> usage_logger_initialized: True
2025-06-13 10:47:40,768 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:40,768 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - USAGE_STEP_SUMMARY - INITIALIZATION | Total Operations: 0 | Cumulative Input Tokens: 0 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 0 | Unified usage logger created
  -> step_name: INITIALIZATION, total_operations: 0, cumulative_input_tokens: 0, cumulative_output_tokens: 0, cumulative_total_tokens: 0, additional_info: Unified usage logger created
2025-06-13 10:47:40,768 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:44,068 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - USAGE_CONSUMPTION - Category: llm | Provider: other | Operation: content_moderation | Subtype: restricted_keywords_check | Input Tokens: 76 | Output Tokens: 0 | Total Tokens: 76 | Running Total Operations: 1 | Success: False
  -> query_id: unified_query_20250613_051740_768285, service_category: llm, service_provider: other, service_tier: unknown, operation_type: content_moderation, operation_subtype: restricted_keywords_check, input_tokens: 76, output_tokens: 0, total_tokens: 76, success: False, timestamp: 2025-06-13T05:17:44.067954+00:00
2025-06-13 10:47:44,069 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:44,071 - ERROR - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Error checking restricted keywords: 500: We're experiencing some technical difficulties. Please try again in a few moments. If the issue persists, contact your administrator.
  -> error: 500: We're experiencing some technical difficulties. Please try again in a few moments. If the is...
2025-06-13 10:47:44,072 - ERROR - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:44,072 - WARNING - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Continuing with processing despite error in restricted content check
2025-06-13 10:47:44,072 - WARNING - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:44,072 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - USAGE_STEP_SUMMARY - RESTRICTED_KEYWORDS_CHECK | Total Operations: 1 | Cumulative Input Tokens: 76 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 76 | Completed content moderation check
  -> step_name: RESTRICTED_KEYWORDS_CHECK, total_operations: 1, cumulative_input_tokens: 76, cumulative_output_tokens: 0, cumulative_total_tokens: 76, additional_info: Completed content moderation check
2025-06-13 10:47:44,072 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:44,072 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Processing file...
2025-06-13 10:47:44,072 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:44,072 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Request parameters
  -> file: None, prompt: create a dummy bar chart from some dummy data, page_number: None, title: None, response_type: Brief, custom_prompt_name: None
2025-06-13 10:47:44,072 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:44,176 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Using existing title: Untitled for project comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn
  -> title: Untitled, exists: True
2025-06-13 10:47:44,176 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:44,176 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Entered else block in process file route
2025-06-13 10:47:44,176 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:44,246 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Length of previous_responses before removing images: 0
  -> previous_responses_length: 0
2025-06-13 10:47:44,246 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:44,248 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Removed images from previous_responses
2025-06-13 10:47:44,248 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:44,248 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Length of previous_responses after removing images: 0
  -> previous_responses_length_after: 0
2025-06-13 10:47:44,248 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:46,266 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - USAGE_CONSUMPTION - Category: llm | Provider: other | Operation: query_routing | Subtype: source_selection | Input Tokens: 1111 | Output Tokens: 25 | Total Tokens: 1136 | Running Total Operations: 2 | Success: True
  -> query_id: unified_query_20250613_051740_768285, service_category: llm, service_provider: other, service_tier: unknown, operation_type: query_routing, operation_subtype: source_selection, input_tokens: 1111, output_tokens: 25, total_tokens: 1136, success: True, timestamp: 2025-06-13T05:17:46.266227+00:00
2025-06-13 10:47:46,266 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:46,266 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - source_to_select: {'intent': 'general_query', 'query': '', 'source': [], 'clarification': '', 'answer': ''}
  -> source_to_select: {'intent': 'general_query', 'query': '', 'source': [], 'clarification': '', 'answer': ''}
2025-06-13 10:47:46,266 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:46,267 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - type of source_to_select: <class 'dict'>
  -> source_type: <class 'dict'>
2025-06-13 10:47:46,267 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:46,267 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - source_to_select after processing: {'intent': 'general_query', 'query': '', 'source': [], 'clarification': '', 'answer': ''}
  -> source_to_select_processed: {'intent': 'general_query', 'query': '', 'source': [], 'clarification': '', 'answer': ''}
2025-06-13 10:47:46,267 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:46,267 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - USAGE_STEP_SUMMARY - SOURCE_SELECTION | Total Operations: 2 | Cumulative Input Tokens: 1187 | Cumulative Output Tokens: 25 | Cumulative Total Tokens: 1212 | Selected source: general_query
  -> step_name: SOURCE_SELECTION, total_operations: 2, cumulative_input_tokens: 1187, cumulative_output_tokens: 25, cumulative_total_tokens: 1212, additional_info: Selected source: general_query
2025-06-13 10:47:46,267 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:46,267 - ERROR - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Error in web search: list index out of range
  -> error: list index out of range
2025-06-13 10:47:46,267 - ERROR - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:46,267 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Switching to search in recently updated file
2025-06-13 10:47:46,267 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:46,271 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - STREAMING_START - PDF processing streaming initiated
  -> stream_type: pdf_processing, file_name: None, file_type: None, prompt_length: 45, response_type: Brief, visualization: False, llm_call: False
2025-06-13 10:47:46,271 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:46,344 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - No file provided, use existing data from MongoDB for pdf processing
2025-06-13 10:47:46,344 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:46,344 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - file_context: 0
  -> extracted_text_length: 0
2025-06-13 10:47:46,344 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:46,344 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - valid_page_numbers incase of no file provided in request: []
  -> valid_page_numbers: []
2025-06-13 10:47:46,344 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:46,344 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Entered not llm call block in pdf processor
2025-06-13 10:47:46,344 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:46,344 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - No existing insight_data available - calling generate_insight
  -> insight_data_available: False
2025-06-13 10:47:46,344 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:46,431 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - STREAMING_START - PDF processor streaming initiated
  -> stream_type: pdf_processor, prompt_length: 45, extracted_text_length: 0, response_type: Brief, visualization: False, valid_page_numbers: []
2025-06-13 10:47:46,431 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:46,431 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - STREAMING_START - Non-data analysis real-time streaming initiated
  -> stream_type: non_data_analysis_realtime, function: fake_llm_resonse, source_type: file, prompt_length: 45, extracted_text_length: 0, chat_history_length: 0, response_type: Brief, visualization: False
2025-06-13 10:47:46,431 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:46,510 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Starting real-time insight generation
2025-06-13 10:47:46,510 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:48,383 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - USAGE_CONSUMPTION - Category: vision | Provider: other | Operation: insight_generation | Subtype: realtime_streaming | Input Tokens: 1512 | Output Tokens: 0 | Total Tokens: 1512 | Running Total Operations: 3 | Success: False
  -> query_id: unified_query_20250613_051740_768285, service_category: vision, service_provider: other, service_tier: unknown, operation_type: insight_generation, operation_subtype: realtime_streaming, input_tokens: 1512, output_tokens: 0, total_tokens: 1512, success: False, timestamp: 2025-06-13T05:17:48.383219+00:00
2025-06-13 10:47:48,383 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:48,384 - ERROR - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Real-time streaming failed: Invalid argument provided to Gemini: 400 API Key not found. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API Key not found. Please pass a valid API key."
]
2025-06-13 10:47:48,384 - ERROR - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:48,384 - ERROR - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Error in fake_llm_response: {str(e)}
2025-06-13 10:47:48,384 - ERROR - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:48,386 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - USAGE_STEP_SUMMARY - PROCESSING_COMPLETE | Total Operations: 3 | Cumulative Input Tokens: 2699 | Cumulative Output Tokens: 25 | Cumulative Total Tokens: 2724 | File processing completed successfully
  -> step_name: PROCESSING_COMPLETE, total_operations: 3, cumulative_input_tokens: 2699, cumulative_output_tokens: 25, cumulative_total_tokens: 2724, additional_info: File processing completed successfully
2025-06-13 10:47:48,386 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:48,387 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Saving unified usage events
  -> event_count: 3, query_id: unified_query_20250613_051740_768285
2025-06-13 10:47:48,387 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:48,459 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Successfully saved unified usage events
  -> document_id: 684bb47c93a4521d2fca25b2, total_operations: 3, total_tokens: 2724
2025-06-13 10:47:48,459 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:48,460 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Successfully saved 3 usage events to MongoDB
  -> mongodb_id: 684bb47c93a4521d2fca25b2, events_count: 3, response_id: None, query_id: unified_query_20250613_051740_768285
2025-06-13 10:47:48,460 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
2025-06-13 10:47:48,460 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - Usage events saved to MongoDB for final event generator path
2025-06-13 10:47:48,460 - INFO - project_user.ovais-1.comtest1.0hdvhvdhcjhjedjjbnkhhvhvjscddssbn - 
