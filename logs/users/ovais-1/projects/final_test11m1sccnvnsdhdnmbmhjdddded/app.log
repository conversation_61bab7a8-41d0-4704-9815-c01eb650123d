2025-06-12 15:15:02,089 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - File processing request started
  -> file_name: None, response_type: Brief, custom_prompt_name: None
2025-06-12 15:15:02,089 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:02,090 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Token validation is turned OFF. Skipping authentication checks.
2025-06-12 15:15:02,090 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:02,090 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250612_094502_090342
2025-06-12 15:15:02,090 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:02,090 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Unified usage tracking initialized
  -> usage_logger_initialized: True
2025-06-12 15:15:02,090 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:02,090 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - USAGE_STEP_SUMMARY - INITIALIZATION | Total Operations: 0 | Cumulative Input Tokens: 0 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 0 | Unified usage logger created
  -> step_name: INITIALIZATION, total_operations: 0, cumulative_input_tokens: 0, cumulative_output_tokens: 0, cumulative_total_tokens: 0, additional_info: Unified usage logger created
2025-06-12 15:15:02,090 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:06,514 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: content_moderation | Subtype: restricted_keywords_check | Input Tokens: 81 | Output Tokens: 1 | Total Tokens: 82 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250612_094502_090342, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: content_moderation, operation_subtype: restricted_keywords_check, input_tokens: 81, output_tokens: 1, total_tokens: 82, success: True, timestamp: 2025-06-12T09:45:06.514227+00:00
2025-06-12 15:15:06,514 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:06,515 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - USAGE_STEP_SUMMARY - RESTRICTED_KEYWORDS_CHECK | Total Operations: 1 | Cumulative Input Tokens: 81 | Cumulative Output Tokens: 1 | Cumulative Total Tokens: 82 | Completed content moderation check
  -> step_name: RESTRICTED_KEYWORDS_CHECK, total_operations: 1, cumulative_input_tokens: 81, cumulative_output_tokens: 1, cumulative_total_tokens: 82, additional_info: Completed content moderation check
2025-06-12 15:15:06,515 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:06,515 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Processing file...
2025-06-12 15:15:06,515 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:06,515 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Request parameters
  -> file: None, prompt: choose some dummy revenue data and create a descriptive statistics in tabular format, page_number: None, title: None, response_type: Brief, custom_prompt_name: None
2025-06-12 15:15:06,515 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:06,607 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Set default title to 'Untitled' for new project
  -> title: Untitled, exists: False
2025-06-12 15:15:06,607 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:06,607 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Entered else block in process file route
2025-06-12 15:15:06,608 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:06,652 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Length of previous_responses before removing images: 0
  -> previous_responses_length: 0
2025-06-12 15:15:06,653 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:06,653 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Removed images from previous_responses
2025-06-12 15:15:06,653 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:06,653 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Length of previous_responses after removing images: 0
  -> previous_responses_length_after: 0
2025-06-12 15:15:06,653 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:22,600 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: query_routing | Subtype: source_selection | Input Tokens: 1116 | Output Tokens: 229 | Total Tokens: 1345 | Running Total Operations: 2 | Success: True
  -> query_id: unified_query_20250612_094502_090342, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: query_routing, operation_subtype: source_selection, input_tokens: 1116, output_tokens: 229, total_tokens: 1345, success: True, timestamp: 2025-06-12T09:45:22.600621+00:00
2025-06-12 15:15:22,600 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:22,601 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - source_to_select: {'intent': 'self-answer', 'query': 'Generate dummy revenue data and create a descriptive statistics table', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': 'Here is a table with dummy revenue data and its descriptive statistics:\n\n| Month    | Revenue ($) |\n|----------|-------------|\n| January  | 12000       |\n| February | 15000       |\n| March    | 18000       |\n| April    | 14000       |\n| May      | 16000       |\n| June     | 20000       |\n\nDescriptive Statistics:\n\n| Statistic       | Value ($) |\n|-----------------|-----------|\n| Mean           | 15833.33  |\n| Median         | 15500.00  |\n| Minimum        | 12000.00  |\n| Maximum        | 20000.00  |\n| Standard Deviation | 2764.91  |'}
  -> source_to_select: {'intent': 'self-answer', 'query': 'Generate dummy revenue data and create a descriptive statisti...
2025-06-12 15:15:22,601 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:22,601 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - type of source_to_select: <class 'dict'>
  -> source_type: <class 'dict'>
2025-06-12 15:15:22,601 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:22,601 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - source_to_select after processing: {'intent': 'self-answer', 'query': 'Generate dummy revenue data and create a descriptive statistics table', 'source': [{'Filename': [], 'FileTool': False, 'WebSeach': False, 'SelfAnswer': True}], 'clarification': '', 'answer': 'Here is a table with dummy revenue data and its descriptive statistics:\n\n| Month    | Revenue ($) |\n|----------|-------------|\n| January  | 12000       |\n| February | 15000       |\n| March    | 18000       |\n| April    | 14000       |\n| May      | 16000       |\n| June     | 20000       |\n\nDescriptive Statistics:\n\n| Statistic       | Value ($) |\n|-----------------|-----------|\n| Mean           | 15833.33  |\n| Median         | 15500.00  |\n| Minimum        | 12000.00  |\n| Maximum        | 20000.00  |\n| Standard Deviation | 2764.91  |'}
  -> source_to_select_processed: {'intent': 'self-answer', 'query': 'Generate dummy revenue data and create a descriptive statisti...
2025-06-12 15:15:22,601 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:22,601 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - USAGE_STEP_SUMMARY - SOURCE_SELECTION | Total Operations: 2 | Cumulative Input Tokens: 1197 | Cumulative Output Tokens: 230 | Cumulative Total Tokens: 1427 | Selected source: self-answer
  -> step_name: SOURCE_SELECTION, total_operations: 2, cumulative_input_tokens: 1197, cumulative_output_tokens: 230, cumulative_total_tokens: 1427, additional_info: Selected source: self-answer
2025-06-12 15:15:22,601 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:22,601 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - source_to_select['source'][0]['FileTool']: False
  -> file_tool: False
2025-06-12 15:15:22,601 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:22,601 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - source_to_select['source'][0]['WebSeach']: False
  -> web_search: False
2025-06-12 15:15:22,601 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:22,601 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - source_to_select['source'][0]['SelfAnswer']: True
  -> self_answer: True
2025-06-12 15:15:22,602 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:22,602 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Processing self answer
2025-06-12 15:15:22,602 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:22,602 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - --------------------PAGES, self answer---------------------
2025-06-12 15:15:22,602 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:22,602 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Valid page numbers: []
  -> valid_page_numbers: []
2025-06-12 15:15:22,602 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:22,603 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - STREAMING_START - Self answer streaming initiated
  -> stream_type: self_answer, source_type: self_answer, query: Generate dummy revenue data and create a descriptive statistics table, response_type: Brief, visualization: False
2025-06-12 15:15:22,603 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:22,603 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - USAGE_STEP_SUMMARY - MAIN_LLM_RESPONSE | Total Operations: 2 | Cumulative Input Tokens: 1197 | Cumulative Output Tokens: 230 | Cumulative Total Tokens: 1427 | Starting main LLM response generation for self answer
  -> step_name: MAIN_LLM_RESPONSE, total_operations: 2, cumulative_input_tokens: 1197, cumulative_output_tokens: 230, cumulative_total_tokens: 1427, additional_info: Starting main LLM response generation for self answer
2025-06-12 15:15:22,604 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:22,604 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - STREAMING_START - Non-data analysis real-time streaming initiated
  -> stream_type: non_data_analysis_realtime, function: fake_llm_resonse, source_type: , prompt_length: 69, extracted_text_length: 0, chat_history_length: 0, response_type: Brief, visualization: False
2025-06-12 15:15:22,604 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:22,677 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Starting real-time insight generation
2025-06-12 15:15:22,677 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:25,727 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Received chunk #1
  -> chunk_length: 0, chunk_preview: EMPTY
2025-06-12 15:15:25,727 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:25,728 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Received chunk #2
  -> chunk_length: 3, chunk_preview: ```
2025-06-12 15:15:25,728 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:26,324 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Received chunk #3
  -> chunk_length: 4, chunk_preview: json
2025-06-12 15:15:26,324 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:26,406 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Received chunk #4
  -> chunk_length: 1, chunk_preview: 

2025-06-12 15:15:26,406 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:26,490 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Received chunk #5
  -> chunk_length: 2, chunk_preview: {

2025-06-12 15:15:26,491 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:46,542 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Summary streaming completed - boundary detected
  -> summary_length: 1346
2025-06-12 15:15:46,543 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,660 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: insight_generation | Subtype: realtime_streaming | Input Tokens: 1513 | Output Tokens: 571 | Total Tokens: 2084 | Running Total Operations: 3 | Success: True
  -> query_id: unified_query_20250612_094502_090342, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: insight_generation, operation_subtype: realtime_streaming, input_tokens: 1513, output_tokens: 571, total_tokens: 2084, success: True, timestamp: 2025-06-12T09:45:50.660460+00:00
2025-06-12 15:15:50,660 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,660 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Real-time streaming completed successfully
  -> total_content_length: 1844, summary_length: 1329, streaming_summary: True, summary_complete: True, content_preview: ```json
{
    "summary": "**Dummy Revenue Data and Descriptive Statistics**\n\nBelow is a generat..., content_ends_with:        "Can you provide a forecast for revenue in the next 6 months based on this data?"
    ]
}
```, total_chunks_received: 566
2025-06-12 15:15:50,661 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,661 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Removed markdown code block wrapper from JSON
  -> original_length: 1844, cleaned_length: 1832
2025-06-12 15:15:50,661 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,661 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - JSON detection check
  -> is_json_like: True, starts_with_brace: True, ends_with_brace: True, content_start: {
    "summary": "**, content_end:  this data?"
    ]
}
2025-06-12 15:15:50,661 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,661 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Transformed table data to match expected schema
  -> original_table_count: 0, transformed_table_count: 0, original_keys: [], transformed_keys: []
2025-06-12 15:15:50,661 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,661 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Skipped title addition - content was already streamed
  -> summary_length: 1277
2025-06-12 15:15:50,661 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,662 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Successfully parsed streamed content as structured JSON
  -> summary_length: 1277, tables_count: 0, questions_count: 4
2025-06-12 15:15:50,662 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,710 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - No file provided, use existing data from MongoDB for pdf processing
2025-06-12 15:15:50,711 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,711 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - file_context: 0
  -> extracted_text_length: 0
2025-06-12 15:15:50,711 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,711 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - valid_page_numbers incase of no file provided in request: []
  -> valid_page_numbers: []
2025-06-12 15:15:50,712 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,712 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Entered not llm call block in pdf processor
2025-06-12 15:15:50,712 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,712 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Using existing insight_data - skipping LLM call to prevent duplication
  -> insight_data_available: True, source_type: self_answer, insight_data_type: dict, has_summary: True
2025-06-12 15:15:50,712 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,712 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Putting images as '' in insights data as it will be empty in modes other than data analysis.
2025-06-12 15:15:50,712 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,713 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Added token usage to insight_data before saving - Input: 2710, Output: 801, Total: 3511
  -> input_tokens: 2710, output_tokens: 801, total_tokens: 3511, response_id: 684aa1ce19e0240e16c85847
2025-06-12 15:15:50,713 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,713 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Linked token events with response_id: 684aa1ce19e0240e16c85847
  -> response_id: 684aa1ce19e0240e16c85847
2025-06-12 15:15:50,713 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,713 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Saving project data in mongo db insights collection for pdf processor
2025-06-12 15:15:50,713 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,714 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Saving project data in mongo db
2025-06-12 15:15:50,714 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,761 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Project exists in mongo db
2025-06-12 15:15:50,762 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:50,762 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - File does not exist
2025-06-12 15:15:50,763 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:51,330 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Project data saved in mongo db when file does not exist
2025-06-12 15:15:51,331 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:51,381 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Saved project data in mongo db for pdf processor
2025-06-12 15:15:51,382 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:51,382 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Replacing the tables in insight data with actual table data for response sending
2025-06-12 15:15:51,382 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:51,425 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Token usage already included in saved data - Input: 2710, Output: 801
  -> input_tokens: 2710, output_tokens: 801
2025-06-12 15:15:51,425 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:51,425 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - USAGE_STEP_SUMMARY - PROCESSING_COMPLETE | Total Operations: 3 | Cumulative Input Tokens: 2710 | Cumulative Output Tokens: 801 | Cumulative Total Tokens: 3511 | Self answer processing completed successfully
  -> step_name: PROCESSING_COMPLETE, total_operations: 3, cumulative_input_tokens: 2710, cumulative_output_tokens: 801, cumulative_total_tokens: 3511, additional_info: Self answer processing completed successfully
2025-06-12 15:15:51,425 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:51,426 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Saving unified usage events
  -> event_count: 3, query_id: unified_query_20250612_094502_090342
2025-06-12 15:15:51,426 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:51,473 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Successfully saved unified usage events
  -> document_id: 684aa1cf19e0240e16c85848, total_operations: 3, total_tokens: 3511
2025-06-12 15:15:51,473 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:51,473 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Usage events linked to response
  -> response_id: 684aa1ce19e0240e16c85847
2025-06-12 15:15:51,473 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:51,474 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Successfully saved 3 usage events to MongoDB
  -> mongodb_id: 684aa1cf19e0240e16c85848, events_count: 3, response_id: 684aa1ce19e0240e16c85847, query_id: unified_query_20250612_094502_090342
2025-06-12 15:15:51,474 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
2025-06-12 15:15:51,474 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - Usage events saved to MongoDB for self answer path
2025-06-12 15:15:51,474 - INFO - project_user.ovais-1.final_test11m1sccnvnsdhdnmbmhjdddded - 
