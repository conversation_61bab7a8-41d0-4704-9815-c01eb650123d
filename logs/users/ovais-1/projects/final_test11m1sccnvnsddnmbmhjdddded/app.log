2025-06-12 15:14:21,405 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - File processing request started
  -> file_name: sample_gdpr_document.pdf, response_type: Brief, custom_prompt_name: None
2025-06-12 15:14:21,405 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:21,406 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Token validation is turned OFF. Skipping authentication checks.
2025-06-12 15:14:21,406 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:21,406 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250612_094421_406164
2025-06-12 15:14:21,406 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:21,406 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Unified usage tracking initialized
  -> usage_logger_initialized: True
2025-06-12 15:14:21,406 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:21,406 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - USAGE_STEP_SUMMARY - INITIALIZATION | Total Operations: 0 | Cumulative Input Tokens: 0 | Cumulative Output Tokens: 0 | Cumulative Total Tokens: 0 | Unified usage logger created
  -> step_name: INITIALIZATION, total_operations: 0, cumulative_input_tokens: 0, cumulative_output_tokens: 0, cumulative_total_tokens: 0, additional_info: Unified usage logger created
2025-06-12 15:14:21,406 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,448 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: content_moderation | Subtype: restricted_keywords_check | Input Tokens: 81 | Output Tokens: 1 | Total Tokens: 82 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250612_094421_406164, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: content_moderation, operation_subtype: restricted_keywords_check, input_tokens: 81, output_tokens: 1, total_tokens: 82, success: True, timestamp: 2025-06-12T09:44:26.447759+00:00
2025-06-12 15:14:26,448 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,449 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - USAGE_STEP_SUMMARY - RESTRICTED_KEYWORDS_CHECK | Total Operations: 1 | Cumulative Input Tokens: 81 | Cumulative Output Tokens: 1 | Cumulative Total Tokens: 82 | Completed content moderation check
  -> step_name: RESTRICTED_KEYWORDS_CHECK, total_operations: 1, cumulative_input_tokens: 81, cumulative_output_tokens: 1, cumulative_total_tokens: 82, additional_info: Completed content moderation check
2025-06-12 15:14:26,449 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,449 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Processing file...
2025-06-12 15:14:26,449 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,449 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Request parameters
  -> file: sample_gdpr_document.pdf, prompt: choose some dummy revenue data and create a descriptive statistics in tabular format, page_number: None, title: None, response_type: Brief, custom_prompt_name: None
2025-06-12 15:14:26,450 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,559 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Set default title to 'Untitled' for new project
  -> title: Untitled, exists: False
2025-06-12 15:14:26,560 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,562 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Created dirs: files/final_test11m1sccnvnsddnmbmhjdddded, files/final_test11m1sccnvnsddnmbmhjdddded/data_analysis, files/final_test11m1sccnvnsddnmbmhjdddded/non_data_analysis
  -> project_dir: files/final_test11m1sccnvnsddnmbmhjdddded, data_analysis_dir: files/final_test11m1sccnvnsddnmbmhjdddded/data_analysis, non_data_analysis_dir: files/final_test11m1sccnvnsddnmbmhjdddded/non_data_analysis
2025-06-12 15:14:26,562 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,563 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - File type determined as: pdf from extension: pdf and filename is : sample_gdpr_document
  -> file_type: pdf, filename: sample_gdpr_document.pdf, file_name_without_ext: sample_gdpr_document
2025-06-12 15:14:26,563 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,585 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Saved encrypted file to: files/final_test11m1sccnvnsddnmbmhjdddded/non_data_analysis/sample_gdpr_document.enc
  -> file_path: files/final_test11m1sccnvnsddnmbmhjdddded/non_data_analysis/sample_gdpr_document.enc, file_name: sample_gdpr_document.pdf
2025-06-12 15:14:26,585 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,585 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Extract text from file content
2025-06-12 15:14:26,586 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,691 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Processing page 1...
2025-06-12 15:14:26,691 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,691 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Inside process_pdf_page function
2025-06-12 15:14:26,691 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,715 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Extracting images from page, ocr is active
2025-06-12 15:14:26,715 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,718 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Extracted 0 images for page 1
2025-06-12 15:14:26,718 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,719 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - First 100 characters of page_content is : ['Sample GDPR Compliance Document\n1. Company Details\nCompany Name: DataSecure Solutions Ltd.\nAddr
2025-06-12 15:14:26,719 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,719 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Processing page after [<Page:1>, <Page:2>],1
2025-06-12 15:14:26,719 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,719 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - page content trimmed to first 100 characters is : Sample GDPR Compliance Document
1. Company Details
Company Name: DataSecure Solutions Ltd.
Address: 
2025-06-12 15:14:26,719 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,719 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Processing page 2...
2025-06-12 15:14:26,719 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,719 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Inside process_pdf_page function
2025-06-12 15:14:26,719 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,728 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Extracting images from page, ocr is active
2025-06-12 15:14:26,728 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,729 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Extracted 0 images for page 2
2025-06-12 15:14:26,729 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,729 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - First 100 characters of page_content is : ['Sample GDPR Compliance Document\n- Legal and regulatory authorities (as required)\n6. Security Mea
2025-06-12 15:14:26,729 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,729 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Processing page after [<Page:1>, <Page:2>],2
2025-06-12 15:14:26,729 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,729 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - page content trimmed to first 100 characters is : Sample GDPR Compliance Document
- Legal and regulatory authorities (as required)
6. Security Measure
2025-06-12 15:14:26,729 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,821 - WARNING - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Token-aware extraction failed in PDF processing, using fallback: 'int' object has no attribute 'split'
2025-06-12 15:14:26,821 - WARNING - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,821 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - output text is : Sample GDPR Compliance Document
1. Company Details
Company Name: DataSecure Solutions Ltd.
Address: 123 Privacy Lane, London, UK
Contact: <EMAIL>
2. Types of Personal Data Processed
- Full Name
- Email Address
- IP Address
- Location Data
- Contact Number
- Employment History
3. Purpose of Processing
Personal data is collected for the purposes of:
- Providing software services
- Customer communication
- Marketing (with consent)
- Employment administration
4. Data Retention Policy
Personal data is retained for a period not exceeding 5 years unless legal obligations require longer retention.
5. Data Sharing with Third Parties
Data may be shared with:
- Cloud hosting providers
- Email communication services
Sample GDPR Compliance Document
- Legal and regulatory authorities (as required)
6. Security Measures
We implement the following data protection measures:
- End-to-end encryption
- Role-based access control
- Regular security audits
- Data anonymization for analytics
7. Data Subject Rights
Data subjects have the right to:
- Access their personal data
- Rectify inaccurate data
- Request data erasure (right to be forgotten)
- Object to data processing
- Data portability
2025-06-12 15:14:26,821 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,822 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - output text, valid page numbers, first 100 characters of extracted text per page, valid page numbers : ('Sample GDPR Compliance Document\n1. Company Details\nCompany Name: DataSecure Solutions Ltd.\nAddress: 123 Privacy Lane, London, UK\nContact: <EMAIL>\n2. Types of Personal Data Processed\n- Full Name\n- Email Address\n- IP Address\n- Location Data\n- Contact Number\n- Employment History\n3. Purpose of Processing\nPersonal data is collected for the purposes of:\n- Providing software services\n- Customer communication\n- Marketing (with consent)\n- Employment administration\n4. Data Retention Policy\nPersonal data is retained for a period not exceeding 5 years unless legal obligations require longer retention.\n5. Data Sharing with Third Parties\nData may be shared with:\n- Cloud hosting providers\n- Email communication services\nSample GDPR Compliance Document\n- Legal and regulatory authorities (as required)\n6. Security Measures\nWe implement the following data protection measures:\n- End-to-end encryption\n- Role-based access control\n- Regular security audits\n- Data anonymization for analytics\n7. Data Subject Rights\nData subjects have the right to:\n- Access their personal data\n- Rectify inaccurate data\n- Request data erasure (right to be forgotten)\n- Object to data processing\n- Data portability', [1, 2], "['Sample GDPR Compliance Document\\n1. Company Details\\nCompany Name: DataSecure Solutions Ltd.\\nAddr", [1, 2])
2025-06-12 15:14:26,822 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,824 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Successfully cleaned up temp directory: temp_files/session_690bac08661b494d82d389658fabd325
  -> temp_dir: temp_files/session_690bac08661b494d82d389658fabd325
2025-06-12 15:14:26,825 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,825 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - -----------------------------------
2025-06-12 15:14:26,825 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,825 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Extracted text isSample GDPR Compliance Document
1. Company Details
Company Name: DataSecure Solutions Ltd.
Address: 123 Privacy Lane, London, UK
Contact: <EMAIL>
2. Types of Personal Data Processed
- Full Name
- Email Address
- IP Address
- Location Data
- Contact Number
- Employment History
3. Purpose of Processing
Personal data is collected for the purposes of:
- Providing software services
- Customer communication
- Marketing (with consent)
- Employment administration
4. Data Retention Policy
Personal data is retained for a period not exceeding 5 years unless legal obligations require longer retention.
5. Data Sharing with Third Parties
Data may be shared with:
- Cloud hosting providers
- Email communication services
Sample GDPR Compliance Document
- Legal and regulatory authorities (as required)
6. Security Measures
We implement the following data protection measures:
- End-to-end encryption
- Role-based access control
- Regular security audits
- Data anonymization for analytics
7. Data Subject Rights
Data subjects have the right to:
- Access their personal data
- Rectify inaccurate data
- Request data erasure (right to be forgotten)
- Object to data processing
- Data portability
  -> extracted_text_length: 1201
2025-06-12 15:14:26,825 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,825 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - -----------------------------------
2025-06-12 15:14:26,826 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:26,826 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Generating intent from 1200 characters of extracted text
  -> text_length: 1200, file_name: sample_gdpr_document.pdf
2025-06-12 15:14:26,826 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,874 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: intent_generation | Subtype: file_upload | Input Tokens: 581 | Output Tokens: 83 | Total Tokens: 664 | Running Total Operations: 2 | Success: True
  -> query_id: unified_query_20250612_094421_406164, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: intent_generation, operation_subtype: file_upload, input_tokens: 581, output_tokens: 83, total_tokens: 664, success: True, timestamp: 2025-06-12T09:44:35.874586+00:00
2025-06-12 15:14:35,875 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,875 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Intent generated successfully in file processing route
  -> file_name: sample_gdpr_document.pdf
2025-06-12 15:14:35,876 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,876 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - USAGE_STEP_SUMMARY - INTENT_GENERATION | Total Operations: 2 | Cumulative Input Tokens: 662 | Cumulative Output Tokens: 84 | Cumulative Total Tokens: 746 | Generated intent for document file: sample_gdpr_document.pdf
  -> step_name: INTENT_GENERATION, total_operations: 2, cumulative_input_tokens: 662, cumulative_output_tokens: 84, cumulative_total_tokens: 746, additional_info: Generated intent for document file: sample_gdpr_document.pdf
2025-06-12 15:14:35,876 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,879 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - intent----------------------------context --------------------- %s
  -> intent: {'context_intent': {'intent_description': 'The document outlines the GDPR compliance measures and...
2025-06-12 15:14:35,879 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,879 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Process pdf called with file_name: sample_gdpr_document.pdf
  -> file_name: sample_gdpr_document.pdf
2025-06-12 15:14:35,879 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,880 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - STREAMING_START - PDF processing streaming initiated (data analysis path)
  -> stream_type: pdf_processing_data_analysis, file_name: sample_gdpr_document.pdf, file_type: pdf, prompt_length: 84, response_type: Brief, visualization: False, custom_prompt_name: None
2025-06-12 15:14:35,880 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,924 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - File and project exists
2025-06-12 15:14:35,924 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,924 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Saving pickle file to: files/final_test11m1sccnvnsddnmbmhjdddded/non_data_analysis/sample_gdpr_document.pkl
  -> pkl_file_path: files/final_test11m1sccnvnsddnmbmhjdddded/non_data_analysis/sample_gdpr_document.pkl, file_name: sample_gdpr_document.pdf
2025-06-12 15:14:35,924 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,926 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - valid_page_numbers incase of file and project exists: [1, 2]
  -> valid_page_numbers: [1, 2], file_name: sample_gdpr_document.pdf
2025-06-12 15:14:35,926 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,974 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Using intent passed from file processing route (avoiding duplicate generation)
2025-06-12 15:14:35,974 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,974 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - intent----------------------------context --------------------- %s
  -> intent: {'context_intent': {'intent_description': 'The document outlines the GDPR compliance measures and...
2025-06-12 15:14:35,975 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,975 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Adding default prompt to the prompt in pdf processor (file and project exists)
2025-06-12 15:14:35,975 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,975 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Entered not llm call block in pdf processor
2025-06-12 15:14:35,975 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,975 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Using token consumption from token logger - Input: 662, Output: 84
  -> input_tokens: 662, output_tokens: 84
2025-06-12 15:14:35,975 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,975 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Putting images as '' in insights data as it will be empty in modes other than data analysis.
2025-06-12 15:14:35,975 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,975 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Added token usage to insight_data before saving - Input: 662, Output: 84, Total: 746
  -> input_tokens: 662, output_tokens: 84, total_tokens: 746, response_id: 684aa18319e0240e16c85844
2025-06-12 15:14:35,975 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,976 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Linked token events with response_id: 684aa18319e0240e16c85844
  -> response_id: 684aa18319e0240e16c85844
2025-06-12 15:14:35,976 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,976 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Saving project data in mongo db insights collection for pdf processor
2025-06-12 15:14:35,976 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:35,976 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Saving project data in mongo db
2025-06-12 15:14:35,976 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:36,021 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Project exists in mongo db
2025-06-12 15:14:36,021 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:36,021 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Saving pickle file to-- : files/final_test11m1sccnvnsddnmbmhjdddded/non_data_analysis/sample_gdpr_document.pdf
  -> pkl_file_path: files/final_test11m1sccnvnsddnmbmhjdddded/non_data_analysis/sample_gdpr_document.pdf, file_name: sample_gdpr_document.pdf
2025-06-12 15:14:36,021 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:36,022 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - File Exists
2025-06-12 15:14:36,022 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:36,022 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - File id for saving project data in mongo db: %s
  -> file_id: 6e389035-57c2-40df-90e3-352b573bd5f8
2025-06-12 15:14:36,022 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:36,022 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - ------------------------494
2025-06-12 15:14:36,022 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:36,022 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Project data saved in mongo db when file exists
2025-06-12 15:14:36,022 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:36,071 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Project data saved in mongo db when file does not exist
2025-06-12 15:14:36,072 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:36,122 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Saved project data in mongo db for pdf processor
2025-06-12 15:14:36,123 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:36,124 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Replacing the tables in insight data with actual table data for response sending
2025-06-12 15:14:36,124 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:36,177 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Token usage already included in saved data - Input: 662, Output: 84
  -> input_tokens: 662, output_tokens: 84
2025-06-12 15:14:36,178 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:36,178 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Event: %s
  -> event: {"type": "final_response", "content": {"summary": "Thank you for uploading your document. What would
2025-06-12 15:14:36,178 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:36,178 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Type of event content: <class 'dict'>
  -> content_type: <class 'dict'>
2025-06-12 15:14:36,178 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:36,178 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - USAGE_STEP_SUMMARY - PROCESSING_COMPLETE | Total Operations: 2 | Cumulative Input Tokens: 662 | Cumulative Output Tokens: 84 | Cumulative Total Tokens: 746 | PDF file processing completed successfully
  -> step_name: PROCESSING_COMPLETE, total_operations: 2, cumulative_input_tokens: 662, cumulative_output_tokens: 84, cumulative_total_tokens: 746, additional_info: PDF file processing completed successfully
2025-06-12 15:14:36,178 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:36,179 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Saving unified usage events
  -> event_count: 2, query_id: unified_query_20250612_094421_406164
2025-06-12 15:14:36,179 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:36,656 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Successfully saved unified usage events
  -> document_id: 684aa18419e0240e16c85845, total_operations: 2, total_tokens: 746
2025-06-12 15:14:36,657 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:36,657 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Usage events linked to response
  -> response_id: 684aa18319e0240e16c85844
2025-06-12 15:14:36,658 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:36,658 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Successfully saved 2 usage events to MongoDB
  -> mongodb_id: 684aa18419e0240e16c85845, events_count: 2, response_id: 684aa18319e0240e16c85844, query_id: unified_query_20250612_094421_406164
2025-06-12 15:14:36,658 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
2025-06-12 15:14:36,658 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - Usage events saved to MongoDB for PDF processing path
2025-06-12 15:14:36,658 - INFO - project_user.ovais-1.final_test11m1sccnvnsddnmbmhjdddded - 
