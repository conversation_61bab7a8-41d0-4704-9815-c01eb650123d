2025-06-13 10:51:10,968 - INFO - project_user.test_user_structured.test_project_structured - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250613_052110_964940
2025-06-13 10:51:10,968 - INFO - project_user.test_user_structured.test_project_structured - 
2025-06-13 10:51:13,314 - INFO - project_user.test_user_structured.test_project_structured - USAGE_CONSUMPTION - Category: llm | Provider: openai | Operation: test | Subtype: openai_structured_test | Input Tokens: 31 | Output Tokens: 107 | Total Tokens: 138 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250613_052110_964940, service_category: llm, service_provider: openai, service_tier: paid, operation_type: test, operation_subtype: openai_structured_test, input_tokens: 31, output_tokens: 107, total_tokens: 138, success: True, timestamp: 2025-06-13T05:21:13.314142+00:00
2025-06-13 10:51:13,314 - INFO - project_user.test_user_structured.test_project_structured - 
2025-06-13 10:51:14,319 - INFO - project_user.test_user_structured.test_project_structured - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250613_052114_319009
2025-06-13 10:51:14,319 - INFO - project_user.test_user_structured.test_project_structured - 
2025-06-13 10:51:16,142 - INFO - project_user.test_user_structured.test_project_structured - USAGE_CONSUMPTION - Category: vision | Provider: other | Operation: test | Subtype: gemini_structured_test | Input Tokens: 31 | Output Tokens: 0 | Total Tokens: 31 | Running Total Operations: 1 | Success: False
  -> query_id: unified_query_20250613_052114_319009, service_category: vision, service_provider: other, service_tier: unknown, operation_type: test, operation_subtype: gemini_structured_test, input_tokens: 31, output_tokens: 0, total_tokens: 31, success: False, timestamp: 2025-06-13T05:21:16.142916+00:00
2025-06-13 10:51:16,143 - INFO - project_user.test_user_structured.test_project_structured - 
2025-06-13 10:51:17,147 - INFO - project_user.test_user_structured.test_project_structured - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250613_052117_147188
2025-06-13 10:51:17,147 - INFO - project_user.test_user_structured.test_project_structured - 
2025-06-13 10:51:23,808 - INFO - project_user.test_user_structured.test_project_structured - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: test | Subtype: deepseek_structured_test | Input Tokens: 127 | Output Tokens: 51 | Total Tokens: 178 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250613_052117_147188, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: test, operation_subtype: deepseek_structured_test, input_tokens: 127, output_tokens: 51, total_tokens: 178, success: True, timestamp: 2025-06-13T05:21:23.808591+00:00
2025-06-13 10:51:23,808 - INFO - project_user.test_user_structured.test_project_structured - 
