2025-06-13 10:29:46,636 - INFO - project_user.test_user.test_project - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250613_045946_635725
2025-06-13 10:29:46,636 - INFO - project_user.test_user.test_project - 
2025-06-13 10:29:46,661 - INFO - project_user.test_user.test_project - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250613_045946_661385
2025-06-13 10:29:46,661 - INFO - project_user.test_user.test_project - 
2025-06-13 10:50:50,790 - INFO - project_user.test_user.test_project - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250613_052050_790007
2025-06-13 10:50:50,790 - INFO - project_user.test_user.test_project - 
2025-06-13 10:50:52,515 - INFO - project_user.test_user.test_project - USAGE_CONSUMPTION - Category: llm | Provider: openai | Operation: test | Subtype: openai_test | Input Tokens: 37 | Output Tokens: 10 | Total Tokens: 47 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250613_052050_790007, service_category: llm, service_provider: openai, service_tier: paid, operation_type: test, operation_subtype: openai_test, input_tokens: 37, output_tokens: 10, total_tokens: 47, success: True, timestamp: 2025-06-13T05:20:52.514963+00:00
2025-06-13 10:50:52,515 - INFO - project_user.test_user.test_project - 
2025-06-13 10:50:53,518 - INFO - project_user.test_user.test_project - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250613_052053_518119
2025-06-13 10:50:53,518 - INFO - project_user.test_user.test_project - 
2025-06-13 10:50:54,627 - INFO - project_user.test_user.test_project - USAGE_CONSUMPTION - Category: vision | Provider: openai | Operation: test | Subtype: openai_test | Input Tokens: 37 | Output Tokens: 11 | Total Tokens: 48 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250613_052053_518119, service_category: vision, service_provider: openai, service_tier: paid, operation_type: test, operation_subtype: openai_test, input_tokens: 37, output_tokens: 11, total_tokens: 48, success: True, timestamp: 2025-06-13T05:20:54.627095+00:00
2025-06-13 10:50:54,627 - INFO - project_user.test_user.test_project - 
2025-06-13 10:50:55,639 - INFO - project_user.test_user.test_project - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250613_052055_639184
2025-06-13 10:50:55,639 - INFO - project_user.test_user.test_project - 
2025-06-13 10:51:00,690 - INFO - project_user.test_user.test_project - USAGE_CONSUMPTION - Category: llm | Provider: deepseek | Operation: test | Subtype: deepseek_test | Input Tokens: 35 | Output Tokens: 12 | Total Tokens: 47 | Running Total Operations: 1 | Success: True
  -> query_id: unified_query_20250613_052055_639184, service_category: llm, service_provider: deepseek, service_tier: paid, operation_type: test, operation_subtype: deepseek_test, input_tokens: 35, output_tokens: 12, total_tokens: 47, success: True, timestamp: 2025-06-13T05:21:00.690429+00:00
2025-06-13 10:51:00,691 - INFO - project_user.test_user.test_project - 
2025-06-13 10:51:01,737 - INFO - project_user.test_user.test_project - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250613_052101_737774
2025-06-13 10:51:01,737 - INFO - project_user.test_user.test_project - 
2025-06-13 10:51:03,971 - INFO - project_user.test_user.test_project - USAGE_CONSUMPTION - Category: llm | Provider: other | Operation: test | Subtype: gemini_test | Input Tokens: 35 | Output Tokens: 0 | Total Tokens: 35 | Running Total Operations: 1 | Success: False
  -> query_id: unified_query_20250613_052101_737774, service_category: llm, service_provider: other, service_tier: unknown, operation_type: test, operation_subtype: gemini_test, input_tokens: 35, output_tokens: 0, total_tokens: 35, success: False, timestamp: 2025-06-13T05:21:03.970943+00:00
2025-06-13 10:51:03,971 - INFO - project_user.test_user.test_project - 
2025-06-13 10:51:04,978 - INFO - project_user.test_user.test_project - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250613_052104_978692
2025-06-13 10:51:04,979 - INFO - project_user.test_user.test_project - 
2025-06-13 10:51:07,042 - INFO - project_user.test_user.test_project - USAGE_CONSUMPTION - Category: vision | Provider: other | Operation: test | Subtype: gemini_test | Input Tokens: 35 | Output Tokens: 0 | Total Tokens: 35 | Running Total Operations: 1 | Success: False
  -> query_id: unified_query_20250613_052104_978692, service_category: vision, service_provider: other, service_tier: unknown, operation_type: test, operation_subtype: gemini_test, input_tokens: 35, output_tokens: 0, total_tokens: 35, success: False, timestamp: 2025-06-13T05:21:07.042603+00:00
2025-06-13 10:51:07,042 - INFO - project_user.test_user.test_project - 
2025-06-13 10:51:08,048 - INFO - project_user.test_user.test_project - UnifiedUsageLogger initialized
  -> query_id: unified_query_20250613_052108_047971
2025-06-13 10:51:08,048 - INFO - project_user.test_user.test_project - 
2025-06-13 10:51:09,958 - INFO - project_user.test_user.test_project - USAGE_CONSUMPTION - Category: llm | Provider: other | Operation: test | Subtype: gemini_test | Input Tokens: 35 | Output Tokens: 0 | Total Tokens: 35 | Running Total Operations: 1 | Success: False
  -> query_id: unified_query_20250613_052108_047971, service_category: llm, service_provider: other, service_tier: unknown, operation_type: test, operation_subtype: gemini_test, input_tokens: 35, output_tokens: 0, total_tokens: 35, success: False, timestamp: 2025-06-13T05:21:09.958225+00:00
2025-06-13 10:51:09,959 - INFO - project_user.test_user.test_project - 
