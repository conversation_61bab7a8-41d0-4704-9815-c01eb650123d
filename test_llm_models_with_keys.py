#!/usr/bin/env python3
"""
Test script to verify all LLM models work with configured API keys.
Tests OpenAI, DeepSeek, and Gemini models using the unified LLM service.
"""

import asyncio
import sys
import os
import time

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.llm_service import create_llm_service
from services.unified_usage_tracking import create_unified_usage_logger

async def test_model(model_name: str, provider_name: str):
    """Test a specific model with a simple chat completion"""
    
    print(f"\n🧪 Testing {provider_name} - {model_name}")
    print("-" * 60)
    
    try:
        # Create LLM service
        llm_service = await create_llm_service(model_name)
        
        # Set up usage tracking
        usage_logger = create_unified_usage_logger(
            project_id="test_project",
            user_id="test_user"
        )
        llm_service.set_unified_logger(usage_logger)
        
        print(f"✓ LLM Service created: {llm_service.provider} - {llm_service.model}")
        
        # Test message
        messages = [
            {"role": "user", "content": "Hello! Please respond with exactly: 'Hello from [MODEL_NAME]! I am working correctly.' Replace [MODEL_NAME] with your actual model name."}
        ]
        
        # Record start time
        start_time = time.time()
        
        # Make the request
        response = await llm_service.chat_completion(
            messages=messages,
            operation_type="test",
            operation_subtype=f"{provider_name.lower()}_test"
        )
        
        # Record end time
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"✓ Response received in {response_time:.2f}s")
        print(f"✓ Response: {response}")
        
        # Get usage statistics
        total_ops, total_input, total_output = usage_logger.get_total_consumption()
        print(f"✓ Usage: {total_input} input + {total_output} output = {total_input + total_output} total tokens")
        
        return True, response, total_input + total_output, response_time
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False, str(e), 0, 0

async def test_structured_output(model_name: str, provider_name: str):
    """Test structured output with a model"""
    
    print(f"\n📊 Testing Structured Output - {provider_name} - {model_name}")
    print("-" * 60)
    
    try:
        from pydantic import BaseModel
        from typing import List
        
        # Define a simple Pydantic model
        class TestResponse(BaseModel):
            message: str
            confidence: float
            keywords: List[str]
        
        # Create LLM service
        llm_service = await create_llm_service(model_name)
        
        # Set up usage tracking
        usage_logger = create_unified_usage_logger(
            project_id="test_project_structured",
            user_id="test_user_structured"
        )
        llm_service.set_unified_logger(usage_logger)
        
        messages = [
            {"role": "user", "content": "Analyze the phrase 'AI is transforming the world'. Provide a message, confidence score (0-1), and 3 keywords."}
        ]
        
        # Record start time
        start_time = time.time()
        
        # Make structured output request
        response = await llm_service.structured_output(
            messages=messages,
            pydantic_model=TestResponse,
            operation_type="test",
            operation_subtype=f"{provider_name.lower()}_structured_test"
        )
        
        # Record end time
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"✓ Structured response received in {response_time:.2f}s")
        print(f"✓ Message: {response.message}")
        print(f"✓ Confidence: {response.confidence}")
        print(f"✓ Keywords: {response.keywords}")
        
        # Get usage statistics
        total_ops, total_input, total_output = usage_logger.get_total_consumption()
        print(f"✓ Usage: {total_input} input + {total_output} output = {total_input + total_output} total tokens")
        
        return True, response, total_input + total_output, response_time
        
    except Exception as e:
        print(f"❌ Structured output error: {str(e)}")
        return False, str(e), 0, 0

async def main():
    """Main test function"""
    
    print("🚀 LLM Models Test with Configured API Keys")
    print("=" * 70)
    
    # Models to test
    test_models = [
        # OpenAI models
        ("gpt-4o-mini", "OpenAI"),
        ("gpt-4o", "OpenAI"),
        
        # DeepSeek models
        ("deepseek-chat", "DeepSeek"),
        
        # Gemini models (our new additions)
        ("gemini-2.0-flash-lite", "Gemini"),
        ("gemini-1.5-flash", "Gemini"),
        ("gemini-2.5-pro-preview-06-05", "Gemini"),
    ]
    
    results = []
    
    # Test basic chat completion for each model
    print("\n🔥 BASIC CHAT COMPLETION TESTS")
    print("=" * 70)
    
    for model_name, provider_name in test_models:
        success, response, tokens, time_taken = await test_model(model_name, provider_name)
        results.append({
            'model': model_name,
            'provider': provider_name,
            'test_type': 'chat',
            'success': success,
            'response': response,
            'tokens': tokens,
            'time': time_taken
        })
        
        # Small delay between requests
        await asyncio.sleep(1)
    
    # Test structured output for a few models
    print("\n\n🏗️ STRUCTURED OUTPUT TESTS")
    print("=" * 70)
    
    structured_test_models = [
        ("gpt-4o-mini", "OpenAI"),
        ("gemini-1.5-flash", "Gemini"),
        ("deepseek-chat", "DeepSeek"),
    ]
    
    for model_name, provider_name in structured_test_models:
        success, response, tokens, time_taken = await test_structured_output(model_name, provider_name)
        results.append({
            'model': model_name,
            'provider': provider_name,
            'test_type': 'structured',
            'success': success,
            'response': response,
            'tokens': tokens,
            'time': time_taken
        })
        
        # Small delay between requests
        await asyncio.sleep(1)
    
    # Print summary
    print("\n\n📋 TEST SUMMARY")
    print("=" * 70)
    
    successful_tests = 0
    total_tests = len(results)
    total_tokens = 0
    total_time = 0
    
    for result in results:
        status = "✅ PASS" if result['success'] else "❌ FAIL"
        print(f"{status} | {result['provider']:8} | {result['model']:25} | {result['test_type']:10} | {result['tokens']:4} tokens | {result['time']:.2f}s")
        
        if result['success']:
            successful_tests += 1
            total_tokens += result['tokens']
            total_time += result['time']
    
    print("-" * 70)
    print(f"Success Rate: {successful_tests}/{total_tests} ({successful_tests/total_tests*100:.1f}%)")
    print(f"Total Tokens Used: {total_tokens}")
    print(f"Total Time: {total_time:.2f}s")
    
    # Provider-specific summary
    print("\n📊 PROVIDER SUMMARY")
    print("-" * 40)
    
    providers = {}
    for result in results:
        provider = result['provider']
        if provider not in providers:
            providers[provider] = {'success': 0, 'total': 0, 'tokens': 0}
        
        providers[provider]['total'] += 1
        if result['success']:
            providers[provider]['success'] += 1
            providers[provider]['tokens'] += result['tokens']
    
    for provider, stats in providers.items():
        success_rate = stats['success'] / stats['total'] * 100
        print(f"{provider:8}: {stats['success']}/{stats['total']} ({success_rate:.1f}%) - {stats['tokens']} tokens")
    
    print("\n🎉 Testing completed!")
    
    if successful_tests == total_tests:
        print("🌟 All tests passed! Your LLM service is working perfectly with all providers.")
    elif successful_tests > 0:
        print("⚠️  Some tests passed. Check the failed ones for API key or configuration issues.")
    else:
        print("💥 All tests failed. Please check your API key configuration.")
    
    return successful_tests == total_tests

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
