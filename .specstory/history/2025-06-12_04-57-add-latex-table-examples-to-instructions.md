<!-- Generated by SpecStory -->

# Add LaTeX table examples to instructions (2025-06-12 10:27:25)

_**User**_

just like in insight generation prompt there is example latex table format and instructions can we add those in assitant instructions as well for both brief and detailed instruction

---

_**Assistant**_



---

I can see you want to add the LaTeX formatting instructions from the insight generation prompt to assistant instructions. Let me first search for other prompt functions in the codebase that might need these LaTeX instructions.

---

<details>
            <summary>Searched codebase "assistant instructions prompt brief detailed" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `config/messages.py` | L21-37 |
| `services/prompts.py` | L1-12 |
| `config/messages.py` | L37-44 |
| `services/data_analysis.py` | L79-98 |
| `services/prompts.py` | L12-36 |
| `services/openai_utils.py` | L49-94 |
| `services/data_analysis.py` | L98-129 |
| `services/data_analysis.py` | L332-354 |
| `services/prompts.py` | L73-109 |
| `services/prompts.py` | L109-118 |
| `services/prompts.py` | L118-141 |
| `services/prompts.py` | L36-73 |
| `services/prompts.py` | L141-152 |
| `utils/templated_projects_utils.py` | L766-808 |
| `todo.txt` | L1-3 |
| `services/prompts.py` | L152-201 |
| `services/llm_utils.py` | L369-435 |
| `services/data_analysis.py` | L354-367 |
| `services/data_analysis.py` | L168-181 |
| `services/prompts.py` | L446-462 |
| `services/llm_service.py` | L1452-1477 |
| `services/openai_utils.py` | L1-38 |
| `services/data_analyst_agent.py` | L118-142 |
| `services/data_analysis.py` | L301-332 |
| `services/data_analysis.py` | L129-168 |

</details>

---



---

I can see the assistant instructions in the `config/messages.py` file. Let me add the LaTeX formatting instructions from the insight generation prompt to both the brief and detailed assistant instructions.

---

Read file: config/messages.py

---



---

Now I'll add the LaTeX formatting instructions to both assistant instruction strings in the config file:

---

```diff
      "IMPORTANT: Always start your response with a descriptive title wrapped in **title** format, followed by two newlines, then your analysis. "
      "Example format: **Title**\\n\\nYour analysis content here... "
-     "When adding descriptions to your charts, ensure they are in a readable font size (at least 12-14pt) and allocate sufficient space in the chart layout for the description text to be clearly visible."
+     "When adding descriptions to your charts, ensure they are in a readable font size (at least 12-14pt) and allocate sufficient space in the chart layout for the description text to be clearly visible. "
+     
+     "**IMPORTANT - LaTeX Table Structure:** "
+     "When including tables in your response, always use LaTeX array format with proper borders and alignment but wrap it in $$ so that it acts as a flag that the table is a latex table: "
+     "- Use \\begin{array}{|c|c|c|...} to start the table with column specifications "
+     "- Use \\hline for horizontal lines between rows "
+     "- Use & to separate columns and \\\\\\\\ to end rows "
+     "- Use \\text{} for text content in cells "
+     "- End with \\end{array} "
+     "* Ensure all table rows have the same number of columns "
+     "* Use consistent alignment and proper mathematical notation when needed "
+     "* Example LaTeX table format: "
+     "$$\\begin{array}{|c|c|c|c|c|c|} "
+     "\\hline "
+     "\\text{Source} & \\text{SS} & \\text{DF} & \\text{MS} & \\text{F} & \\text{P} \\\\\\\\ "
+     "\\hline "
+     "\\text{Between Groups} & 9.83 & 4 & 9.83 & 9.83 & 7.65 \\times 10^{-8} \\\\\\\\ "
+     "\\hline "
+     "\\text{Within Groups} & - & 278 & - & - & - \\\\\\\\ "
+     "\\hline "
+     "\\text{Total} & - & 282 & - & - & - \\\\\\\\ "
+     "\\hline "
+     "\\end{array}$$ "
+     
+     "**IMPORTANT - LaTeX Mathematical Expressions:** "
+     "When including mathematical formulas, calculations, or equations, use proper LaTeX syntax: "
+     "* Enclose inline math with single dollar signs: $formula$ "
+     "* Enclose display math with double dollar signs: $$formula$$ "
+     "* Use correct LaTeX commands for mathematical operations: "
+     "  - Fractions: \\frac{numerator}{denominator} "
+     "  - Superscripts: x^{power} "
+     "  - Subscripts: x_{index} "
+     "  - Greek letters: \\alpha, \\beta, \\gamma, etc. "
+     "  - Mathematical operators: \\sum, \\prod, \\int, \\sqrt{}, etc. "
+     "* Example: Revenue Growth Rate = $$\\frac{Revenue_{current} - Revenue_{previous}}{Revenue_{previous}} \\times 100\\%$$"
  )
  
```

```diff
      "IMPORTANT: Always start your response with a descriptive title wrapped in **title** format, followed by two newlines, then your analysis. "
      "Example format: **Title**\\n\\nYour detailed analysis content here... "
-     "When adding descriptions to your charts, ensure they are in a readable font size (at least 12-14pt) and allocate sufficient space in the chart layout for the description text to be clearly visible."
+     "When adding descriptions to your charts, ensure they are in a readable font size (at least 12-14pt) and allocate sufficient space in the chart layout for the description text to be clearly visible. "
+     
+     "**IMPORTANT - LaTeX Table Structure:** "
+     "When including tables in your response, always use LaTeX array format with proper borders and alignment but wrap it in $$ so that it acts as a flag that the table is a latex table: "
+     "- Use \\begin{array}{|c|c|c|...} to start the table with column specifications "
+     "- Use \\hline for horizontal lines between rows "
+     "- Use & to separate columns and \\\\\\\\ to end rows "
+     "- Use \\text{} for text content in cells "
+     "- End with \\end{array} "
+     "* Ensure all table rows have the same number of columns "
+     "* Use consistent alignment and proper mathematical notation when needed "
+     "* Example LaTeX table format: "
+     "$$\\begin{array}{|c|c|c|c|c|c|} "
+     "\\hline "
+     "\\text{Source} & \\text{SS} & \\text{DF} & \\text{MS} & \\text{F} & \\text{P} \\\\\\\\ "
+     "\\hline "
+     "\\text{Between Groups} & 9.83 & 4 & 9.83 & 9.83 & 7.65 \\times 10^{-8} \\\\\\\\ "
+     "\\hline "
+     "\\text{Within Groups} & - & 278 & - & - & - \\\\\\\\ "
+     "\\hline "
+     "\\text{Total} & - & 282 & - & - & - \\\\\\\\ "
+     "\\hline "
+     "\\end{array}$$ "
+     
+     "**IMPORTANT - LaTeX Mathematical Expressions:** "
+     "When including mathematical formulas, calculations, or equations, use proper LaTeX syntax: "
+     "* Enclose inline math with single dollar signs: $formula$ "
+     "* Enclose display math with double dollar signs: $$formula$$ "
+     "* Use correct LaTeX commands for mathematical operations: "
+     "  - Fractions: \\frac{numerator}{denominator} "
+     "  - Superscripts: x^{power} "
+     "  - Subscripts: x_{index} "
+     "  - Greek letters: \\alpha, \\beta, \\gamma, etc. "
+     "  - Mathematical operators: \\sum, \\prod, \\int, \\sqrt{}, etc. "
+     "* Example: Revenue Growth Rate = $$\\frac{Revenue_{current} - Revenue_{previous}}{Revenue_{previous}} \\times 100\\%$$"
  )
  
```

---



---

Perfect! I've successfully added the LaTeX formatting instructions from the insight generation prompt to both the brief and detailed assistant instructions in `config/messages.py`. 

The changes include:

## Added to both `assistant_instructions_for_brief_summaries` and `assistant_instructions_for_detailed_summaries`:

### **LaTeX Table Structure:**
- Instructions on using LaTeX array format with proper borders and alignment
- Guidelines for column specifications, horizontal lines, text wrapping
- Example table format showing how to structure tables properly

### **LaTeX Mathematical Expressions:**
- Instructions for inline math with single dollar signs: `$formula$`
- Instructions for display math with double dollar signs: `$$formula$$`
- Specific LaTeX commands for:
  - Fractions: `\frac{numerator}{denominator}`
  - Superscripts: `x^{power}`
  - Subscripts: `x_{index}`
  - Greek letters: `\alpha, \beta, \gamma`, etc.
  - Mathematical operators: `\sum, \prod, \int, \sqrt{}`, etc.
- Example showing proper formatting for mathematical expressions

Now both assistant instruction sets have the same comprehensive LaTeX formatting guidelines that ensure consistent mathematical and tabular content presentation across brief and detailed responses.

---

_**User**_

this is the expected latex :-

$$\begin{array}{|c|c|c|c|c|c|}
\hline
\text{Source} & \text{SS} & \text{DF} & \text{MS} & \text{F} & \text{P} \\
\hline
\text{Between Groups} & 9.83 & 4 & 9.83 & 9.83 & 7.65 \times 10^{-8} \\
\hline
\text{Within Groups} & - & 278 & - & - & - \\
\hline
\text{Total} & - & 282 & - & - & - \\
\hline
\end{array}$$

do you thhink the below instructions related to latex table are wrong, i mean some:-



---

_**Assistant**_



---

You're absolutely right! Looking at the expected LaTeX format versus what I wrote in the instructions, there are indeed errors. The main issue is with the row endings.

**The Problem:**
In my instructions, I wrote:
```
"- Use & to separate columns and \\\\\\\\ to end rows "
```

And in the example:
```
"\\text{Source} & \\text{SS} & \\text{DF} & \\text{MS} & \\text{F} & \\text{P} \\\\\\\\ "
```

**But the expected format shows:**
```
\text{Source} & \text{SS} & \text{DF} & \text{MS} & \text{F} & \text{P} \\
```

The issue is that I used `\\\\\\\\` (which would render as `\\\\`) when it should be just `\\\\` (which renders as `\\`).

Let me fix this in the config file:

---



---



---

Read file: config/messages.py

---



---

Now I can see the issues. I need to fix the LaTeX row endings. Let me correct both the instructions and examples:

---



---



---



---



---



---



---



---



---



---



---



---



---

Let me use a different approach and replace the entire assistant instructions section with the corrected LaTeX formatting:

---

```diff
      "- Use \\begin{array}{|c|c|c|...} to start the table with column specifications "
      "- Use \\hline for horizontal lines between rows "
-     "- Use & to separate columns and \\\\\\\\ to end rows "
+     "- Use & to separate columns and \\\\ to end rows "
      "- Use \\text{} for text content in cells "
      "- End with \\end{array} "
```

```diff
      "$$\\begin{array}{|c|c|c|c|c|c|} "
      "\\hline "
-     "\\text{Source} & \\text{SS} & \\text{DF} & \\text{MS} & \\text{F} & \\text{P} \\\\\\\\ "
+     "\\text{Source} & \\text{SS} & \\text{DF} & \\text{MS} & \\text{F} & \\text{P} \\\\ "
      "\\hline "
-     "\\text{Between Groups} & 9.83 & 4 & 9.83 & 9.83 & 7.65 \\times 10^{-8} \\\\\\\\ "
+     "\\text{Between Groups} & 9.83 & 4 & 9.83 & 9.83 & 7.65 \\times 10^{-8} \\\\ "
      "\\hline "
-     "\\text{Within Groups} & - & 278 & - & - & - \\\\\\\\ "
+     "\\text{Within Groups} & - & 278 & - & - & - \\\\ "
      "\\hline "
-     "\\text{Total} & - & 282 & - & - & - \\\\\\\\ "
+     "\\text{Total} & - & 282 & - & - & - \\\\ "
      "\\hline "
      "\\end{array}$$ "
```

```diff
      "- Use \\begin{array}{|c|c|c|...} to start the table with column specifications "
      "- Use \\hline for horizontal lines between rows "
-     "- Use & to separate columns and \\\\\\\\ to end rows "
+     "- Use & to separate columns and \\\\ to end rows "
      "- Use \\text{} for text content in cells "
      "- End with \\end{array} "
```

```diff
      "$$\\begin{array}{|c|c|c|c|c|c|} "
      "\\hline "
-     "\\text{Source} & \\text{SS} & \\text{DF} & \\text{MS} & \\text{F} & \\text{P} \\\\\\\\ "
+     "\\text{Source} & \\text{SS} & \\text{DF} & \\text{MS} & \\text{F} & \\text{P} \\\\ "
      "\\hline "
-     "\\text{Between Groups} & 9.83 & 4 & 9.83 & 9.83 & 7.65 \\times 10^{-8} \\\\\\\\ "
+     "\\text{Between Groups} & 9.83 & 4 & 9.83 & 9.83 & 7.65 \\times 10^{-8} \\\\ "
      "\\hline "
-     "\\text{Within Groups} & - & 278 & - & - & - \\\\\\\\ "
+     "\\text{Within Groups} & - & 278 & - & - & - \\\\ "
      "\\hline "
-     "\\text{Total} & - & 282 & - & - & - \\\\\\\\ "
+     "\\text{Total} & - & 282 & - & - & - \\\\ "
      "\\hline "
      "\\end{array}$$ "
```

---



---

Perfect! I've fixed the LaTeX formatting issues in both assistant instruction sets. You were absolutely right to point this out.

## What was wrong:
- **Row endings**: I had `\\\\\\\\` (which renders as `\\\\`) instead of `\\\\` (which renders as `\\`)
- **Instructions**: The instruction text incorrectly stated to use `\\\\\\\\` to end rows
- **Examples**: All the example table rows had the wrong row endings

## What I fixed:
✅ **Instruction text**: Changed from `"Use & to separate columns and \\\\\\\\ to end rows"` to `"Use & to separate columns and \\\\ to end rows"`

✅ **Example table**: Fixed all row endings from `\\\\\\\\` to `\\\\` in the example LaTeX table

Now the assistant instructions will generate the correct LaTeX format that matches your expected output:

```latex
$$\begin{array}{|c|c|c|c|c|c|}
\hline
\text{Source} & \text{SS} & \text{DF} & \text{MS} & \text{F} & \text{P} \\
\hline
\text{Between Groups} & 9.83 & 4 & 9.83 & 9.83 & 7.65 \times 10^{-8} \\
\hline
\text{Within Groups} & - & 278 & - & - & - \\
\hline
\text{Total} & - & 282 & - & - & - \\
\hline
\end{array}$$
```

The issue was with Python string escaping - when you need `\\` in the final output, you need `\\\\` in the Python string, but I mistakenly used `\\\\\\\\` which would produce `\\\\` in the output instead of the correct `\\`.

---

_**User**_

i think we have to fix this in insight prompt as well

---

_**Assistant**_



---

You're absolutely right! I need to fix the same LaTeX formatting issue in the insight prompt in `services/prompts.py`. Let me check and fix that as well.

---

Read file: services/prompts.py

---



---

