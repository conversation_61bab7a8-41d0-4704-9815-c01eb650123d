<!-- Generated by SpecStory -->

# Fix custom prompts access error (2025-06-12 14:04:38)

_**User**_



---

_**Assistant**_

I notice an error in the logs related to custom prompts. The system is trying to access `custom_prompts` from `services.utils` but it doesn't exist there. Let me check the code and fix the issue.

---

Read file: services/custom_prompts.py

---

Let me check the logs to understand what's causing the error. The error message states: `module 'services.utils' has no attribute 'custom_prompts'`. This suggests that somewhere in the code, there's an attempt to access a `custom_prompts` attribute from the `services.utils` module, but this attribute doesn't exist.

Let me check the `services/utils.py` file:

---

<details>
            <summary>Searched codebase "utils.py" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

Let me check the utils.py file:

---

Read file: services/utils.py

---

Now let's check if there are any references to custom_prompts in the codebase:

---

<details>
            <summary>Grep search for "services.utils.custom_prompts" • **0** files</summary>
        
No results found
</details>

---

Let me try a different search:

---

<details>
            <summary>Grep search for "utils.custom_prompts" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/pdf_processor.py` | L557 | `custom_prompt_content = utils.custom_prompts.get_prompt_by_name(custom_prompt_name)` |

</details>

---

Aha! Found the issue. In `pdf_processor.py`, line 557, there's a reference to `utils.custom_prompts.get_prompt_by_name(custom_prompt_name)`, but the `custom_prompts` module is not imported in `utils.py`.

Let's check the pdf_processor.py file:

---

Read file: services/pdf_processor.py

---

Now I understand the issue. In `pdf_processor.py`, line 557, there's a call to `utils.custom_prompts.get_prompt_by_name(custom_prompt_name)`, but `utils.py` doesn't import the `custom_prompts` module.

Let's fix this by editing the `utils.py` file to import the `custom_prompts` module:

---

```diff
  from services.utils_main import *
  from services.logging_utils import LoggingHelper
+ from services import custom_prompts  # Import custom_prompts module
  
  # This file is kept for backward compatibility with existing code.
```

---

Great! Now the `custom_prompts` module is imported in `utils.py`, so when `pdf_processor.py` calls `utils.custom_prompts.get_prompt_by_name(custom_prompt_name)`, it should work correctly.

Let's test the system to see if the error is fixed:

---

```bash
cd /Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend && python -c "from services import utils; print('Custom prompt for Featured_1:', utils.custom_prompts.get_prompt_by_name('Featured_1'))"
```

```
MongoDB async connection initialized!
⚠️  WARNING: Using hardcoded API keys from config/api.py
Secret key for descryptin is : b'\xefo\xae\x03\xce\x81\xa6\xaf\xffq\xf3#\xba*\xc
aY$\x9dQ\xab\xba\xa8\x86|\xc
1\xab\xc1\x00D\x97l\x9c'
                            /Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/Neuqu
ipAIBackend/services/project_user_logger.py:70: RuntimeW
arning: coroutine 'ProjectUserLogger._background_cleanup' was never awaited
                              pass
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
2025-06-12 14:09:08,015 - WARNING - [U:system|P:general] - langchain_deepseek no
t available (No module named
 'langchain_deepseek'), falling back to ChatOpenAI for DeepSeek
                            2025-06-12 14:09:08,016 - WARNING - [U:system|P:gene
ral] - 
Custom prompt for Featured_1: Summarize the document

```

---

Perfect! The test confirms that the fix is working. Now `utils.custom_prompts.get_prompt_by_name('Featured_1')` returns "Summarize the document" as expected.

To summarize what I did:

1. Identified the error: The system was trying to access `custom_prompts` from `services.utils`, but the module wasn't imported in `utils.py`.

2. Found the cause: In `pdf_processor.py`, there's a call to `utils.custom_prompts.get_prompt_by_name(custom_prompt_name)` which requires the `custom_prompts` module to be accessible through `utils`.

3. Fixed the issue: Added `from services import custom_prompts` to `utils.py` so that `custom_prompts` can be accessed through the `utils` module.

4. Verified the fix: Tested that `utils.custom_prompts.get_prompt_by_name('Featured_1')` now returns the expected value.

The system should now be able to process files with custom prompts correctly. The error "module 'services.utils' has no attribute 'custom_prompts'" should no longer occur.

---

