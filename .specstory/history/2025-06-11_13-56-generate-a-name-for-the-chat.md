<!-- Generated by SpecStory -->

# Generate a name for the chat (2025-06-11 19:26:10)

_**User**_



---

_**Assistant**_



---

I can see from the log that there's an error in your compliance report generation system. The system is failing to read a pickle file, which is causing a 500 Internal Server Error. Let me explore your codebase to understand the issue better.

---

<details>
            <summary>Listed current directory • **29** results</summary>
        
| Name |
|-------|
| 📁 `.git` |
| 📁 `files` |
| 📁 `temp_files` |
| 📄 `SOURCE_TYPE_FIX_SUMMARY.md` |
| 📄 `TOKEN_CONSISTENCY_ANALYSIS.md` |
| 📁 `logs` |
| 📁 `services` |
| 📄 `.DS_Store` |
| 📁 `.specstory` |
| 📄 `.cursorindexingignore` |
| 📁 `docs` |
| 📁 `tests` |
| 📁 `__pycache__` |
| 📁 `NeuquipEnv` |
| 📁 `config` |
| 📁 `models` |
| 📁 `routes` |
| 📁 `exceptions` |
| 📄 `ERROR_HANDLING_GUIDE.md` |
| 📄 `main.py` |
| 📄 `requirements.txt` |
| 📄 `todo.txt` |
| 📄 `print_api_keys.py` |
| 📄 `config.py` |
| 📄 `integrate_project_user_logging.py` |
| 📁 `utils` |
| 📄 `README.md` |
| 📄 `.gitignore` |
| 📄 `structure.md` |

</details>

---



---

<details>
            <summary>Listed directory routes • **10** results</summary>
        
| Name |
|-------|
| 📁 `__pycache__` |
| 📄 `file_processing_routes.py` |
| 📄 `compliance_routes.py` |
| 📄 `main_router.py` |
| 📄 `logging_routes.py` |
| 📄 `file_download_routes.py` |
| 📄 `templated_projects_routes.py` |
| 📄 `file_details_routes.py` |
| 📄 `project_management_routes.py` |
| 📄 `file_routes.py` |

</details>

---



---

<details>
            <summary>Searched codebase "Error reading pickle file {pkl_file_path}: {e}" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `services/file_utils.py` | L184-204 |
| `services/file_utils.py` | L52-92 |
| `services/file_utils.py` | L1-52 |
| `routes/file_processing_routes.py` | L721-738 |
| `routes/file_processing_routes.py` | L702-721 |
| `routes/compliance_routes.py` | L538-566 |
| `utils/templated_projects_utils.py` | L252-275 |
| `services/pdf_processor.py` | L56-91 |
| `services/pdf_processor.py` | L545-568 |
| `services/file_utils.py` | L112-125 |
| `routes/file_processing_routes.py` | L392-418 |
| `utils/templated_projects_utils.py` | L198-231 |
| `logs/users/ovais-1/projects/tests10jun1.011211/app.log` | L75-95 |
| `logs/users/ovais-1/projects/tests10jun1.05112111mx/app.log` | L14959-14970 |
| `logs/users/ovais-1/projects/tests10jun1.011211/app.log` | L471-492 |
| `services/file_utils.py` | L157-184 |
| `logs/users/ovais-1/projects/tests10jun1.011211/app.log` | L277-298 |
| `logs/users/ovais-1/projects/tests10jun1.05112111mxv/app.log` | L15000-15012 |
| `logs/users/ovais-1/projects/nntests10jun1.05112111mxvtnvnqdwd m/app.log` | L763-780 |
| `routes/compliance_routes.py` | L514-538 |
| `logs/users/ovais-1/projects/nntests10jun1.05112111mxvtnvnqdwd m/app.log` | L942-955 |
| `logs/users/ovais-1/projects/nntests10jun1.05112111mxvtnvnqdwd m/app.log` | L1284-1297 |
| `logs/users/ovais-1/projects/nntests10jun1.05112111mxvtnvnqdwd m/app.log` | L286-299 |
| `logs/users/ovais-1/projects/tests10jun1.05112111mxv/app.log` | L15029-15048 |
| `logs/users/ovais-1/projects/tests10jun1.05112111mx/app.log` | L14987-15006 |

</details>

---

<details>
            <summary>Grep search for "generate-compliance-report" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/logs/users/system/projects/general/app.log` | L38056 | `2025-06-11 19:25:12,250 - INFO - project_user.system.general.fallback - Request started: POST /generate-compliance-report/ [ID: 5e8a4994-2783-4cc4-9054-0f556ccd3154]` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/logs/users/system/projects/general/app.log` | L38058 | `2025-06-11 19:25:12,258 - INFO - project_user.system.general.fallback - Request completed: POST /generate-compliance-report/ - 404 [0.009s] [ID: 5e8a4994-2783-4cc4-9054-0f556ccd3154]` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/logs/users/system/projects/general/app.log` | L38060 | `2025-06-11 19:25:25,835 - INFO - project_user.system.general.fallback - Request started: POST /generate-compliance-report/ [ID: 2a23ffea-e276-47f5-925f-c6a91bc720ab]` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/logs/users/system/projects/general/app.log` | L38064 | `2025-06-11 19:25:26,799 - INFO - project_user.system.general.fallback - Request completed: POST /generate-compliance-report/ - 500 [0.964s] [ID: 2a23ffea-e276-47f5-925f-c6a91bc720ab]` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/config/urls.py` | L19 | `compliance_generation_url = "generate-compliance-report"` |

</details>

---



---

Read file: routes/compliance_routes.py

---



---

Read file: services/file_utils.py

---



---

<details>
            <summary>Searched codebase "logging_utils.py" • **1** results</summary>
        
| File |
|------|
| `undefined` |

</details>

---



---

Read file: services/logging_utils.py

---



---

Could not resolve URI: Error: Unable to resolve nonexistent file '/Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/files/final_test/non_data_analysis'

---



---

<details>
            <summary>Grep search for "LoggingHelper\.error.*\{.*\}" • **20** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/logging_routes.py` | L308 | `LoggingHelper.error(f"ERROR: {message}", user_id, project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/unified_usage_tracking.py` | L195 | `LoggingHelper.error(f"Error logging usage event: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/unified_usage_tracking.py` | L432 | `LoggingHelper.error(f"Error saving usage events to MongoDB: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/project_management_routes.py` | L52 | `LoggingHelper.error(f"Error updating title: {str(e)}", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/project_management_routes.py` | L91 | `LoggingHelper.error(f"Error setting model: {str(e)}", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/project_management_routes.py` | L130 | `LoggingHelper.error(f"Error getting model: {str(e)}", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_details_routes.py` | L49 | `LoggingHelper.error(f"Project with _id {project_id} not found", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_details_routes.py` | L76 | `LoggingHelper.error(f"Error getting file details: {str(e)}", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/search_tracking.py` | L130 | `LoggingHelper.error(f"Error logging search event: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/search_tracking.py` | L229 | `LoggingHelper.error(f"Error saving search events to MongoDB: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/templated_projects_routes.py` | L81 | `LoggingHelper.error(f"Failed to create templated project: {result['error']}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/templated_projects_routes.py` | L87 | `LoggingHelper.error(f"Validation error: {str(e)}", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/templated_projects_routes.py` | L91 | `LoggingHelper.error(f"Unexpected error creating templated project: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/templated_projects_routes.py` | L145 | `LoggingHelper.error(f"Error retrieving templated project: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/templated_projects_routes.py` | L176 | `LoggingHelper.error("Invalid token", extra_data={"limit": limit, "skip": skip})` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/templated_projects_routes.py` | L212 | `LoggingHelper.error(f"Error listing templated projects: {str(e)}", user_id=user_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/templated_projects_routes.py` | L268 | `LoggingHelper.error(f"Error deleting templated project: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/templated_projects_routes.py` | L336 | `LoggingHelper.error(f"Failed to import templated projects: {result['error']}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/templated_projects_routes.py` | L342 | `LoggingHelper.error(f"Unexpected error importing templated projects: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/templated_projects_routes.py` | L405 | `LoggingHelper.error(f"Error debugging templated projects: {str(e)}", user_id=user_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/search_utils.py` | L72 | `LoggingHelper.error(f"Error in Serper search: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/search_utils.py` | L141 | `LoggingHelper.error(f"Error in DuckDuckGo search: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L88 | `LoggingHelper.error(f"Project with _id {project_id} not found", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L108 | `LoggingHelper.error(f"Project {actual_project_id} not found", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L114 | `LoggingHelper.error(f"User {user_id} not authorized for project {actual_project_id}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L131 | `LoggingHelper.error(f"File ID {file_id} not found in project {actual_project_id}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L185 | `LoggingHelper.error(f"Report for {file_name} is missing required fields: {missing_fields}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L195 | `LoggingHelper.error(f"Invalid JSON in report file for {file_name}", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L200 | `LoggingHelper.error(f"Error reading report file for {file_name}: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L674 | `LoggingHelper.error(f"Error processing file {file_name}: {http_e.detail}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L702 | `LoggingHelper.error(f"Failed to save usage events to MongoDB for compliance report generation: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L750 | `LoggingHelper.error(f"Project with _id {project_id} not found", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L761 | `LoggingHelper.error(f"Project {actual_project_id} not found", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L767 | `LoggingHelper.error(f"User {user_id} not authorized for project {actual_project_id}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L802 | `LoggingHelper.error(f"Error retrieving compliance reports: {str(e)}", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L837 | `LoggingHelper.error(f"Project with _id {project_id} not found", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L845 | `LoggingHelper.error(f"Project {actual_project_id} not found", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L850 | `LoggingHelper.error(f"User {user_id} not authorized for project {actual_project_id}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L860 | `LoggingHelper.error(f"No compliance reports found for project {actual_project_id}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L972 | `LoggingHelper.error(f"Error reading report file {report_path}: {e}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/compliance_routes.py` | L1000 | `LoggingHelper.error(f"Error generating comprehensive compliance markdown: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/file_utils.py` | L39 | `LoggingHelper.error("Directory '{directory}' does not exist. This should have been created earlier.", logger_name="file_utils")` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/file_utils.py` | L60 | `LoggingHelper.error("Pickle file not found: {filePath}", logger_name="file_utils")` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/file_utils.py` | L90 | `LoggingHelper.error("Error reading pickle file: {e}", logger_name="file_utils")` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/file_utils.py` | L182 | `LoggingHelper.error("Error reading data file {file_path}: {e}", logger_name="file_utils")` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/file_utils.py` | L202 | `LoggingHelper.error("Error reading pickle file {pkl_file_path}: {e}", logger_name="file_utils")` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L190 | `LoggingHelper.error(f"Error checking restricted keywords: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L252 | `LoggingHelper.error(f"Unsupported file type: {file_type}", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L384 | `LoggingHelper.error(f"Error getting intent from extracted text: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L487 | `LoggingHelper.error(f"Failed to save usage events to MongoDB for PDF processing: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L524 | `LoggingHelper.error(f"Error processing CSV: {e}", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L539 | `LoggingHelper.error(f"Error processing Excel file: {e}", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L546 | `LoggingHelper.error(f"Error reading file: {e}", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L569 | `LoggingHelper.error(f"Error reading file: {e}", user_id=user_id, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L588 | `LoggingHelper.error(f"Error getting intent for data analysis: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L610 | `LoggingHelper.error(f"Failed to save usage events to MongoDB for data analysis file upload: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L734 | `LoggingHelper.error(f"Error reading pickle file {pkl_file_path}: {e}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L842 | `LoggingHelper.error(f"Error in event generator: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L863 | `LoggingHelper.error(f"Failed to save usage events to MongoDB for web search: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L956 | `LoggingHelper.error(f"Error in event generator: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L977 | `LoggingHelper.error(f"Failed to save usage events to MongoDB for self answer (fallback): {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L1067 | `LoggingHelper.error(f"Error in event generator: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L1088 | `LoggingHelper.error(f"Failed to save usage events to MongoDB for self answer: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L1095 | `LoggingHelper.error(f"Error in web search: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L1171 | `LoggingHelper.error(f"Failed to save usage events to MongoDB: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L1180 | `LoggingHelper.error(f"Error in process_files: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/routes/file_processing_routes.py` | L1199 | `LoggingHelper.error(f"Failed to save usage events to MongoDB on error: {str(save_error)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/token_tracking.py` | L116 | `LoggingHelper.error(f"Failed to log token event: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/token_tracking.py` | L229 | `LoggingHelper.error(f"Error saving token events to MongoDB: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/pdf_processor.py` | L406 | `LoggingHelper.error(f"Error getting intent: {str(e)}", user_id=None, project_id=project_id,` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/llm_utils.py` | L87 | `LoggingHelper.error("Error in get_intent: {str(e)}", user_id=user_id, project_id=project_id or project_id_context)` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/llm_utils.py` | L142 | `LoggingHelper.error("Error in call_llm: {str(e)}", user_id=user_id, project_id=project_id or project_id_context)` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/llm_utils.py` | L195 | `LoggingHelper.error("Error in get_compliance_report: {str(e)}", user_id=user_id, project_id=project_id or project_id_context)` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/llm_utils.py` | L246 | `LoggingHelper.error("Error generating title: {str(e)}", user_id=user_id, project_id=project_id or project_id_context)` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/llm_utils.py` | L310 | `LoggingHelper.error("Error checking restricted keywords: {str(e)}", user_id=user_id, project_id=project_id or project_id_context)` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/llm_utils.py` | L463 | `LoggingHelper.error("Error in custom_llm_prompt_call: {str(e)}", user_id=user_id, project_id=project_id or project_id_context)` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/llm_utils.py` | L691 | `LoggingHelper.error(f"Real-time streaming failed: {str(streaming_error)}", user_id=user_id, project_id=project_id)` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/llm_utils.py` | L778 | `LoggingHelper.error(f"Failed to create structured response: {str(parsing_error)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/llm_utils.py` | L816 | `LoggingHelper.error("Error in fake_llm_response: {str(e)}", user_id=user_id, project_id=project_id)` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/llm_service.py` | L162 | `LoggingHelper.error(f"Proactive token limit check failed: {error_msg}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/llm_service.py` | L239 | `LoggingHelper.error("Error in chat completion", user_id=user_id, project_id=project_id, extra_data={"error": str(e)})` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/llm_service.py` | L293 | `LoggingHelper.error(f"Proactive token limit check failed: {error_msg}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/llm_service.py` | L654 | `LoggingHelper.error("Error in structured output", user_id=user_id, project_id=project_id, extra_data={"error": str(e)})` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/llm_service.py` | L696 | `LoggingHelper.error("Error in fallback JSON parsing", user_id=user_id, project_id=project_id, extra_data={"error": str(e)})` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/llm_service.py` | L972 | `LoggingHelper.error("Error in JSON mode response", user_id=user_id, project_id=project_id, extra_data={"error": str(e)})` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/llm_service.py` | L1140 | `LoggingHelper.error("Error in stream completion", user_id=user_id, project_id=project_id, extra_data={"error": str(e)})` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/data_analyst_agent.py` | L240 | `LoggingHelper.error(f"STREAMING_DEBUG - Failed to queue item {item_count}: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/data_analyst_agent.py` | L250 | `LoggingHelper.error(f"STREAMING_DEBUG - Error in sync generator: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/text_extraction_utils.py` | L579 | `LoggingHelper.error(f"Error extracting text from image using vision model: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/image_utils.py` | L92 | `LoggingHelper.error(f"Error processing image {image_path}: {str(e)}", logger_name="image_utils")` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/image_utils.py` | L144 | `LoggingHelper.error(f"Error encoding image {image_path}: {str(e)}", logger_name="image_utils")` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/usage_tracking_migration.py` | L133 | `LoggingHelper.error(f"Error during coverage verification: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/usage_tracking_migration.py` | L198 | `LoggingHelper.error(f"Error identifying missing operations: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/services/usage_tracking_migration.py` | L280 | `LoggingHelper.error(f"Error generating coverage report: {str(e)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/.specstory/history/2025-06-11_12-52-check-token-consumption-consistency.md` | L1392 | `-                             LoggingHelper.error("Failed to save token events to MongoDB for data analysis query: {str(e)}", user_id=user_id, project_id=project_id)` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/.specstory/history/2025-06-02_08-23-implement-real-time-streaming-with-content-filtering.md` | L948 | `-             LoggingHelper.error(f"Structured output failed: {str(streaming_error)}", user_id=user_id, project_id=project_id)` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/.specstory/history/2025-06-02_08-23-implement-real-time-streaming-with-content-filtering.md` | L949 | `+             LoggingHelper.error(f"Real-time streaming failed: {str(streaming_error)}", user_id=user_id, project_id=project_id)` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/.specstory/history/2025-06-02_08-23-implement-real-time-streaming-with-content-filtering.md` | L992 | `+             LoggingHelper.error(f"Failed to create structured response: {str(parsing_error)}",` |
| `file:///Users/<USER>/Desktop/Cogveel/NEWquip_project/Dev/NeuquipAIBackend/.specstory/history/2025-06-02_08-23-implement-real-time-streaming-with-content-filtering.md` | L2108 | `LoggingHelper.error(f"Failed to create structured response: {str(parsing_error)}",` |

</details>

---



---

Based on my analysis, I've found the root cause of your compliance report generation error. The issue is a **logging bug** where error messages with placeholder variables aren't being formatted correctly, which then causes the actual error details to be hidden.

Here's what's happening:

1. The system is trying to read a pickle file: `files/final_test/non_data_analysis/sample_gdpr_document.pkl`
2. The file doesn't exist or can't be read, causing an exception
3. The error logging uses unformatted string placeholders like `{pkl_file_path}: {e}` instead of actual values
4. This results in the unhelpful error message you see in the logs

Let me fix the logging issues in the affected files:

---

Cancelled

---

