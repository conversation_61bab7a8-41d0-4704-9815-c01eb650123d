<!-- Generated by SpecStory -->

# Compliance check context truncation (2025-06-12 15:03:09)

_**User**_

whenn doing compliance check are we truncating the context if it is above the limit the model can handle

---

_**Assistant**_

I'll search the codebase to understand how compliance checks handle context size limitations.

---

<details>
            <summary>Searched codebase "compliance check context truncation limit" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `services/pdf_processor.py` | L453-485 |
| `routes/compliance_routes.py` | L1308-1331 |
| `services/text_extraction_utils.py` | L157-213 |
| `services/prompts.py` | L201-226 |
| `routes/compliance_routes.py` | L920-942 |
| `services/prompts.py` | L226-246 |
| `exceptions/api_exceptions.py` | L111-119 |
| `services/text_extraction_utils.py` | L330-365 |
| `services/text_extraction_utils.py` | L213-220 |
| `services/llm_utils.py` | L189-197 |
| `routes/compliance_routes.py` | L598-630 |
| `services/llm_utils.py` | L144-189 |
| `services/llm_service.py` | L902-929 |
| `logs/users/ovais-1/projects/final_test11m1sccnvnsddnmbmhjdd/app.log` | L65-111 |
| `services/llm_service.py` | L826-864 |
| `routes/compliance_routes.py` | L165-199 |
| `routes/compliance_routes.py` | L776-806 |
| `services/mongo_manager.py` | L361-404 |
| `routes/compliance_routes.py` | L199-247 |
| `routes/compliance_routes.py` | L35-78 |
| `logs/users/ovais-1/projects/final_test11m1sccnvnsddnmbmhjdd/app.log` | L116-162 |
| `files/final_test/compliance_reports/sample_gdpr_document_PII_compliance.json` | L13-16 |
| `routes/compliance_routes.py` | L713-740 |
| `routes/compliance_routes.py` | L740-776 |
| `routes/compliance_routes.py` | L971-1005 |

</details>

---

Let me check the routes where compliance checks are performed to understand how they handle context limits.

---

<details>
            <summary>Searched codebase "compliance check route endpoint" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `routes/compliance_routes.py` | L776-806 |
| `routes/compliance_routes.py` | L35-78 |
| `routes/compliance_routes.py` | L165-199 |
| `routes/compliance_routes.py` | L713-740 |
| `routes/compliance_routes.py` | L740-776 |
| `routes/compliance_routes.py` | L598-630 |
| `routes/compliance_routes.py` | L833-868 |
| `routes/compliance_routes.py` | L920-942 |
| `routes/compliance_routes.py` | L111-146 |
| `routes/compliance_routes.py` | L146-165 |
| `routes/compliance_routes.py` | L1033-1063 |
| `routes/compliance_routes.py` | L1005-1033 |
| `routes/compliance_routes.py` | L806-833 |
| `routes/compliance_routes.py` | L630-659 |
| `routes/compliance_routes.py` | L1433-1438 |
| `routes/compliance_routes.py` | L514-538 |
| `routes/compliance_routes.py` | L1308-1331 |
| `routes/compliance_routes.py` | L538-566 |
| `routes/compliance_routes.py` | L690-713 |
| `routes/compliance_routes.py` | L199-247 |
| `routes/compliance_routes.py` | L566-598 |
| `routes/compliance_routes.py` | L659-690 |
| `routes/compliance_routes.py` | L971-1005 |
| `routes/compliance_routes.py` | L1063-1116 |
| `routes/compliance_routes.py` | L495-514 |

</details>

---

Let me check how we extract text for compliance checks and how token limits are handled.

---

<details>
            <summary>Searched codebase "generate compliance report pdf processor content extraction" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `routes/compliance_routes.py` | L199-247 |
| `routes/compliance_routes.py` | L293-346 |
| `routes/compliance_routes.py` | L1363-1390 |
| `routes/compliance_routes.py` | L1161-1214 |
| `routes/compliance_routes.py` | L455-495 |
| `routes/compliance_routes.py` | L146-165 |
| `routes/compliance_routes.py` | L1390-1433 |
| `routes/compliance_routes.py` | L165-199 |
| `routes/compliance_routes.py` | L514-538 |
| `routes/compliance_routes.py` | L1063-1116 |
| `routes/compliance_routes.py` | L1308-1331 |
| `routes/compliance_routes.py` | L495-514 |
| `routes/compliance_routes.py` | L247-293 |
| `routes/compliance_routes.py` | L346-378 |
| `routes/compliance_routes.py` | L414-455 |
| `routes/compliance_routes.py` | L566-598 |
| `routes/compliance_routes.py` | L538-566 |
| `routes/compliance_routes.py` | L378-414 |
| `routes/compliance_routes.py` | L1214-1254 |
| `routes/compliance_routes.py` | L1331-1363 |
| `routes/compliance_routes.py` | L659-690 |
| `routes/compliance_routes.py` | L1254-1291 |
| `routes/compliance_routes.py` | L1291-1308 |
| `routes/compliance_routes.py` | L690-713 |
| `routes/compliance_routes.py` | L1116-1161 |

</details>

---

Let me look at the utility functions used to read pickle files for compliance checks.

---

<details>
            <summary>Searched codebase "read_pickle_file_for_compliance function implementation" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `services/file_utils.py` | L184-204 |
| `routes/compliance_routes.py` | L538-566 |
| `routes/compliance_routes.py` | L514-538 |
| `routes/compliance_routes.py` | L165-199 |
| `routes/compliance_routes.py` | L566-598 |
| `services/mongo_manager.py` | L307-332 |
| `routes/compliance_routes.py` | L199-247 |
| `services/file_utils.py` | L52-92 |
| `routes/compliance_routes.py` | L630-659 |
| `models/insight_model.py` | L71-76 |
| `services/prompts.py` | L201-226 |
| `routes/compliance_routes.py` | L146-165 |
| `routes/compliance_routes.py` | L598-630 |
| `routes/compliance_routes.py` | L111-146 |
| `routes/compliance_routes.py` | L659-690 |
| `services/mongo_manager.py` | L272-307 |
| `routes/compliance_routes.py` | L1308-1331 |
| `services/mongo_manager.py` | L332-361 |
| `routes/compliance_routes.py` | L78-111 |
| `routes/compliance_routes.py` | L690-713 |
| `routes/compliance_routes.py` | L1390-1433 |
| `services/file_utils.py` | L1-52 |
| `routes/compliance_routes.py` | L920-942 |
| `routes/compliance_routes.py` | L1-35 |
| `routes/file_processing_routes.py` | L1-51 |

</details>

---

Let me look at how token calculations and truncation work specifically in the compliance context:

---

<details>
            <summary>Searched codebase "get_compliance_report function implementation token limit" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `services/llm_utils.py` | L144-189 |
| `routes/compliance_routes.py` | L35-78 |
| `services/llm_utils.py` | L189-197 |
| `routes/compliance_routes.py` | L713-740 |
| `routes/compliance_routes.py` | L806-833 |
| `routes/compliance_routes.py` | L1005-1033 |
| `routes/compliance_routes.py` | L566-598 |
| `routes/compliance_routes.py` | L776-806 |
| `services/llm_service.py` | L826-864 |
| `routes/compliance_routes.py` | L146-165 |
| `services/prompts.py` | L201-226 |
| `routes/compliance_routes.py` | L971-1005 |
| `routes/compliance_routes.py` | L1-35 |
| `routes/compliance_routes.py` | L165-199 |
| `services/prompts.py` | L226-246 |
| `routes/compliance_routes.py` | L690-713 |
| `routes/compliance_routes.py` | L1308-1331 |
| `routes/compliance_routes.py` | L920-942 |
| `routes/compliance_routes.py` | L199-247 |
| `routes/compliance_routes.py` | L833-868 |
| `routes/compliance_routes.py` | L1033-1063 |
| `routes/compliance_routes.py` | L740-776 |
| `routes/compliance_routes.py` | L495-514 |
| `services/usage_tracking_migration.py` | L239-271 |
| `routes/compliance_routes.py` | L1063-1116 |

</details>

---

Looking at the token utilities used for truncation:

---

<details>
            <summary>Searched codebase "token_utils get_tokens get_model_context_window" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `services/llm_service.py` | L1420-1452 |
| `services/llm_service.py` | L1477-1492 |
| `services/token_utils.py` | L1-49 |
| `services/llm_service.py` | L1278-1311 |
| `services/llm_service.py` | L1191-1216 |
| `services/text_extraction_utils.py` | L220-274 |
| `config/models.py` | L1-52 |
| `services/text_extraction_utils.py` | L460-496 |
| `services/llm_service.py` | L271-309 |
| `services/token_utils.py` | L49-57 |
| `services/token_utils.py` | L146-159 |
| `services/token_utils.py` | L105-146 |
| `services/pdf_processor.py` | L453-485 |
| `services/llm_service.py` | L1248-1278 |
| `services/llm_service.py` | L137-181 |
| `services/token_utils.py` | L187-210 |
| `services/llm_service.py` | L1216-1248 |
| `services/token_utils.py` | L210-248 |
| `services/llm_utils.py` | L1-31 |
| `services/token_utils.py` | L80-105 |
| `services/token_utils.py` | L57-80 |
| `services/token_utils.py` | L159-187 |
| `services/text_extraction_utils.py` | L496-540 |
| `services/openai_utils.py` | L161-201 |
| `services/token_utils.py` | L248-274 |

</details>

---

Based on my search of the codebase, I can provide you with a clear answer about how compliance checks handle context size limitations:

Yes, the system does truncate the context if it's above the limit the model can handle during compliance checks. Here's how it works:

1. When processing files for compliance checks, the system:

   - Reads the content of the file using `read_pickle_file_for_compliance`
   - Passes this content to `get_compliance_prompt` to create a prompt
   - Uses `get_compliance_report` to generate the compliance analysis

2. The `UnifiedLLMService` class has built-in token management that:

   - Calculates input tokens using `get_tokens` from the token_utils module
   - Gets the model's context window using `get_model_context_window`
   - Applies a safety margin of 5000 tokens
   - Checks if input tokens exceed the maximum safe limit

3. If the content exceeds the token limit:

   - In `pdf_processor.py`, there's an intelligent truncation mechanism that triggers when content exceeds available tokens
   - The system uses `truncate_content_intelligently` function which preserves parts from both the beginning and end of the document
   - It adds a clear "[CONTENT TRUNCATED]" message in between
   - The truncation preserves about 2000 tokens from the start and 1000 tokens from the end by default

4. The system logs when truncation happens:
   - "Content exceeds available tokens, applying intelligent truncation"
   - Information about original tokens, available tokens, and the truncated result

This approach ensures that even very large documents can be processed for compliance checking, while still maintaining enough context for meaningful analysis.

---

_**User**_



---

_**Assistant**_

Based on the log entries you shared, I can see that the system is not truncating the context when it exceeds token limits for compliance checks. Instead, it's detecting the token limit exceedance and returning an error.

The key sequence in the logs shows:

1. The system reads a large PDF file ("big file.pdf") with content size of 612,610 bytes
2. When it attempts to process this content:
   - It calculates the token count as 131,755 tokens
   - The model (deepseek-chat) has a context window of 65,536 tokens
   - With a safety margin of 5,000 tokens, the safe limit is 60,536 tokens
3. Since the content exceeds this limit (201% utilization), the system:
   - Logs a critical token warning
   - Fails the operation with error: "Input tokens (131,755) exceed safe limit (60,536)"
   - Returns a 400 error with message: "Your request contains too much text to process. Please reduce the amount of text and try again."

So rather than automatically truncating large content for compliance checks, the system is currently rejecting requests that exceed the token limit and asking users to reduce the text size manually.

---

_**User**_

should we truncate the text from the pickle files when the limit is hit, i mean implementing same strategy as in fake llm response for truncation

---

_**Assistant**_

Yes, implementing the same intelligent truncation strategy for compliance checks as you're using in other parts of the system would be a good approach. Rather than returning an error when token limits are exceeded, you could automatically truncate the content to fit within the model's context window.

Looking at your codebase, you already have the `truncate_content_intelligently` function in `services/text_extraction_utils.py` which does exactly what you need:

1. It preserves important content from both the start and end of the document
2. It adds a clear indication that content has been truncated
3. It respects token limits for the specified model

To implement this for compliance checks, you could modify the `generate_compliance_report` endpoint in `routes/compliance_routes.py`. Specifically, after reading the content but before passing it to the LLM, add a token check and truncation step:

---

```diff
  from services.async_fs_utils import async_makedirs, async_remove, async_path_exists
  from services.logging_utils import LoggingHelper
+ from services.token_utils import get_tokens
+ from services.llm_service import get_model_context_window
+ from services.text_extraction_utils import truncate_content_intelligently
  
  # Configure logging - will use project+user specific logging
```

```diff
                                    extra_data={"file_name": file_name, "content_size_bytes": len(content)})
  
+                 # Check for token limit and truncate if needed before generating compliance report
+                 from services.token_utils import get_tokens
+                 from services.llm_service import get_model_context_window
+                 from services.text_extraction_utils import truncate_content_intelligently
+                 
+                 # Get project model information
+                 project_model = await mongo_manager.get_model_for_project(actual_project_id)
+                 from services.llm_service import get_provider_economy_model
+                 model = get_provider_economy_model(project_model)
+                 
+                 # Create prompt and estimate tokens
+                 prompt_template = get_compliance_prompt(content, framework_type)
+                 prompt_tokens = get_tokens(prompt_template, model)
+                 
+                 # Get context window and calculate available tokens
+                 context_window = get_model_context_window(model)
+                 safety_margin = 5000
+                 max_safe_tokens = context_window - safety_margin
+                 
+                 if prompt_tokens > max_safe_tokens:
+                     LoggingHelper.warning(
+                         f"Content exceeds available tokens ({prompt_tokens} > {max_safe_tokens}), applying intelligent truncation",
+                         user_id=user_id, project_id=project_id,
+                         extra_data={
+                             "original_tokens": prompt_tokens,
+                             "available_tokens": max_safe_tokens,
+                             "context_window": context_window,
+                             "safety_margin": safety_margin,
+                             "file_name": file_name
+                         }
+                     )
+                     
+                     # Get just the raw content portion
+                     # Estimate tokens needed for prompt template without content
+                     empty_prompt = get_compliance_prompt("", framework_type)
+                     template_tokens = get_tokens(empty_prompt, model)
+                     
+                     # Calculate how many tokens we can use for content
+                     available_for_content = max_safe_tokens - template_tokens
+                     
+                     # Truncate content intelligently
+                     truncated_content = await truncate_content_intelligently(
+                         content, available_for_content, model
+                     )
+                     
+                     # Create new prompt with truncated content
+                     prompt = get_compliance_prompt(truncated_content, framework_type)
+                     
+                     LoggingHelper.info(
+                         f"Content truncated from {prompt_tokens} to {get_tokens(prompt, model)} tokens",
+                         user_id=user_id, project_id=project_id,
+                         extra_data={"file_name": file_name}
+                     )
+                 else:
+                     # Use original content if within limits
+                     prompt = prompt_template
+ 
                  # Generate compliance report for this file
                  LoggingHelper.info(f"Generating compliance report for {file_name}", user_id=user_id, project_id=project_id,
                                    extra_data={"file_name": file_name, "framework_type": framework_type})
-                 prompt = get_compliance_prompt(content, framework_type)
                  compliance_report = await get_compliance_report(prompt, actual_project_id, mongo_manager, usage_logger, usage_logger)
                  # Convert ComplianceReport Pydantic model to dictionary
```

---

Now let's remove the duplicate imports at the top since we're already importing them within the function:

---



---

Let's fix this properly by using search_replace to make these changes:

---



---

Let's also clean up the additional import statements inside the function since we're importing them multiple times:

---



---

Now let's also handle the error case in the try-except block to ensure the user gets a better error message:

---



---

Let's read the current file to find the exact error handling pattern we need to modify:

---

Read file: routes/compliance_routes.py

---

Let's examine the exception handling section:

---

Read file: routes/compliance_routes.py

---

Let's modify the exception handling to specifically handle ContextLengthExceededError:

---



---

Let's also import the required exception at the top of the file:

---



---

Let's read the imports section of the file to get the correct string to replace:

---

Read file: routes/compliance_routes.py

---

Now let's add the import for ContextLengthExceededError:

---



---

Great! Now, let's remove the duplicate import in the exception handler since we've added it at the top of the file:

---



---

Perfect! With these changes, your compliance check system will now:

1. Check if the content exceeds the token limit before sending it to the LLM
2. If it does, intelligently truncate the content to fit within the model's context window
3. If the content is still too large even after truncation, gracefully handle the error and provide a helpful message
4. Continue processing other files even if one file is too large

This implementation follows the same intelligent truncation strategy that's already used elsewhere in your codebase, preserving the most important parts of the document while ensuring compliance checks can be performed on large files.

---

_**User**_

in custom llm usecase:-



---

_**Assistant**_

I see the same token limit issue is happening with custom prompts as well. Let's apply a similar intelligent truncation strategy to the custom LLM prompt functionality.

Looking at the logs, we can see that custom prompts are hitting the same token limit (130,880 tokens exceeding the 65,536 token limit with 5,000 token safety margin).

Let's modify the custom LLM prompt handler to implement the same truncation approach:

---

<details>
            <summary>Searched codebase "custom_llm_prompt_call function implementation" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `services/llm_utils.py` | L314-369 |
| `services/llm_utils.py` | L369-435 |
| `services/llm_utils.py` | L435-465 |
| `services/llm_utils.py` | L89-130 |
| `services/llm_utils.py` | L130-144 |
| `services/openai_utils.py` | L468-478 |
| `services/llm_utils.py` | L197-243 |
| `services/llm_service.py` | L137-181 |
| `services/llm_utils.py` | L1-31 |
| `services/openai_utils.py` | L123-161 |
| `services/llm_service.py` | L77-111 |
| `services/llm_service.py` | L1558-1573 |
| `services/llm_service.py` | L929-969 |
| `services/llm_service.py` | L111-137 |
| `services/llm_service.py` | L505-538 |
| `services/llm_service.py` | L634-659 |
| `services/llm_service.py` | L659-689 |
| `services/llm_utils.py` | L243-250 |
| `services/llm_service.py` | L778-804 |
| `services/llm_service.py` | L424-456 |
| `services/llm_utils.py` | L31-75 |
| `services/llm_service.py` | L394-424 |
| `services/llm_utils.py` | L465-513 |
| `services/llm_utils.py` | L513-546 |
| `services/llm_service.py` | L309-340 |

</details>

---

Let's look at the text truncation utility that's used in other parts of the code:

---

Read file: services/text_extraction_utils.py

---

Now, let's modify the `custom_llm_prompt_call` function to use the truncation utility:

---



---



---

