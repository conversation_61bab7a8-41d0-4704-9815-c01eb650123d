<!-- Generated by SpecStory -->

# Update LaTeX table example in instructions (2025-06-12 15:08:57)

_**User**_

latex table example that is in insight prompt should be also added to assistant instrctions 

---

_**Assistant**_

Looking at the code, I need to fix the LaTeX table example in the assistant instructions to match the one in the insight prompt.

---



---

Let me check the file contents to ensure I have the right string to replace.

---

Read file: config/messages.py

---

Now let me check the LaTeX table format in the insight prompt to match it properly.

---

Read file: services/prompts.py

---

Let me look at the LaTeX table format in the insight prompt.

---

Read file: services/prompts.py

---

Now I'll update the LaTeX table format in the assistant instructions to match the format in the insight prompt.

---



---

Let me try a more precise search and replace for each instance separately.

---



---

Let me try to read and update the exact content of the LaTeX table in the file.

---

Read file: config/messages.py

---

Now I'll try to edit the file using the edit_file tool to match the format in the insight prompt.

---

```diff
      "* Use consistent alignment and proper mathematical notation when needed "
      "* Example LaTeX table format: "
-     "$$\\begin{array}{|c|c|c|c|c|c|} "
+     "$$"
+     "\\begin{array}{|c|c|c|c|c|c|} "
      "\\hline "
      "\\text{Source} & \\text{SS} & \\text{DF} & \\text{MS} & \\text{F} & \\text{P} \\\\ "
```

```diff
      "\\text{Total} & - & 282 & - & - & - \\\\ "
      "\\hline "
-     "\\end{array}$$ "
+     "\\end{array}"
+     "$$"
      
      "**IMPORTANT - LaTeX Mathematical Expressions:** "
```

```diff
      "* Use consistent alignment and proper mathematical notation when needed "
      "* Example LaTeX table format: "
-     "$$\\begin{array}{|c|c|c|c|c|c|} "
+     "$$"
+     "\\begin{array}{|c|c|c|c|c|c|} "
      "\\hline "
      "\\text{Source} & \\text{SS} & \\text{DF} & \\text{MS} & \\text{F} & \\text{P} \\\\ "
```

```diff
      "\\text{Total} & - & 282 & - & - & - \\\\ "
      "\\hline "
-     "\\end{array}$$ "
+     "\\end{array}"
+     "$$"
      
      "**IMPORTANT - LaTeX Mathematical Expressions:** "
```

---

Great! I've successfully updated the LaTeX table format in both the brief and detailed instruction sections to match the format used in the insight prompt. The format now includes separate lines for the opening and closing dollar signs ($$), which is consistent with the example in the insight prompt.

The changes were made to both:
1. `assistant_instructions_for_brief_summaries`
2. `assistant_instructions_for_detailed_summaries`

This ensures that the LaTeX table format is consistent across all messages and prompts in the application.

---

