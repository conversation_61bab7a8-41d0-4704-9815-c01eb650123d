"""
Unified Usage Tracking Service
Consolidates token and search consumption tracking into a single system.
Tracks all service usage (LLM, Search, Vision, etc.) in one collection.
"""

import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Literal
from dataclasses import dataclass, asdict
from services.token_utils import get_tokens
from services.logging_utils import LoggingHelper

logger = logging.getLogger(__name__)

# Type definitions for better type safety
ServiceCategory = Literal["llm", "search", "vision", "embedding", "other"]
ServiceProvider = Literal["openai", "deepseek", "duckduckgo", "serper", "google", "bing", "other"]
ServiceTier = Literal["free", "paid", "premium", "unknown"]


@dataclass
class UsageEvent:
    """Individual usage consumption event - unified for all services"""
    timestamp: str
    service_category: ServiceCategory
    service_provider: ServiceProvider
    service_tier: ServiceTier
    operation_type: str  # "chat_completion", "web_search", "image_generation", etc.
    operation_subtype: str  # "streaming", "duckduckgo_search", "vision_analysis", etc.
    input_tokens: int
    output_tokens: int
    total_tokens: int
    success: bool
    error_message: Optional[str]
    operation_details: Dict[str, Any]  # Service-specific details


class UnifiedUsageLogger:
    """
    Unified usage logger that tracks ALL service consumption in one place.
    Replaces separate TokenEventLogger and SearchEventLogger.
    """
    
    def __init__(self, project_id: str, user_id: str = None, query_id: str = None, mongo_manager=None):
        self.project_id = project_id
        self.user_id = user_id
        self.query_id = query_id or self._generate_query_id()
        self.response_id = None  # Will be set when response is generated
        self.events: List[UsageEvent] = []
        self._total_operations = 0
        self._total_input_tokens = 0
        self._total_output_tokens = 0
        self.mongo_manager = mongo_manager

        # Use project+user specific logging
        LoggingHelper.info(f"UnifiedUsageLogger initialized",
                          user_id=self.user_id, project_id=self.project_id,
                          extra_data={"query_id": self.query_id})

    def _generate_query_id(self) -> str:
        """Generate unique query ID"""
        return f"unified_query_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S_%f')}"
    
    def _resolve_service_info(self, service_provider: str, service_category: str = None) -> tuple[ServiceProvider, ServiceTier]:
        """Resolve service provider to standardized values and tier"""
        provider_mapping = {
            "openai": ("openai", "paid"),
            "deepseek": ("deepseek", "paid"),
            "duckduckgo": ("duckduckgo", "free"),
            "serper": ("serper", "paid"),
            "google": ("google", "paid"),
            "bing": ("bing", "paid")
        }
        
        provider_lower = service_provider.lower()
        if provider_lower in provider_mapping:
            return provider_mapping[provider_lower]
        
        # Default fallback
        return ("other", "unknown")
    
    def _extract_provider_from_model(self, model_name: str) -> ServiceProvider:
        """Extract service provider from model name"""
        model_lower = model_name.lower()
        if any(x in model_lower for x in ["gpt", "openai"]):
            return "openai"
        elif "deepseek" in model_lower:
            return "deepseek"
        elif "gemini" in model_lower:
            return "gemini"
        else:
            return "other"

    def log_event(self,
                  service_category: ServiceCategory = None,
                  service_provider: ServiceProvider = None,
                  operation_type: str = None,
                  input_tokens: int = None,
                  output_tokens: int = None,
                  operation_subtype: str = "general",
                  operation_details: Dict[str, Any] = None,
                  success: bool = True,
                  error_message: str = None,
                  **kwargs) -> None:
        """
        Log individual usage consumption event.
        This is the core method for all usage tracking.

        DO NOT call this method directly. Use the convenience methods instead:
        - For LLM usage: log_llm_usage()
        - For search usage: log_search_usage()
        - For vision usage: log_vision_usage()
        """
        try:
            # Handle legacy TokenEventLogger signature for backward compatibility
            # Old signature: log_event(operation_type, model_name, input_tokens, output_tokens, ...)
            if service_category is not None and service_provider is not None and operation_type is not None and input_tokens is not None and output_tokens is not None:
                # Check if this looks like the old signature where first param is operation_type
                if isinstance(service_category, str) and isinstance(service_provider, str) and isinstance(operation_type, int):
                    LoggingHelper.warning(f"MIGRATION REQUIRED: Detected legacy TokenEventLogger.log_event() call signature. Converting to UnifiedUsageLogger format.",
                                         user_id=self.user_id, project_id=self.project_id,
                                         extra_data={"detected_operation_type": service_category, "detected_model_name": service_provider})

                    # Remap parameters from old signature to new signature
                    old_operation_type = service_category  # First param was operation_type
                    old_model_name = service_provider      # Second param was model_name
                    old_input_tokens = operation_type      # Third param was input_tokens
                    old_output_tokens = input_tokens       # Fourth param was output_tokens
                    old_operation_subtype = output_tokens if isinstance(output_tokens, str) else operation_subtype

                    # Use log_llm_usage for legacy calls
                    return self.log_llm_usage(
                        model_name=old_model_name,
                        input_tokens=old_input_tokens,
                        output_tokens=old_output_tokens,
                        operation_type=old_operation_type,
                        operation_subtype=old_operation_subtype,
                        operation_details=operation_details,
                        success=success,
                        error_message=error_message
                    )

            # Validate required parameters for new signature
            if service_category is None or service_provider is None or operation_type is None:
                raise ValueError(f"Missing required parameters. Got: service_category={service_category}, service_provider={service_provider}, operation_type={operation_type}")

            if input_tokens is None or output_tokens is None:
                raise ValueError(f"Missing token parameters. Got: input_tokens={input_tokens}, output_tokens={output_tokens}")

            # Handle deprecated model_name parameter for backward compatibility
            if 'model_name' in kwargs:
                LoggingHelper.warning(f"MIGRATION REQUIRED: 'model_name' parameter passed to log_event(). Use log_llm_usage() instead.",
                                     user_id=self.user_id, project_id=self.project_id,
                                     extra_data={"model_name": kwargs['model_name'], "operation_type": operation_type})
                # Add model_name to operation_details if not already present
                if operation_details is None:
                    operation_details = {}
                if "model_name" not in operation_details:
                    operation_details["model_name"] = kwargs['model_name']
            # Resolve service information
            provider, service_tier = self._resolve_service_info(service_provider, service_category)
            total_tokens = input_tokens + output_tokens
            
            # Create usage event
            event = UsageEvent(
                timestamp=datetime.now(timezone.utc).isoformat(),
                service_category=service_category,
                service_provider=provider,
                service_tier=service_tier,
                operation_type=operation_type,
                operation_subtype=operation_subtype,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                total_tokens=total_tokens,
                success=success,
                error_message=error_message,
                operation_details=operation_details or {}
            )
            
            # Add to events list
            self.events.append(event)
            
            # Update totals
            self._total_operations += 1
            self._total_input_tokens += input_tokens
            self._total_output_tokens += output_tokens
            
            # Structured logging for monitoring
            self._log_to_application_logs(event)

            LoggingHelper.debug(f"Usage event logged: {service_category}/{operation_type} - Input: {input_tokens}, Output: {output_tokens}",
                               user_id=self.user_id, project_id=self.project_id,
                               extra_data={"service_category": service_category, "service_provider": service_provider,
                                         "input_tokens": input_tokens, "output_tokens": output_tokens})

        except Exception as e:
            LoggingHelper.error(f"Error logging usage event: {str(e)}",
                               user_id=self.user_id, project_id=self.project_id,
                               extra_data={"error": str(e), "service_category": service_category,
                                         "service_provider": service_provider})

    def _log_to_application_logs(self, event: UsageEvent) -> None:
        """Log usage event to application logs for monitoring"""
        log_data = {
            "query_id": self.query_id,
            "service_category": event.service_category,
            "service_provider": event.service_provider,
            "service_tier": event.service_tier,
            "operation_type": event.operation_type,
            "operation_subtype": event.operation_subtype,
            "input_tokens": event.input_tokens,
            "output_tokens": event.output_tokens,
            "total_tokens": event.total_tokens,
            "success": event.success,
            "timestamp": event.timestamp
        }

        # Enhanced logging with step-by-step details
        LoggingHelper.info(
            f"USAGE_CONSUMPTION - Category: {event.service_category} | "
            f"Provider: {event.service_provider} | "
            f"Operation: {event.operation_type} | "
            f"Subtype: {event.operation_subtype} | "
            f"Input Tokens: {event.input_tokens} | "
            f"Output Tokens: {event.output_tokens} | "
            f"Total Tokens: {event.total_tokens} | "
            f"Running Total Operations: {self._total_operations} | "
            f"Success: {event.success}",
            user_id=self.user_id, project_id=self.project_id,
            extra_data=log_data
        )

    # Convenience methods for specific service types
    def log_llm_usage(self, 
                      model_name: str,
                      input_tokens: int,
                      output_tokens: int,
                      operation_type: str = "chat_completion",
                      operation_subtype: str = "general",
                      operation_details: Dict[str, Any] = None,
                      success: bool = True,
                      error_message: str = None) -> None:
        """Log LLM token usage"""
        provider = self._extract_provider_from_model(model_name)
        
        details = {
            "model_name": model_name,
            **(operation_details or {})
        }
        
        self.log_event(
            service_category="llm",
            service_provider=provider,
            operation_type=operation_type,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            operation_subtype=operation_subtype,
            operation_details=details,
            success=success,
            error_message=error_message
        )

    def log_search_usage(self,
                        service_provider: ServiceProvider,
                        query: str,
                        response_text: str,
                        operation_subtype: str = "web_search",
                        operation_details: Dict[str, Any] = None,
                        success: bool = True,
                        error_message: str = None) -> None:
        """Log search usage"""
        # Set token counts to 0 for web search operations as cost is calculated per operation/call
        input_tokens = 0
        output_tokens = 0

        details = {
            "query": query[:500],  # Truncate for storage
            "response_length": len(response_text) if response_text else 0,
            **(operation_details or {})
        }

        self.log_event(
            service_category="search",
            service_provider=service_provider,
            operation_type="web_search",
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            operation_subtype=operation_subtype,
            operation_details=details,
            success=success,
            error_message=error_message
        )

    def log_vision_usage(self,
                        model_name: str,
                        input_tokens: int,
                        output_tokens: int,
                        operation_type: str = "image_analysis",
                        operation_subtype: str = "text_extraction",
                        operation_details: Dict[str, Any] = None,
                        success: bool = True,
                        error_message: str = None) -> None:
        """Log vision model usage"""
        provider = self._extract_provider_from_model(model_name)

        details = {
            "model_name": model_name,
            **(operation_details or {})
        }

        self.log_event(
            service_category="vision",
            service_provider=provider,
            operation_type=operation_type,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            operation_subtype=operation_subtype,
            operation_details=details,
            success=success,
            error_message=error_message
        )

    def get_total_consumption(self) -> tuple[int, int, int]:
        """Get total consumption metrics across all services"""
        return (
            self._total_operations,
            self._total_input_tokens,
            self._total_output_tokens
        )

    def get_total_tokens(self) -> tuple[int, int]:
        """Get total input and output tokens for backward compatibility with TokenEventLogger"""
        return self._total_input_tokens, self._total_output_tokens

    def get_consumption_by_category(self) -> Dict[str, Dict[str, int]]:
        """Get consumption metrics grouped by service category"""
        category_stats = {}

        for event in self.events:
            category = event.service_category
            if category not in category_stats:
                category_stats[category] = {
                    "operations": 0,
                    "input_tokens": 0,
                    "output_tokens": 0,
                    "total_tokens": 0
                }

            category_stats[category]["operations"] += 1
            category_stats[category]["input_tokens"] += event.input_tokens
            category_stats[category]["output_tokens"] += event.output_tokens
            category_stats[category]["total_tokens"] += event.total_tokens

        return category_stats

    def get_consumption_by_provider(self) -> Dict[str, Dict[str, int]]:
        """Get consumption metrics grouped by service provider"""
        provider_stats = {}

        for event in self.events:
            provider = event.service_provider
            if provider not in provider_stats:
                provider_stats[provider] = {
                    "operations": 0,
                    "input_tokens": 0,
                    "output_tokens": 0,
                    "total_tokens": 0
                }

            provider_stats[provider]["operations"] += 1
            provider_stats[provider]["input_tokens"] += event.input_tokens
            provider_stats[provider]["output_tokens"] += event.output_tokens
            provider_stats[provider]["total_tokens"] += event.total_tokens

        return provider_stats

    def log_step_summary(self, step_name: str, additional_info: str = "") -> None:
        """Log a summary of usage consumption up to this step"""
        LoggingHelper.info(
            f"USAGE_STEP_SUMMARY - {step_name} | "
            f"Total Operations: {self._total_operations} | "
            f"Cumulative Input Tokens: {self._total_input_tokens} | "
            f"Cumulative Output Tokens: {self._total_output_tokens} | "
            f"Cumulative Total Tokens: {self._total_input_tokens + self._total_output_tokens}"
            f"{' | ' + additional_info if additional_info else ''}",
            user_id=self.user_id, project_id=self.project_id,
            extra_data={
                "step_name": step_name,
                "total_operations": self._total_operations,
                "cumulative_input_tokens": self._total_input_tokens,
                "cumulative_output_tokens": self._total_output_tokens,
                "cumulative_total_tokens": self._total_input_tokens + self._total_output_tokens,
                "additional_info": additional_info
            }
        )

    async def save_to_mongodb(self) -> Optional[str]:
        """Save all usage events to MongoDB"""
        if not self.mongo_manager:
            LoggingHelper.warning("No mongo_manager provided - cannot save usage events to database",
                                 user_id=self.user_id, project_id=self.project_id)
            return None

        if not self.events:
            LoggingHelper.info("No usage events to save",
                              user_id=self.user_id, project_id=self.project_id)
            return None

        try:
            # Convert events to dict format for MongoDB
            events_data = [asdict(event) for event in self.events]

            # Save to MongoDB with response_id link
            result = await self.mongo_manager.save_usage_events(
                project_id=self.project_id,
                query_id=self.query_id,
                user_id=self.user_id,
                events=events_data,
                response_id=self.response_id
            )

            if result:
                LoggingHelper.info(f"Successfully saved {len(self.events)} usage events to MongoDB",
                                  user_id=self.user_id, project_id=self.project_id,
                                  extra_data={"mongodb_id": result, "events_count": len(self.events),
                                            "response_id": self.response_id, "query_id": self.query_id})
            else:
                LoggingHelper.error("Failed to save usage events to MongoDB",
                                   user_id=self.user_id, project_id=self.project_id,
                                   extra_data={"events_count": len(self.events)})

            return result
        except Exception as e:
            LoggingHelper.error(f"Error saving usage events to MongoDB: {str(e)}",
                               user_id=self.user_id, project_id=self.project_id,
                               extra_data={"error": str(e), "events_count": len(self.events)})
            return None

    def get_events_summary(self) -> List[Dict[str, Any]]:
        """Get detailed breakdown of all usage events"""
        return [asdict(event) for event in self.events]

    def set_response_id(self, response_id: str) -> None:
        """Set response ID to link usage events with response"""
        self.response_id = response_id
        LoggingHelper.debug(f"Usage events linked to response_id: {response_id}",
                           user_id=self.user_id, project_id=self.project_id,
                           extra_data={"response_id": response_id, "query_id": self.query_id})


# Factory function for creating unified usage logger
def create_unified_usage_logger(project_id: str, user_id: str = None, mongo_manager=None) -> UnifiedUsageLogger:
    """Factory function to create a unified usage logger"""
    return UnifiedUsageLogger(
        project_id=project_id,
        user_id=user_id,
        mongo_manager=mongo_manager
    )
