"""
Unified LLM Service for OpenAI and DeepSeek providers using LangChain.
This service provides a consistent interface for chat completions, structured output, and streaming.
"""

import logging
import json
import os
from typing import Optional, List, Dict, Any, AsyncGenerator, Union
from datetime import datetime, timezone

from langchain_core.messages import HumanMessage, SystemMessage, BaseMessage
from langchain_core.language_models.chat_models import BaseChatModel
from services.token_tracking import TokenEventLogger, TokenTrackingMixin
from services.unified_usage_tracking import UnifiedUsageLogger
from services.token_utils import get_tokens, estimate_tokens_from_messages, estimate_output_tokens
from langchain_openai import ChatOpenAI
from pydantic import BaseModel

from exceptions.api_exceptions import handle_llm_error
import config
from services.logging_utils import LoggingHelper

def _get_context_or_fallback():
    """Get user/project context with fallback to None for system operations."""
    try:
        from services.logging_middleware import get_current_user_id, get_current_project_id
        return get_current_user_id(), get_current_project_id()
    except:
        return None, None

# Try to import ChatDeepSeek, fallback to ChatOpenAI if not available
try:
    from langchain_deepseek import ChatDeepSeek
    DEEPSEEK_AVAILABLE = True
    user_id, project_id = _get_context_or_fallback()
    LoggingHelper.info("ChatDeepSeek available for native DeepSeek support", user_id=user_id, project_id=project_id)
except (ImportError, NameError, Exception) as e:
    user_id, project_id = _get_context_or_fallback()
    LoggingHelper.warning(f"langchain_deepseek not available ({str(e)}), falling back to ChatOpenAI for DeepSeek", user_id=user_id, project_id=project_id)
    ChatDeepSeek = None
    DEEPSEEK_AVAILABLE = False

# Try to import ChatGoogleGenerativeAI for Gemini support
try:
    from langchain_google_genai import ChatGoogleGenerativeAI
    GEMINI_AVAILABLE = True
    user_id, project_id = _get_context_or_fallback()
    LoggingHelper.info("ChatGoogleGenerativeAI available for native Gemini support", user_id=user_id, project_id=project_id)
except (ImportError, NameError, Exception) as e:
    user_id, project_id = _get_context_or_fallback()
    LoggingHelper.warning(f"langchain_google_genai not available ({str(e)}), Gemini models will not be supported", user_id=user_id, project_id=project_id)
    ChatGoogleGenerativeAI = None
    GEMINI_AVAILABLE = False


class LLMProvider:
    """Enum-like class for supported providers"""
    OPENAI = "openai"
    DEEPSEEK = "deepseek"
    GEMINI = "gemini"


class UnifiedLLMService(TokenTrackingMixin):
    """
    Unified interface for multiple LLM providers using LangChain.
    Supports OpenAI, DeepSeek, and Gemini with consistent async interface.
    Includes comprehensive token tracking for all operations.
    """

    def __init__(self, provider: str, model: str, **kwargs):
        """
        Initialize the LLM service with specified provider and model.

        Args:
            provider: Either 'openai', 'deepseek', or 'gemini'
            model: Model name (e.g., 'gpt-4o', 'deepseek-chat', 'gemini-1.5-flash')
            **kwargs: Additional parameters like temperature, max_tokens, etc.
        """
        super().__init__()
        self.provider = provider.lower()
        self.model = model
        self.llm = self._create_llm(**kwargs)

    def _create_llm(self, **kwargs) -> BaseChatModel:
        """Factory method to create provider-specific LLM instances"""
        default_params = {
            # "temperature": kwargs.get("temperature", 0),
            "max_tokens": kwargs.get("max_tokens", 4000),
        }

        if self.provider == LLMProvider.OPENAI:
            return ChatOpenAI(
                model=self.model,
                api_key=config.openai_api_key,
                **default_params,
                **kwargs
            )
        elif self.provider == LLMProvider.DEEPSEEK:
            if DEEPSEEK_AVAILABLE and ChatDeepSeek:
                # Use native ChatDeepSeek for better compatibility
                user_id, project_id = _get_context_or_fallback()
                LoggingHelper.info("Using native ChatDeepSeek", user_id=user_id, project_id=project_id,
                                  extra_data={"model": self.model})
                return ChatDeepSeek(
                    model=self.model,
                    api_key=config.deepseek_api_key,
                    **default_params,
                    **kwargs
                )
            else:
                # Fallback to ChatOpenAI with DeepSeek endpoint
                user_id, project_id = _get_context_or_fallback()
                LoggingHelper.info("Using ChatOpenAI fallback for DeepSeek", user_id=user_id, project_id=project_id,
                                  extra_data={"model": self.model})
                return ChatOpenAI(
                    model=self.model,
                    api_key=config.deepseek_api_key,
                    base_url="https://api.deepseek.com",
                    **default_params,
                    **kwargs
                )
        elif self.provider == LLMProvider.GEMINI:
            if GEMINI_AVAILABLE and ChatGoogleGenerativeAI:
                # Use native ChatGoogleGenerativeAI for Gemini models
                user_id, project_id = _get_context_or_fallback()
                LoggingHelper.info("Using native ChatGoogleGenerativeAI", user_id=user_id, project_id=project_id,
                                  extra_data={"model": self.model})
                return ChatGoogleGenerativeAI(
                    model=self.model,
                    google_api_key=config.gemini_api_key,
                    **default_params,
                    **kwargs
                )
            else:
                # Gemini requires native support, no fallback available
                user_id, project_id = _get_context_or_fallback()
                LoggingHelper.error("ChatGoogleGenerativeAI not available, cannot use Gemini models",
                                   user_id=user_id, project_id=project_id,
                                   extra_data={"model": self.model})
                raise ValueError(f"Gemini models require langchain-google-genai package. Please install it to use {self.model}")
        else:
            raise ValueError(f"Unsupported provider: {self.provider}")

    async def chat_completion(
        self,
        messages: Union[List[Dict[str, str]], List[BaseMessage]],
        operation_type: str = "general",
        operation_subtype: str = "chat_completion",
        operation_details: Dict[str, Any] = None,
        **kwargs
    ) -> str:
        """
        Unified chat completion interface with token tracking.

        Args:
            messages: List of messages in dict format or LangChain message format
            operation_type: Type of operation for token tracking
            operation_subtype: Subtype of operation for token tracking
            operation_details: Additional details for token tracking
            **kwargs: Additional parameters

        Returns:
            String response from the model
        """
        input_tokens = 0
        output_tokens = 0
        success = True
        error_message = None

        try:
            # Log operation start
            self._log_operation_start(operation_type, operation_subtype)

            # Convert dict messages to LangChain message format if needed
            if messages and isinstance(messages[0], dict):
                langchain_messages = self._convert_to_langchain_messages(messages)
            else:
                langchain_messages = messages

            # Estimate input tokens
            input_tokens = self._estimate_input_tokens(messages)

            # Log token context information
            self._log_token_context_info(input_tokens, operation_type)

            # Proactive token management - check if we're exceeding safe limits
            context_window = get_model_context_window(self.model)
            safety_margin = 5000  # Reserve tokens for response and safety
            max_safe_tokens = context_window - safety_margin

            if input_tokens > max_safe_tokens:
                user_id, project_id = _get_context_or_fallback()
                error_msg = f"Input tokens ({input_tokens:,}) exceed safe limit ({max_safe_tokens:,}) for model {self.model}. Context window: {context_window:,}, Safety margin: {safety_margin:,}"
                LoggingHelper.error(f"Proactive token limit check failed: {error_msg}",
                                  user_id=user_id, project_id=project_id)

                # Track failed operation
                self._track_operation(
                    operation_type=operation_type,
                    model_name=self.model,
                    input_tokens=input_tokens,
                    output_tokens=0,
                    operation_subtype=operation_subtype,
                    operation_details=operation_details,
                    success=False,
                    error_message=error_msg
                )

                raise ValueError(error_msg)

            # Make LLM call
            response = await self.llm.ainvoke(langchain_messages, **kwargs)

            # Extract actual token usage from response metadata
            actual_tokens = self._extract_token_usage(response)
            if actual_tokens['input_tokens'] > 0:
                input_tokens = actual_tokens['input_tokens']

                # For vision models, OpenAI only counts text tokens in prompt_tokens
                # We need to add our estimated image tokens for accurate tracking
                if self._is_vision_model_call(langchain_messages):
                    estimated_image_tokens = self._estimate_image_tokens_from_messages(langchain_messages)
                    input_tokens += estimated_image_tokens
                    LoggingHelper.info(f"Vision model token adjustment: OpenAI text tokens ({actual_tokens['input_tokens']}) + estimated image tokens ({estimated_image_tokens}) = {input_tokens} total",
                                     extra_data={"openai_text_tokens": actual_tokens['input_tokens'],
                                               "estimated_image_tokens": estimated_image_tokens,
                                               "total_input_tokens": input_tokens})

            if actual_tokens['output_tokens'] > 0:
                output_tokens = actual_tokens['output_tokens']
            else:
                # Fallback to estimation
                output_tokens = self._estimate_output_tokens(response.content)

            # Track token usage
            self._track_operation(
                operation_type=operation_type,
                model_name=self.model,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                operation_subtype=operation_subtype,
                operation_details=operation_details,
                success=success
            )

            # Log operation end
            self._log_operation_end(operation_type, operation_subtype, success, input_tokens, output_tokens)

            return response.content

        except Exception as e:
            success = False
            error_message = str(e)

            # Track failed operation (still consumed input tokens)
            self._track_operation(
                operation_type=operation_type,
                model_name=self.model,
                input_tokens=input_tokens,
                output_tokens=0,
                operation_subtype=operation_subtype,
                operation_details=operation_details,
                success=success,
                error_message=error_message
            )

            # Log operation end (failed)
            self._log_operation_end(operation_type, operation_subtype, success, input_tokens, 0)

            user_id, project_id = _get_context_or_fallback()
            LoggingHelper.error("Error in chat completion", user_id=user_id, project_id=project_id, extra_data={"error": str(e)})
            handle_llm_error(e)

    async def structured_output(
        self,
        messages: Union[List[Dict[str, str]], List[BaseMessage]],
        pydantic_model: BaseModel = None,
        json_mode: bool = False,
        operation_type: str = "general",
        operation_subtype: str = "structured_output",
        operation_details: Dict[str, Any] = None,
        **kwargs
    ) -> Union[Dict, BaseModel]:
        """
        Unified structured output interface with token tracking.

        Args:
            messages: List of messages
            pydantic_model: Pydantic model for structured output
            json_mode: Whether to use JSON mode instead of structured output
            operation_type: Type of operation for token tracking
            operation_subtype: Subtype of operation for token tracking
            operation_details: Additional details for token tracking
            **kwargs: Additional parameters

        Returns:
            Structured response (dict or Pydantic model instance)
        """
        input_tokens = 0
        output_tokens = 0
        success = True
        error_message = None

        try:
            # Convert dict messages to LangChain message format if needed
            if messages and isinstance(messages[0], dict):
                langchain_messages = self._convert_to_langchain_messages(messages)
            else:
                langchain_messages = messages

            # Estimate input tokens
            input_tokens = self._estimate_input_tokens(messages)

            # Log token context information
            self._log_token_context_info(input_tokens, operation_type)

            # Proactive token management - check if we're exceeding safe limits
            context_window = get_model_context_window(self.model)
            safety_margin = 5000  # Reserve tokens for response and safety
            max_safe_tokens = context_window - safety_margin

            if input_tokens > max_safe_tokens:
                user_id, project_id = _get_context_or_fallback()
                error_msg = f"Input tokens ({input_tokens:,}) exceed safe limit ({max_safe_tokens:,}) for model {self.model}. Context window: {context_window:,}, Safety margin: {safety_margin:,}"
                LoggingHelper.error(f"Proactive token limit check failed: {error_msg}",
                                  user_id=user_id, project_id=project_id)

                # Track failed operation
                self._track_operation(
                    operation_type=operation_type,
                    model_name=self.model,
                    input_tokens=input_tokens,
                    output_tokens=0,
                    operation_subtype=operation_subtype,
                    operation_details=operation_details,
                    success=False,
                    error_message=error_msg
                )

                raise ValueError(error_msg)

            if pydantic_model:
                # Provider-specific handling for structured output
                if self.provider == LLMProvider.OPENAI:
                    # OpenAI supports structured output with Pydantic models
                    try:
                        structured_llm = self.llm.with_structured_output(pydantic_model)
                        response = await structured_llm.ainvoke(langchain_messages, **kwargs)

                        # Use unified token extraction for consistent tracking
                        from services.token_utils import extract_tokens_unified
                        token_data = await extract_tokens_unified(
                            response=response,
                            messages=langchain_messages,
                            model_name=self.model,
                            operation_type=operation_type
                        )
                        
                        input_tokens = token_data["input_tokens"]
                        output_tokens = token_data["output_tokens"]
                        
                        # Log token extraction source for monitoring
                        user_id, project_id = _get_context_or_fallback()
                        LoggingHelper.info(f"Structured output token extraction: {token_data['source']}", 
                                          user_id=user_id, project_id=project_id,
                                          extra_data={
                                              "token_source": token_data["source"],
                                              "input_tokens": input_tokens,
                                              "output_tokens": output_tokens,
                                              "operation_type": operation_type
                                          })

                        # Track the operation
                        self._track_operation(
                            operation_type=operation_type,
                            model_name=self.model,
                            input_tokens=input_tokens,
                            output_tokens=output_tokens,
                            operation_subtype=operation_subtype,
                            operation_details=operation_details,
                            success=success
                        )

                        return response
                    except Exception as e:
                        user_id, project_id = _get_context_or_fallback()
                        LoggingHelper.warning("OpenAI structured output failed, falling back to JSON mode", user_id=user_id, project_id=project_id, extra_data={"error": str(e)})
                        # Fallback to JSON mode for OpenAI as well
                        response = await self._fallback_json_parsing(langchain_messages, pydantic_model, **kwargs)

                        # Use unified token extraction for consistent tracking
                        from services.token_utils import extract_tokens_unified
                        token_data = await extract_tokens_unified(
                            response=response,
                            messages=langchain_messages,
                            model_name=self.model,
                            operation_type=operation_type
                        )
                        
                        input_tokens = token_data["input_tokens"]
                        output_tokens = token_data["output_tokens"]
                        
                        # Log token extraction source for monitoring
                        user_id, project_id = _get_context_or_fallback()
                        LoggingHelper.info(f"Fallback JSON parsing token extraction: {token_data['source']}", 
                                          user_id=user_id, project_id=project_id,
                                          extra_data={
                                              "token_source": token_data["source"],
                                              "input_tokens": input_tokens,
                                              "output_tokens": output_tokens,
                                              "operation_type": operation_type
                                          })

                        # Track the operation
                        self._track_operation(
                            operation_type=operation_type,
                            model_name=self.model,
                            input_tokens=input_tokens,
                            output_tokens=output_tokens,
                            operation_subtype=operation_subtype,
                            operation_details=operation_details,
                            success=success
                        )

                        return response

                elif self.provider == LLMProvider.DEEPSEEK:
                    if DEEPSEEK_AVAILABLE and isinstance(self.llm, type(ChatDeepSeek)) if ChatDeepSeek else False:
                        # Try native ChatDeepSeek structured output first
                        try:
                            structured_llm = self.llm.with_structured_output(pydantic_model)
                            response = await structured_llm.ainvoke(langchain_messages, **kwargs)

                            # Use unified token extraction for consistent tracking
                            from services.token_utils import extract_tokens_unified
                            token_data = await extract_tokens_unified(
                                response=response,
                                messages=langchain_messages,
                                model_name=self.model,
                                operation_type=operation_type
                            )
                            
                            input_tokens = token_data["input_tokens"]
                            output_tokens = token_data["output_tokens"]
                            
                            # Log token extraction source for monitoring
                            user_id, project_id = _get_context_or_fallback()
                            LoggingHelper.info(f"Structured output token extraction: {token_data['source']}", 
                                              user_id=user_id, project_id=project_id,
                                              extra_data={
                                                  "token_source": token_data["source"],
                                                  "input_tokens": input_tokens,
                                                  "output_tokens": output_tokens,
                                                  "operation_type": operation_type
                                              })

                            # Track the operation
                            self._track_operation(
                                operation_type=operation_type,
                                model_name=self.model,
                                input_tokens=input_tokens,
                                output_tokens=output_tokens,
                                operation_subtype=operation_subtype,
                                operation_details=operation_details,
                                success=success
                            )

                            return response
                        except Exception as e:
                            user_id, project_id = _get_context_or_fallback()
                            LoggingHelper.info("Native ChatDeepSeek structured output not supported, using JSON mode", user_id=user_id, project_id=project_id, extra_data={"error": str(e)})
                            response = await self._fallback_json_parsing(langchain_messages, pydantic_model, **kwargs)

                            # Use unified token extraction for consistent tracking
                            from services.token_utils import extract_tokens_unified
                            token_data = await extract_tokens_unified(
                                response=response,
                                messages=langchain_messages,
                                model_name=self.model,
                                operation_type=operation_type
                            )
                            
                            input_tokens = token_data["input_tokens"]
                            output_tokens = token_data["output_tokens"]
                            
                            # Log token extraction source for monitoring
                            user_id, project_id = _get_context_or_fallback()
                            LoggingHelper.info(f"Fallback JSON parsing token extraction: {token_data['source']}", 
                                              user_id=user_id, project_id=project_id,
                                              extra_data={
                                                  "token_source": token_data["source"],
                                                  "input_tokens": input_tokens,
                                                  "output_tokens": output_tokens,
                                                  "operation_type": operation_type
                                              })

                            # Track the operation
                            self._track_operation(
                                operation_type=operation_type,
                                model_name=self.model,
                                input_tokens=input_tokens,
                                output_tokens=output_tokens,
                                operation_subtype=operation_subtype,
                                operation_details=operation_details,
                                success=success
                            )

                            return response
                    else:
                        # Using ChatOpenAI with DeepSeek endpoint - use JSON mode
                        user_id, project_id = _get_context_or_fallback()
                        LoggingHelper.info("Using JSON mode for DeepSeek via ChatOpenAI", user_id=user_id, project_id=project_id)
                        response = await self._fallback_json_parsing(langchain_messages, pydantic_model, **kwargs)

                        # Use unified token extraction for consistent tracking
                        from services.token_utils import extract_tokens_unified
                        token_data = await extract_tokens_unified(
                            response=response,
                            messages=langchain_messages,
                            model_name=self.model,
                            operation_type=operation_type
                        )
                        
                        input_tokens = token_data["input_tokens"]
                        output_tokens = token_data["output_tokens"]
                        
                        # Log token extraction source for monitoring
                        user_id, project_id = _get_context_or_fallback()
                        LoggingHelper.info(f"Fallback JSON parsing token extraction: {token_data['source']}", 
                                          user_id=user_id, project_id=project_id,
                                          extra_data={
                                              "token_source": token_data["source"],
                                              "input_tokens": input_tokens,
                                              "output_tokens": output_tokens,
                                              "operation_type": operation_type
                                          })

                        # Track the operation
                        self._track_operation(
                            operation_type=operation_type,
                            model_name=self.model,
                            input_tokens=input_tokens,
                            output_tokens=output_tokens,
                            operation_subtype=operation_subtype,
                            operation_details=operation_details,
                            success=success
                        )

                        return response

                elif self.provider == LLMProvider.GEMINI:
                    if GEMINI_AVAILABLE and isinstance(self.llm, type(ChatGoogleGenerativeAI)) if ChatGoogleGenerativeAI else False:
                        # Try native ChatGoogleGenerativeAI structured output first
                        try:
                            structured_llm = self.llm.with_structured_output(pydantic_model)
                            response = await structured_llm.ainvoke(langchain_messages, **kwargs)

                            # Use unified token extraction for consistent tracking
                            from services.token_utils import extract_tokens_unified
                            token_data = await extract_tokens_unified(
                                response=response,
                                messages=langchain_messages,
                                model_name=self.model,
                                operation_type=operation_type
                            )

                            input_tokens = token_data["input_tokens"]
                            output_tokens = token_data["output_tokens"]

                            # Log token extraction source for monitoring
                            user_id, project_id = _get_context_or_fallback()
                            LoggingHelper.info(f"Structured output token extraction: {token_data['source']}",
                                              user_id=user_id, project_id=project_id,
                                              extra_data={
                                                  "token_source": token_data["source"],
                                                  "input_tokens": input_tokens,
                                                  "output_tokens": output_tokens,
                                                  "operation_type": operation_type
                                              })

                            # Track the operation
                            self._track_operation(
                                operation_type=operation_type,
                                model_name=self.model,
                                input_tokens=input_tokens,
                                output_tokens=output_tokens,
                                operation_subtype=operation_subtype,
                                operation_details=operation_details,
                                success=success
                            )

                            return response
                        except Exception as e:
                            user_id, project_id = _get_context_or_fallback()
                            LoggingHelper.info("Native ChatGoogleGenerativeAI structured output not supported, using JSON mode", user_id=user_id, project_id=project_id, extra_data={"error": str(e)})
                            response = await self._fallback_json_parsing(langchain_messages, pydantic_model, **kwargs)

                            # Use unified token extraction for consistent tracking
                            from services.token_utils import extract_tokens_unified
                            token_data = await extract_tokens_unified(
                                response=response,
                                messages=langchain_messages,
                                model_name=self.model,
                                operation_type=operation_type
                            )

                            input_tokens = token_data["input_tokens"]
                            output_tokens = token_data["output_tokens"]

                            # Log token extraction source for monitoring
                            user_id, project_id = _get_context_or_fallback()
                            LoggingHelper.info(f"Fallback JSON parsing token extraction: {token_data['source']}",
                                              user_id=user_id, project_id=project_id,
                                              extra_data={
                                                  "token_source": token_data["source"],
                                                  "input_tokens": input_tokens,
                                                  "output_tokens": output_tokens,
                                                  "operation_type": operation_type
                                              })

                            # Track the operation
                            self._track_operation(
                                operation_type=operation_type,
                                model_name=self.model,
                                input_tokens=input_tokens,
                                output_tokens=output_tokens,
                                operation_subtype=operation_subtype,
                                operation_details=operation_details,
                                success=success
                            )

                            return response
                    else:
                        # Gemini requires native support, use JSON mode fallback
                        user_id, project_id = _get_context_or_fallback()
                        LoggingHelper.info("Using JSON mode for Gemini", user_id=user_id, project_id=project_id)
                        response = await self._fallback_json_parsing(langchain_messages, pydantic_model, **kwargs)

                        # Use unified token extraction for consistent tracking
                        from services.token_utils import extract_tokens_unified
                        token_data = await extract_tokens_unified(
                            response=response,
                            messages=langchain_messages,
                            model_name=self.model,
                            operation_type=operation_type
                        )

                        input_tokens = token_data["input_tokens"]
                        output_tokens = token_data["output_tokens"]

                        # Log token extraction source for monitoring
                        user_id, project_id = _get_context_or_fallback()
                        LoggingHelper.info(f"Fallback JSON parsing token extraction: {token_data['source']}",
                                          user_id=user_id, project_id=project_id,
                                          extra_data={
                                              "token_source": token_data["source"],
                                              "input_tokens": input_tokens,
                                              "output_tokens": output_tokens,
                                              "operation_type": operation_type
                                          })

                        # Track the operation
                        self._track_operation(
                            operation_type=operation_type,
                            model_name=self.model,
                            input_tokens=input_tokens,
                            output_tokens=output_tokens,
                            operation_subtype=operation_subtype,
                            operation_details=operation_details,
                            success=success
                        )

                        return response
                else:
                    # For other providers, use JSON mode and manual parsing
                    user_id, project_id = _get_context_or_fallback()
                    LoggingHelper.info("Provider using JSON mode fallback", user_id=user_id, project_id=project_id, extra_data={"provider": self.provider})
                    response = await self._fallback_json_parsing(langchain_messages, pydantic_model, **kwargs)

                    # Use unified token extraction for consistent tracking
                    from services.token_utils import extract_tokens_unified
                    token_data = await extract_tokens_unified(
                        response=response,
                        messages=langchain_messages,
                        model_name=self.model,
                        operation_type=operation_type
                    )
                    
                    input_tokens = token_data["input_tokens"]
                    output_tokens = token_data["output_tokens"]
                    
                    # Log token extraction source for monitoring
                    user_id, project_id = _get_context_or_fallback()
                    LoggingHelper.info(f"Fallback JSON parsing token extraction: {token_data['source']}", 
                                      user_id=user_id, project_id=project_id,
                                      extra_data={
                                          "token_source": token_data["source"],
                                          "input_tokens": input_tokens,
                                          "output_tokens": output_tokens,
                                          "operation_type": operation_type
                                      })

                    # Track the operation
                    self._track_operation(
                        operation_type=operation_type,
                        model_name=self.model,
                        input_tokens=input_tokens,
                        output_tokens=output_tokens,
                        operation_subtype=operation_subtype,
                        operation_details=operation_details,
                        success=success
                    )

                    return response

            elif json_mode:
                # Use JSON mode without Pydantic model
                response = await self._json_mode_response(langchain_messages, **kwargs)

                # Use unified token extraction for consistent tracking
                from services.token_utils import extract_tokens_unified
                token_data = await extract_tokens_unified(
                    response=response,
                    messages=langchain_messages,
                    model_name=self.model,
                    operation_type=operation_type
                )
                
                input_tokens = token_data["input_tokens"]
                output_tokens = token_data["output_tokens"]
                
                # Log token extraction source for monitoring
                user_id, project_id = _get_context_or_fallback()
                LoggingHelper.info(f"Fallback JSON parsing token extraction: {token_data['source']}", 
                                  user_id=user_id, project_id=project_id,
                                  extra_data={
                                      "token_source": token_data["source"],
                                      "input_tokens": input_tokens,
                                      "output_tokens": output_tokens,
                                      "operation_type": operation_type
                                  })

                # Track the operation
                self._track_operation(
                    operation_type=operation_type,
                    model_name=self.model,
                    input_tokens=input_tokens,
                    output_tokens=output_tokens,
                    operation_subtype=operation_subtype,
                    operation_details=operation_details,
                    success=success
                )

                return response
            else:
                # Regular response
                response = await self.llm.ainvoke(langchain_messages, **kwargs)

                # Extract actual token usage from response metadata
                actual_tokens = self._extract_token_usage(response)
                if actual_tokens['input_tokens'] > 0:
                    input_tokens = actual_tokens['input_tokens']

                    # For vision models, OpenAI only counts text tokens in prompt_tokens
                    # We need to add our estimated image tokens for accurate tracking
                    if self._is_vision_model_call(langchain_messages):
                        estimated_image_tokens = self._estimate_image_tokens_from_messages(langchain_messages)
                        input_tokens += estimated_image_tokens
                        LoggingHelper.info(f"Vision model token adjustment: OpenAI text tokens ({actual_tokens['input_tokens']}) + estimated image tokens ({estimated_image_tokens}) = {input_tokens} total",
                                         extra_data={"openai_text_tokens": actual_tokens['input_tokens'],
                                                   "estimated_image_tokens": estimated_image_tokens,
                                                   "total_input_tokens": input_tokens})

                if actual_tokens['output_tokens'] > 0:
                    output_tokens = actual_tokens['output_tokens']
                else:
                    # Fallback to estimation
                    output_tokens = self._estimate_output_tokens(response.content)

                # Track the operation
                self._track_operation(
                    operation_type=operation_type,
                    model_name=self.model,
                    input_tokens=input_tokens,
                    output_tokens=output_tokens,
                    operation_subtype=operation_subtype,
                    operation_details=operation_details,
                    success=success
                )

                return response.content

        except Exception as e:
            success = False
            error_message = str(e)

            # Track failed operation
            self._track_operation(
                operation_type=operation_type,
                model_name=self.model,
                input_tokens=input_tokens,
                output_tokens=0,
                operation_subtype=operation_subtype,
                operation_details=operation_details,
                success=success,
                error_message=error_message
            )

            user_id, project_id = _get_context_or_fallback()
            LoggingHelper.error("Error in structured output", user_id=user_id, project_id=project_id, extra_data={"error": str(e)})
            handle_llm_error(e)

    async def _fallback_json_parsing(self, langchain_messages, pydantic_model, **kwargs):
        """Fallback method for structured output using JSON mode and manual parsing"""
        try:
            # Add schema instruction to the prompt
            schema_instruction = f"\n\nPlease respond with a valid JSON object that matches this schema: {pydantic_model.model_json_schema()}"

            # Create a copy of messages to avoid modifying the original
            messages_copy = langchain_messages.copy()
            if messages_copy:
                last_message = messages_copy[-1]
                if hasattr(last_message, 'content'):
                    last_message.content += schema_instruction

            # Get JSON response
            json_response = await self._json_mode_response(messages_copy, **kwargs)

            # Parse and validate against Pydantic model
            if isinstance(json_response, dict):
                try:
                    # Try to fix common issues with DeepSeek responses
                    fixed_response = self._fix_json_response(json_response, pydantic_model)
                    return pydantic_model(**fixed_response)
                except (ValueError, TypeError) as e:
                    user_id, project_id = _get_context_or_fallback()
                    LoggingHelper.warning("Failed to validate JSON against Pydantic model", user_id=user_id, project_id=project_id, extra_data={
                        "error": str(e),
                        "model_name": pydantic_model.__name__ if hasattr(pydantic_model, '__name__') else str(pydantic_model),
                        "json_response_keys": list(json_response.keys()) if isinstance(json_response, dict) else "not_dict",
                        "json_response_sample": str(json_response)[:500] if json_response else "empty"
                    })
                    # Create a default response with required fields
                    return self._create_default_response(pydantic_model)
            else:
                user_id, project_id = _get_context_or_fallback()
                LoggingHelper.warning("JSON response is not a dict, returning default response", user_id=user_id, project_id=project_id)
                return self._create_default_response(pydantic_model)

        except Exception as e:
            user_id, project_id = _get_context_or_fallback()
            LoggingHelper.error("Error in fallback JSON parsing", user_id=user_id, project_id=project_id, extra_data={"error": str(e)})
            return self._create_default_response(pydantic_model)

    def _fix_json_response(self, json_response: dict, pydantic_model) -> dict:
        """Fix common issues in JSON responses from different providers"""
        # Handle SourceSelecton model specifically
        if hasattr(pydantic_model, '__name__') and pydantic_model.__name__ == 'SourceSelecton':
            # Fix common DeepSeek response format issues
            if 'source' in json_response and isinstance(json_response['source'], dict):
                # Convert single source dict to list of Tools
                source_dict = json_response['source']
                # Fix WebSearch -> WebSeach (the model actually expects WebSeach with typo)
                if 'WebSearch' in source_dict:
                    source_dict['WebSeach'] = source_dict.pop('WebSearch')
                json_response['source'] = [source_dict]

            # Ensure all required fields exist with proper types
            # Convert non-string values to strings for string fields
            if 'intent' not in json_response or not isinstance(json_response['intent'], str):
                json_response['intent'] = str(json_response.get('intent', 'general_query')) if json_response.get('intent') is not None else 'general_query'

            if 'query' not in json_response or not isinstance(json_response['query'], str):
                json_response['query'] = str(json_response.get('query', '')) if json_response.get('query') is not None else ''

            if 'clarification' not in json_response or not isinstance(json_response['clarification'], str):
                json_response['clarification'] = str(json_response.get('clarification', '')) if json_response.get('clarification') is not None else ''

            if 'answer' not in json_response or not isinstance(json_response['answer'], str):
                json_response['answer'] = str(json_response.get('answer', '')) if json_response.get('answer') is not None else ''

            # Ensure source is a list
            if 'source' not in json_response or not isinstance(json_response['source'], list):
                json_response['source'] = []

        # Handle InsightExtraction model specifically
        elif hasattr(pydantic_model, '__name__') and pydantic_model.__name__ == 'InsightExtraction':
            # Fix common issues with InsightExtraction responses

            # Ensure summary field exists and is a string
            if 'summary' not in json_response:
                json_response['summary'] = 'Analysis completed successfully.'
            elif not isinstance(json_response['summary'], str):
                # Convert non-string values to string
                json_response['summary'] = str(json_response['summary']) if json_response['summary'] is not None else 'Analysis completed successfully.'

            # Ensure tables field exists and is a list
            if 'tables' not in json_response:
                json_response['tables'] = []
            elif not isinstance(json_response['tables'], list):
                json_response['tables'] = []
            else:
                # Fix individual table entries if they exist
                fixed_tables = []
                for table in json_response['tables']:
                    if isinstance(table, dict):
                        # Fix common table field issues
                        fixed_table = {
                            'chart_name': str(table.get('chart_name', 'Chart')),
                            'type_of_chart': str(table.get('type_of_chart', 'bar')),
                            'chart_decription': str(table.get('chart_decription', table.get('chart_description', 'Chart description'))),
                            'x_labels': table.get('x_labels', []) if isinstance(table.get('x_labels'), list) else [],
                            'y_labels': table.get('y_labels', []) if isinstance(table.get('y_labels'), list) else [],
                            'values': []
                        }

                        # Fix values field - ensure it's a list of integers
                        values = table.get('values', [])
                        if isinstance(values, list):
                            for val in values:
                                try:
                                    # Convert to int, handling various formats
                                    if isinstance(val, (int, float)):
                                        fixed_table['values'].append(int(val))
                                    elif isinstance(val, str) and val.replace('.', '').replace('-', '').isdigit():
                                        fixed_table['values'].append(int(float(val)))
                                    else:
                                        fixed_table['values'].append(0)
                                except (ValueError, TypeError):
                                    fixed_table['values'].append(0)

                        fixed_tables.append(fixed_table)
                json_response['tables'] = fixed_tables

            # Ensure suggested_questions field exists and is a list of strings
            if 'suggested_questions' not in json_response:
                json_response['suggested_questions'] = []
            elif not isinstance(json_response['suggested_questions'], list):
                json_response['suggested_questions'] = []
            else:
                # Ensure all questions are strings
                json_response['suggested_questions'] = [
                    str(q) for q in json_response['suggested_questions']
                    if q is not None
                ]

        # Handle DocumentIntentExtraction model specifically
        elif hasattr(pydantic_model, '__name__') and pydantic_model.__name__ == 'DocumentIntentExtraction':
            # Ensure context_intent field exists and is properly structured
            if 'context_intent' not in json_response:
                json_response['context_intent'] = {
                    'intent_description': 'General document analysis',
                    'keywords': ['document', 'analysis', 'content']
                }
            elif not isinstance(json_response['context_intent'], dict):
                json_response['context_intent'] = {
                    'intent_description': 'General document analysis',
                    'keywords': ['document', 'analysis', 'content']
                }
            else:
                # Fix the context_intent structure
                context_intent = json_response['context_intent']

                # Ensure intent_description is a string
                if 'intent_description' not in context_intent or not isinstance(context_intent['intent_description'], str):
                    context_intent['intent_description'] = str(context_intent.get('intent_description', 'General document analysis'))

                # Ensure keywords is a list of strings
                if 'keywords' not in context_intent:
                    context_intent['keywords'] = ['document', 'analysis', 'content']
                elif not isinstance(context_intent['keywords'], list):
                    context_intent['keywords'] = ['document', 'analysis', 'content']
                else:
                    # Ensure all keywords are strings
                    context_intent['keywords'] = [
                        str(kw) for kw in context_intent['keywords']
                        if kw is not None
                    ]
                    # Ensure we have at least some keywords
                    if not context_intent['keywords']:
                        context_intent['keywords'] = ['document', 'analysis', 'content']

        # Handle ComplianceReport model specifically
        elif hasattr(pydantic_model, '__name__') and pydantic_model.__name__ == 'ComplianceReport':
            # Ensure framework field exists and is a string
            if 'framework' not in json_response or not isinstance(json_response['framework'], str):
                json_response['framework'] = str(json_response.get('framework', 'Unknown'))

            # Ensure is_valid is a boolean
            if 'is_valid' not in json_response:
                json_response['is_valid'] = True
            elif not isinstance(json_response['is_valid'], bool):
                # Convert string/int to boolean
                val = json_response['is_valid']
                if isinstance(val, str):
                    json_response['is_valid'] = val.lower() in ('true', '1', 'yes', 'valid')
                else:
                    json_response['is_valid'] = bool(val)

            # Ensure individual_factors_score is a dict
            if 'individual_factors_score' not in json_response:
                json_response['individual_factors_score'] = {}
            elif not isinstance(json_response['individual_factors_score'], dict):
                json_response['individual_factors_score'] = {}

            # Ensure average_score is a float
            if 'average_score' not in json_response:
                json_response['average_score'] = 0.0
            else:
                try:
                    json_response['average_score'] = float(json_response['average_score'])
                except (ValueError, TypeError):
                    json_response['average_score'] = 0.0

            # Ensure summary is a string
            if 'summary' not in json_response:
                json_response['summary'] = ""
            elif not isinstance(json_response['summary'], str):
                json_response['summary'] = str(json_response['summary']) if json_response['summary'] is not None else ""

            # Ensure reason is a string
            if 'reason' not in json_response:
                json_response['reason'] = ""
            elif not isinstance(json_response['reason'], str):
                json_response['reason'] = str(json_response['reason']) if json_response['reason'] is not None else ""

        return json_response

    def _create_default_response(self, pydantic_model):
        """Create a default response for the given Pydantic model"""
        # Handle SourceSelecton model specifically
        if hasattr(pydantic_model, '__name__') and pydantic_model.__name__ == 'SourceSelecton':
            from models.insight_model import Tools
            default_tools = Tools(
                Filename=[],
                FileTool=False,
                WebSeach=False,  # Note: using the typo as it's in the model
                SelfAnswer=True
            )
            return pydantic_model(
                intent="general_query",
                query="",
                source=[default_tools],
                clarification="",
                answer=""
            )
        # Handle InsightExtraction model specifically
        elif hasattr(pydantic_model, '__name__') and pydantic_model.__name__ == 'InsightExtraction':
            return pydantic_model(
                summary="I apologize, but I encountered an issue processing your request. Please try again.",
                tables=[],
                suggested_questions=[
                    "Could you please rephrase your question?",
                    "What specific information are you looking for?",
                    "Would you like me to analyze a different aspect of the data?"
                ]
            )
        # Handle DocumentIntentExtraction model specifically
        elif hasattr(pydantic_model, '__name__') and pydantic_model.__name__ == 'DocumentIntentExtraction':
            from models.insight_model import ContextIntent
            default_context_intent = ContextIntent(
                intent_description="General document analysis",
                keywords=["document", "analysis", "content"]
            )
            return pydantic_model(
                context_intent=default_context_intent
            )
        # Handle ComplianceReport model specifically
        elif hasattr(pydantic_model, '__name__') and pydantic_model.__name__ == 'ComplianceReport':
            return pydantic_model(
                framework="Unknown",
                is_valid=False,
                individual_factors_score={},
                average_score=0.0,
                summary="Unable to complete compliance analysis due to technical issues.",
                reason="System error occurred during analysis."
            )
        else:
            # For other models, try to create with empty/default values
            try:
                return pydantic_model()
            except:
                # If that fails, raise the original error
                raise

    async def _json_mode_response(self, langchain_messages, **kwargs):
        """Handle JSON mode response for different providers"""
        try:
            if self.provider == LLMProvider.OPENAI:
                # OpenAI supports response_format parameter
                response = await self.llm.ainvoke(
                    langchain_messages,
                    response_format={"type": "json_object"},
                    **kwargs
                )
            else:
                # For other providers, add JSON instruction to prompt and don't use response_format
                messages_copy = langchain_messages.copy()
                if messages_copy:
                    last_message = messages_copy[-1]
                    if hasattr(last_message, 'content'):
                        if "JSON" not in last_message.content:
                            last_message.content += "\n\nPlease respond with a valid JSON object."

                # Remove response_format from kwargs for non-OpenAI providers
                clean_kwargs = {k: v for k, v in kwargs.items() if k != 'response_format'}
                response = await self.llm.ainvoke(messages_copy, **clean_kwargs)

            # Parse JSON response
            try:
                return json.loads(response.content)
            except json.JSONDecodeError:
                # Try to extract JSON from response
                import re
                json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
                if json_match:
                    try:
                        return json.loads(json_match.group())
                    except:
                        pass
                # Fallback: return the raw content wrapped in a dict
                user_id, project_id = _get_context_or_fallback()
                LoggingHelper.warning("Failed to parse JSON response, returning raw content", user_id=user_id, project_id=project_id)
                return {"content": response.content}

        except Exception as e:
            user_id, project_id = _get_context_or_fallback()
            LoggingHelper.error("Error in JSON mode response", user_id=user_id, project_id=project_id, extra_data={"error": str(e)})
            return {"error": str(e)}



    def _convert_to_langchain_messages(self, messages: List[Dict[str, str]]) -> List[BaseMessage]:
        """Convert dict messages to LangChain message format"""
        langchain_messages = []
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")

            if role == "system":
                langchain_messages.append(SystemMessage(content=content))
            elif role in ["user", "human"]:
                # Handle both simple string content and multimodal content
                if isinstance(content, list):
                    # Multimodal content - pass through as-is for LangChain to handle
                    langchain_messages.append(HumanMessage(content=content))
                else:
                    # Simple string content
                    langchain_messages.append(HumanMessage(content=content))
            # Add more role mappings as needed

        return langchain_messages

    def _extract_token_usage(self, response) -> Dict[str, int]:
        """Extract token usage from LangChain response metadata"""
        try:
            if hasattr(response, 'response_metadata'):
                metadata = response.response_metadata

                if self.provider == LLMProvider.OPENAI:
                    # OpenAI format
                    usage = metadata.get('token_usage', {})
                    return {
                        'input_tokens': usage.get('prompt_tokens', 0),
                        'output_tokens': usage.get('completion_tokens', 0),
                        'total_tokens': usage.get('total_tokens', 0)
                    }
                elif self.provider == LLMProvider.DEEPSEEK:
                    # DeepSeek format (may vary based on implementation)
                    usage = metadata.get('usage', {})
                    return {
                        'input_tokens': usage.get('prompt_tokens', 0),
                        'output_tokens': usage.get('completion_tokens', 0),
                        'total_tokens': usage.get('total_tokens', 0)
                    }

            # Fallback - no token usage found
            return {'input_tokens': 0, 'output_tokens': 0, 'total_tokens': 0}

        except Exception as e:
            user_id, project_id = _get_context_or_fallback()
            LoggingHelper.warning("Failed to extract token usage from response", user_id=user_id, project_id=project_id, extra_data={"error": str(e)})
            return {'input_tokens': 0, 'output_tokens': 0, 'total_tokens': 0}

    async def stream_completion(
        self,
        messages: Union[List[Dict[str, str]], List[BaseMessage]],
        operation_type: str = "general",
        operation_subtype: str = "streaming",
        operation_details: Dict[str, Any] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        Unified streaming completion interface with token tracking.

        Args:
            messages: List of messages in dict format or LangChain message format
            operation_type: Type of operation for token tracking
            operation_subtype: Subtype of operation for token tracking
            operation_details: Additional details for token tracking
            **kwargs: Additional parameters

        Yields:
            String chunks from the model response
        """
        input_tokens = 0
        output_tokens = 0
        success = True
        error_message = None
        accumulated_content = ""

        try:
            # Log streaming start for LLM service
            user_id, project_id = _get_context_or_fallback()
            LoggingHelper.info("STREAMING_START - LLM service streaming initiated",
                              user_id=user_id, project_id=project_id,
                              extra_data={
                                  "stream_type": "llm_service",
                                  "model": self.model,
                                  "provider": self.provider,
                                  "operation_type": operation_type,
                                  "operation_subtype": operation_subtype,
                                  "messages_count": len(messages) if messages else 0,
                                  "operation_details": operation_details
                              })

            # Convert dict messages to LangChain message format if needed
            if messages and isinstance(messages[0], dict):
                langchain_messages = self._convert_to_langchain_messages(messages)
            else:
                langchain_messages = messages

            # Estimate input tokens
            input_tokens = self._estimate_input_tokens(messages)

            # Log token context information
            self._log_token_context_info(input_tokens, operation_type)

            # Stream LLM response
            async for chunk in self.llm.astream(langchain_messages, **kwargs):
                content = chunk.content
                accumulated_content += content
                yield content

            # Use unified token extraction for consistent tracking
            from services.token_utils import extract_tokens_unified
            token_data = await extract_tokens_unified(
                accumulated_content=accumulated_content,
                messages=langchain_messages,
                model_name=self.model,
                operation_type=operation_type
            )
            
            input_tokens = token_data["input_tokens"]
            output_tokens = token_data["output_tokens"]
            
            # Log token extraction source for monitoring
            user_id, project_id = _get_context_or_fallback()
            LoggingHelper.info(f"Streaming token extraction: {token_data['source']}", 
                              user_id=user_id, project_id=project_id,
                              extra_data={
                                  "token_source": token_data["source"],
                                  "input_tokens": input_tokens,
                                  "output_tokens": output_tokens,
                                  "operation_type": operation_type
                              })

            # Track token usage for streaming
            self._track_operation(
                operation_type=operation_type,
                model_name=self.model,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                operation_subtype=operation_subtype,
                operation_details=operation_details,
                success=success
            )

        except Exception as e:
            success = False
            error_message = str(e)

            # Track failed streaming operation
            self._track_operation(
                operation_type=operation_type,
                model_name=self.model,
                input_tokens=input_tokens,
                output_tokens=0,
                operation_subtype=operation_subtype,
                operation_details=operation_details,
                success=success,
                error_message=error_message
            )

            user_id, project_id = _get_context_or_fallback()
            LoggingHelper.error("Error in stream completion", user_id=user_id, project_id=project_id, extra_data={"error": str(e)})
            raise

    def _estimate_input_tokens(self, messages: Union[List[Dict[str, str]], List[BaseMessage]]) -> int:
        """Estimate input tokens from messages"""
        return estimate_tokens_from_messages(messages, self.model)

    def _estimate_output_tokens(self, text: str) -> int:
        """Estimate output tokens from generated text"""
        return estimate_output_tokens(text, self.model)

    def _is_vision_model_call(self, messages: List[BaseMessage]) -> bool:
        """Check if this is a vision model call with images"""
        try:
            for message in messages:
                if hasattr(message, 'content') and isinstance(message.content, list):
                    for content_part in message.content:
                        if isinstance(content_part, dict) and content_part.get("type") == "image_url":
                            return True
            return False
        except Exception:
            return False

    def _estimate_image_tokens_from_messages(self, messages: List[BaseMessage]) -> int:
        """Estimate image tokens from multimodal messages"""
        total_image_tokens = 0
        try:
            for message in messages:
                if hasattr(message, 'content') and isinstance(message.content, list):
                    for content_part in message.content:
                        if isinstance(content_part, dict) and content_part.get("type") == "image_url":
                            image_url = content_part.get("image_url", {}).get("url", "")
                            if image_url.startswith("data:image/"):
                                try:
                                    # Extract base64 data and estimate tokens properly
                                    base64_data = image_url.split(",")[1]
                                    # Decode base64 to get actual image size
                                    import base64
                                    image_bytes = base64.b64decode(base64_data)
                                    # Use proper image token estimation
                                    from services.image_utils import estimate_image_tokens
                                    image_tokens = estimate_image_tokens(len(image_bytes))
                                    total_image_tokens += image_tokens
                                except Exception as e:
                                    LoggingHelper.warning(f"Failed to estimate image tokens: {e}")
                                    # Fallback: use a reasonable default for images
                                    total_image_tokens += 1000
            return total_image_tokens
        except Exception as e:
            LoggingHelper.warning(f"Error estimating image tokens from messages: {e}")
            return 0

    def _log_token_context_info(self, input_tokens: int, operation_type: str = "general") -> None:
        """
        Log input token count and model context window information for monitoring.

        Args:
            input_tokens: Estimated input token count
            operation_type: Type of operation being performed
        """
        try:
            # Get model context window
            context_window = get_model_context_window(self.model)

            # Calculate utilization percentage
            utilization_percentage = (input_tokens / context_window) * 100 if context_window > 0 else 0

            # Get user/project context for logging
            user_id, project_id = _get_context_or_fallback()

            # Check if enhanced formatting is enabled
            enhanced_formatting = getattr(config, 'ENHANCED_LOG_FORMATTING', True)
            compact_mode = getattr(config, 'COMPACT_LOG_MODE', False)

            # Create visual separator for better readability
            separator = "=" * 80 if enhanced_formatting else "-" * 50

            # Determine log level and create formatted message based on utilization
            if utilization_percentage >= 90:
                # Critical: Very close to limit
                if enhanced_formatting:
                    formatted_message = f"""
{separator}
CRITICAL TOKEN CONTEXT WARNING - HIGH UTILIZATION DETECTED
{separator}
Token Utilization: {utilization_percentage:.1f}% (VERY HIGH - Near Limit!)
Input Tokens: {input_tokens:,}
Context Window: {context_window:,}
Model: {self.model}
Operation: {operation_type}
Recommendation: Consider reducing input size or splitting the request
{separator}"""
                else:
                    formatted_message = f"CRITICAL TOKEN WARNING: {utilization_percentage:.1f}% utilization | {input_tokens:,}/{context_window:,} tokens | Model: {self.model} | Operation: {operation_type}"

                LoggingHelper.warning(
                    formatted_message,
                    user_id=user_id,
                    project_id=project_id,
                    extra_data={
                        "input_tokens": input_tokens,
                        "context_window": context_window,
                        "utilization_percentage": round(utilization_percentage, 2),
                        "model": self.model,
                        "operation_type": operation_type,
                        "warning_level": "critical"
                    }
                )
            elif utilization_percentage >= 75:
                # Warning: Getting close to limit
                if enhanced_formatting:
                    formatted_message = f"""
{separator}
MODERATE TOKEN CONTEXT WARNING - MODERATE UTILIZATION
{separator}
Token Utilization: {utilization_percentage:.1f}% (Getting Close to Limit)
Input Tokens: {input_tokens:,}
Context Window: {context_window:,}
Model: {self.model}
Operation: {operation_type}
Recommendation: Monitor token usage and consider optimization
{separator}"""
                else:
                    formatted_message = f"MODERATE TOKEN WARNING: {utilization_percentage:.1f}% utilization | {input_tokens:,}/{context_window:,} tokens | Model: {self.model} | Operation: {operation_type}"

                LoggingHelper.warning(
                    formatted_message,
                    user_id=user_id,
                    project_id=project_id,
                    extra_data={
                        "input_tokens": input_tokens,
                        "context_window": context_window,
                        "utilization_percentage": round(utilization_percentage, 2),
                        "model": self.model,
                        "operation_type": operation_type,
                        "warning_level": "moderate"
                    }
                )
            else:
                # Info: Normal usage - more compact format for normal operations
                if enhanced_formatting:
                    formatted_message = f"""
{"-" * 60}
TOKEN CONTEXT INFO - Normal Usage ({utilization_percentage:.1f}%)
Tokens: {input_tokens:,} / {context_window:,} | Model: {self.model} | Operation: {operation_type}
{"-" * 60}"""
                else:
                    formatted_message = f"TOKEN INFO: {utilization_percentage:.1f}% utilization | {input_tokens:,}/{context_window:,} tokens | Model: {self.model} | Operation: {operation_type}"

                LoggingHelper.info(
                    formatted_message,
                    user_id=user_id,
                    project_id=project_id,
                    extra_data={
                        "input_tokens": input_tokens,
                        "context_window": context_window,
                        "utilization_percentage": round(utilization_percentage, 2),
                        "model": self.model,
                        "operation_type": operation_type,
                        "warning_level": "normal"
                    }
                )

        except Exception as e:
            user_id, project_id = _get_context_or_fallback()
            LoggingHelper.error(
                f"ERROR: Failed to log token context information | Error: {str(e)} | Model: {self.model}",
                user_id=user_id,
                project_id=project_id,
                extra_data={"error": str(e), "model": self.model}
            )

    def _log_operation_start(self, operation_type: str, operation_subtype: str = "general") -> None:
        """
        Log the start of an LLM operation for better traceability.

        Args:
            operation_type: Type of operation being performed
            operation_subtype: Subtype of operation
        """
        user_id, project_id = _get_context_or_fallback()

        start_message = f"""
LLM OPERATION START
{'-' * 50}
Operation: {operation_type}
Subtype: {operation_subtype}
Model: {self.model}
Provider: {self.provider}
{'-' * 50}"""

        LoggingHelper.info(
            start_message,
            user_id=user_id,
            project_id=project_id,
            extra_data={
                "operation_type": operation_type,
                "operation_subtype": operation_subtype,
                "model": self.model,
                "provider": self.provider,
                "event": "operation_start"
            }
        )

    def _log_operation_end(self, operation_type: str, operation_subtype: str = "general",
                          success: bool = True, input_tokens: int = 0, output_tokens: int = 0) -> None:
        """
        Log the end of an LLM operation with results.

        Args:
            operation_type: Type of operation that was performed
            operation_subtype: Subtype of operation
            success: Whether the operation was successful
            input_tokens: Number of input tokens used
            output_tokens: Number of output tokens generated
        """
        user_id, project_id = _get_context_or_fallback()

        status_text = "SUCCESS" if success else "FAILED"

        end_message = f"""
LLM OPERATION {status_text}
{'-' * 50}
Operation: {operation_type}
Subtype: {operation_subtype}
Model: {self.model}
Input Tokens: {input_tokens:,}
Output Tokens: {output_tokens:,}
Total Tokens: {input_tokens + output_tokens:,}
{'-' * 50}"""

        log_level = LoggingHelper.info if success else LoggingHelper.error
        log_level(
            end_message,
            user_id=user_id,
            project_id=project_id,
            extra_data={
                "operation_type": operation_type,
                "operation_subtype": operation_subtype,
                "model": self.model,
                "provider": self.provider,
                "success": success,
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "total_tokens": input_tokens + output_tokens,
                "event": "operation_end"
            }
        )


def resolve_model_config(model_name: str) -> tuple[str, str]:
    """
    Resolve model name to provider and actual model name.

    Args:
        model_name: Model name from database or config

    Returns:
        Tuple of (provider, actual_model_name)
    """
    # Model mapping for backward compatibility
    model_mapping = {
        # OpenAI models
        "gpt-4o": (LLMProvider.OPENAI, "gpt-4o"),
        "gpt-4o-mini": (LLMProvider.OPENAI, "gpt-4o-mini"),
        "gpt-4o-2024-08-06": (LLMProvider.OPENAI, "gpt-4o-2024-08-06"),
        "gpt-3.5-turbo": (LLMProvider.OPENAI, "gpt-3.5-turbo"),

        # DeepSeek models
        "deepseek-chat": (LLMProvider.DEEPSEEK, "deepseek-chat"),
        "deepseek-reasoner": (LLMProvider.DEEPSEEK, "deepseek-reasoner"),

        # Gemini models
        "gemini-2.0-flash-lite": (LLMProvider.GEMINI, "gemini-2.0-flash-lite"),
        "gemini-2.5-pro-preview-06-05": (LLMProvider.GEMINI, "gemini-2.5-pro-preview-06-05"),
        "gemini-1.5-flash": (LLMProvider.GEMINI, "gemini-1.5-flash"),
    }

    if model_name in model_mapping:
        return model_mapping[model_name]

    # Default to OpenAI if model not found in mapping
    user_id, project_id = _get_context_or_fallback()
    LoggingHelper.warning("Model not found in mapping, defaulting to OpenAI", user_id=user_id, project_id=project_id, extra_data={"model_name": model_name})
    return LLMProvider.OPENAI, model_name


def get_model_context_window(model_name: str) -> int:
    """
    Get the context window size for a given model.

    Args:
        model_name: Model name from database or config

    Returns:
        Context window size in tokens
    """
    # Model context window mapping
    context_window_mapping = {
        # OpenAI models
        "gpt-4o": config.MODEL_GPT4O_CONTEXT_WINDOW,
        "gpt-4o-mini": config.MODEL_GPT4O_MINI_CONTEXT_WINDOW,
        "gpt-4o-2024-08-06": config.MODEL_GPT4O_2024_CONTEXT_WINDOW,
        "gpt-3.5-turbo": config.MODEL_GPT35_TURBO_CONTEXT_WINDOW,

        # DeepSeek models
        "deepseek-chat": config.MODEL_DEEPSEEK_CHAT_CONTEXT_WINDOW,
        "deepseek-reasoner": config.MODEL_DEEPSEEK_REASONER_CONTEXT_WINDOW,

        # Gemini models
        "gemini-2.0-flash-lite": config.MODEL_GEMINI_2_0_FLASH_LITE_CONTEXT_WINDOW,
        "gemini-2.5-pro-preview-06-05": config.MODEL_GEMINI_2_5_PRO_PREVIEW_CONTEXT_WINDOW,
        "gemini-1.5-flash": config.MODEL_GEMINI_1_5_FLASH_CONTEXT_WINDOW,
    }

    if model_name in context_window_mapping:
        return context_window_mapping[model_name]

    # Default to a conservative context window if model not found
    user_id, project_id = _get_context_or_fallback()
    LoggingHelper.warning("Context window for model not found, defaulting to 4000 tokens", user_id=user_id, project_id=project_id, extra_data={"model_name": model_name})
    return 4000


def get_assistant_model(requested_model: str) -> str:
    """
    Get the appropriate model for OpenAI Assistants API.
    Always returns an OpenAI model, regardless of the requested model.

    Args:
        requested_model: The model requested by the project

    Returns:
        OpenAI model name suitable for Assistants API
    """
    # Check if the requested model is already an OpenAI model
    provider, _ = resolve_model_config(requested_model)

    if provider == LLMProvider.OPENAI:
        # If it's already an OpenAI model, use it
        return requested_model
    else:
        # If it's not an OpenAI model (e.g., DeepSeek), use default assistant model
        import config
        user_id, project_id = _get_context_or_fallback()
        LoggingHelper.info("Requested model not compatible with OpenAI Assistants API, using default", user_id=user_id, project_id=project_id, extra_data={"requested_model": requested_model, "default_assistant_model": config.DEFAULT_ASSISTANT_MODEL})
        return config.DEFAULT_ASSISTANT_MODEL


def validate_context_window(model_name: str, estimated_tokens: int) -> bool:
    """
    Validate if the estimated token count fits within the model's context window.

    Args:
        model_name: Model name to check
        estimated_tokens: Estimated number of tokens in the input

    Returns:
        True if tokens fit within context window, False otherwise
    """
    context_window = get_model_context_window(model_name)
    return estimated_tokens <= context_window


def get_provider_economy_model(project_model: str) -> str:
    """
    Get the economy model for the same provider as the project model.

    Args:
        project_model: The main model selected for the project

    Returns:
        Economy model name from the same provider
    """
    provider, _ = resolve_model_config(project_model)

    if provider in config.PROVIDER_MODEL_TIERS:
        return config.PROVIDER_MODEL_TIERS[provider]["economy"]

    # Fallback to the project model itself if provider not found
    user_id, project_id = _get_context_or_fallback()
    LoggingHelper.warning("Provider not found in model tiers, using project model", user_id=user_id, project_id=project_id, extra_data={"provider": provider, "project_model": project_model})
    return project_model


def get_provider_vision_model(project_model: str) -> str:
    """
    Get the vision model for the same provider as the project model.

    Args:
        project_model: The main model selected for the project

    Returns:
        Vision model name from the same provider (or fallback)
    """
    provider, _ = resolve_model_config(project_model)

    if provider in config.PROVIDER_MODEL_TIERS:
        return config.PROVIDER_MODEL_TIERS[provider]["vision"]

    # Fallback to default vision model if provider not found
    user_id, project_id = _get_context_or_fallback()
    LoggingHelper.warning("Provider not found in model tiers, using default vision model", user_id=user_id, project_id=project_id, extra_data={"provider": provider, "default_vision_model": config.DEFAULT_VISION_MODEL})
    return config.DEFAULT_VISION_MODEL


def get_model_tier(model_name: str) -> str:
    """
    Determine the tier (premium, economy, vision) of a given model.

    Args:
        model_name: Model name to check

    Returns:
        Model tier: "premium", "economy", "vision", or "unknown"
    """
    for tiers in config.PROVIDER_MODEL_TIERS.values():
        # Check economy first (more specific)
        if model_name == tiers["economy"]:
            return "economy"
        # Check vision (more specific)
        elif model_name == tiers["vision"]:
            return "vision"
        # Check premium (broader category)
        elif model_name in tiers["premium"]:
            return "premium"

    return "unknown"


async def create_llm_service(model_name: str, **kwargs) -> UnifiedLLMService:
    """
    Factory function to create LLM service instance.

    Args:
        model_name: Model name to resolve
        **kwargs: Additional parameters

    Returns:
        UnifiedLLMService instance
    """
    provider, actual_model = resolve_model_config(model_name)
    return UnifiedLLMService(provider=provider, model=actual_model, **kwargs)
