#!/usr/bin/env python3
"""
Debug script to check API key configuration.
"""

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config.api as config

def debug_api_keys():
    """Debug API key configuration"""
    
    print("🔍 API Key Configuration Debug")
    print("=" * 50)
    
    print(f"USE_HARDCODED_KEYS: {config.USE_HARDCODED_KEYS}")
    print()
    
    print("Hardcoded Keys:")
    print(f"  HARDCODED_OPENAI_API_KEY: {config.HARDCODED_OPENAI_API_KEY[:10]}..." if hasattr(config, 'HARDCODED_OPENAI_API_KEY') and config.HARDCODED_OPENAI_API_KEY else "  HARDCODED_OPENAI_API_KEY: NOT SET")
    print(f"  HARDCODED_DEEPSEEK_API_KEY: {config.HARDCODED_DEEPSEEK_API_KEY[:10]}..." if hasattr(config, 'HARDCODED_DEEPSEEK_API_KEY') and config.HARDCODED_DEEPSEEK_API_KEY else "  HARDCODED_DEEPSEEK_API_KEY: NOT SET")
    print(f"  HARDCODED_GOOGLE_API_KEY: {config.HARDCODED_GOOGLE_API_KEY[:10]}..." if hasattr(config, 'HARDCODED_GOOGLE_API_KEY') and config.HARDCODED_GOOGLE_API_KEY else "  HARDCODED_GOOGLE_API_KEY: NOT SET")
    print(f"  HARDCODED_GEMINI_API_KEY: {config.HARDCODED_GEMINI_API_KEY[:10]}..." if hasattr(config, 'HARDCODED_GEMINI_API_KEY') and config.HARDCODED_GEMINI_API_KEY else "  HARDCODED_GEMINI_API_KEY: NOT SET")
    print()
    
    print("Final API Keys:")
    print(f"  openai_api_key: {config.openai_api_key[:10]}..." if hasattr(config, 'openai_api_key') and config.openai_api_key else "  openai_api_key: NOT SET")
    print(f"  deepseek_api_key: {config.deepseek_api_key[:10]}..." if hasattr(config, 'deepseek_api_key') and config.deepseek_api_key else "  deepseek_api_key: NOT SET")
    print(f"  google_api_key: {config.google_api_key[:10]}..." if hasattr(config, 'google_api_key') and config.google_api_key else "  google_api_key: NOT SET")
    print(f"  gemini_api_key: {config.gemini_api_key[:10]}..." if hasattr(config, 'gemini_api_key') and config.gemini_api_key else "  gemini_api_key: NOT SET")
    print()
    
    print("Key Availability Check:")
    print(f"  hasattr(config, 'gemini_api_key'): {hasattr(config, 'gemini_api_key')}")
    print(f"  config.gemini_api_key is not None: {hasattr(config, 'gemini_api_key') and config.gemini_api_key is not None}")
    print(f"  len(config.gemini_api_key): {len(config.gemini_api_key) if hasattr(config, 'gemini_api_key') and config.gemini_api_key else 'N/A'}")
    
    # Test direct access
    try:
        gemini_key = config.gemini_api_key
        print(f"  Direct access successful: {gemini_key[:10]}...")
    except AttributeError as e:
        print(f"  Direct access failed: {e}")
    
    print()
    print("Environment Variables:")
    print(f"  GEMINI_API_KEY: {os.getenv('GEMINI_API_KEY', 'NOT SET')}")
    print(f"  GOOGLE_API_KEY: {os.getenv('GOOGLE_API_KEY', 'NOT SET')}")

if __name__ == "__main__":
    debug_api_keys()
